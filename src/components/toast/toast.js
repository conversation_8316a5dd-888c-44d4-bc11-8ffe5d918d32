import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { TOAST_CONTENT_PROPERTIES } from 'src/constants';
const Toast = ({ title, content, position, lifeTime, type }) => {
  const { className, icon } = TOAST_CONTENT_PROPERTIES.filter(
    (toast) => toast.type === type
  )[0];
  const [isMounted, setIsMounted] = useState(true);
  useEffect(() => {
    const timeId = setTimeout(() => {
      setIsMounted(false);
    }, lifeTime);

    return () => {
      clearTimeout(timeId);
    };
  }, [lifeTime]);
  const close = () => setIsMounted(false);
  return isMounted
    ? createPortal(
        <div
          className={`p-toast p-component ${position}`}
          style={{ zIndex: 1201 }}
        >
          <div>
            <div
              className={`p-toast-message ${className} p-toast-message-enter-done`}
              role="alert"
              aria-live="assertive"
              aria-atomic="true"
            >
              <div className="p-toast-message-content">
                <span className={`p-toast-message-icon pi ${icon}`} />

                <div className="p-toast-message-text">
                  <span className="p-toast-summary">{title}</span>
                  <div className="p-toast-detail">{content}</div>
                </div>
                <button
                  type="button"
                  className="p-toast-icon-close p-link"
                  aria-label="Close"
                  onClick={close}
                >
                  <span
                    className="p-toast-icon-close-icon pi pi-times"
                    aria-hidden="true"
                  ></span>
                  <span role="presentation" className="p-ink"></span>
                </button>
              </div>
            </div>
          </div>
        </div>,
        document.getElementById('toast')
      )
    : null;
};
toast.defaultProps = {
  lifeTime: 3000,
  title: 'Toast Title',
  content: 'Toast content',
  type: 'success',
  position: 'p-toast-top-right',
};
export default Toast;
