import DataGrid, {
  Selection,
  Paging,
  FilterRow,
  Scrolling,
  Column,
} from 'devextreme-react/data-grid';

const DataGridBox = ({
  gridDataSource,
  columnField,
  columnCaption,
  setGridBoxValue,
  id,
}) => {
  const dataGridOnSelectionChanged = (e) => {
    e.component.getSelectedRowsData().then((data) => {
      const selectedRow = data.map((row) => row.userId);
      setGridBoxValue(selectedRow, id);
    });
  };


  return (
    <>
      <DataGrid
        dataSource={gridDataSource}
        hoverStateEnabled={true}
        keyExpr="userId"
        onSelectionChanged={dataGridOnSelectionChanged}
        height={250}
        className="mb-3"
      >
        <Column dataField={columnField} caption={columnCaption} />
        <Selection
          mode="multiple"
          deferred={true}
          showCheckBoxesMode="always"
        />
        <Scrolling mode="virtual" />
        <Paging enabled={true} pageSize={10} />
        <FilterRow visible={true} />
      </DataGrid>
      <style global jsx>{`
        .dx-datagrid-rowsview
          .dx-select-checkboxes-hidden
          > tbody
          > tr
          > td
          > .dx-select-checkbox {
          display: inline-block;
        }
      `}</style>
    </>
  );
};

export default DataGridBox;
