import DropDownBox from 'devextreme-react/drop-down-box';
import { TextArea } from 'devextreme-react';
import DataGrid, {
  Selection,
  Paging,
  FilterRow,
  Scrolling,
} from 'devextreme-react/data-grid';
import { useCallback } from 'react';

const GridBox = ({
  gridDataSource,
  gridColumns,
  customState,
  boxName,
  gridBoxValue,
  setGridBoxValue,
  dataKey,
  dataText,
  defaultSelectionFilter,
  height,
  width,
}) => {
  const dataGridOnSelectionChanged = useCallback((e) => {
    e.component.getSelectedRowsData().then((data) => {
      const selectedRow = data.map((row) => row);
      setGridBoxValue(selectedRow);
    });
  }, []);

  const dataGridRender = useCallback(() => {
    return (
      <DataGrid
        dataSource={gridDataSource}
        columns={gridColumns}
        hoverStateEnabled={true}
        onSelectionChanged={dataGridOnSelectionChanged}
        height={400}
        keyExpr={dataKey}
        defaultSelectionFilter={defaultSelectionFilter}
      >
        <Selection
          mode="multiple"
          deferred={true}
          allowSelectAll={false}
          showCheckBoxesMode="always"
        />
        <Scrolling mode="virtual" />
        <Paging enabled={true} pageSize={10} />
        <FilterRow visible={true} />
      </DataGrid>
    );
  }, [gridDataSource, customState]);
  const dropdownOptions = { minWidth: width };
  return (
    <>
      <DropDownBox
        deferRendering={false}
        placeholder={boxName}
        showClearButton={true}
        dataSource={gridDataSource}
        contentRender={dataGridRender}
        dropDownOptions={dropdownOptions}
      />
      <TextArea
        height={height}
        value={gridBoxValue.map((item) => item[dataText]).toString()}
        readOnly={true}
      />
      <style global jsx>
        {`
          .dx-dropdowneditor-overlay .dx-popup-flex-height {
            max-height: 400px !important;
          }
        `}
      </style>
    </>
  );
};

GridBox.defaultProps = {
  dataKey: 'id',
  dataText: 'name',
  defaultSelectionFilter: [],
  height: 100,
  // width: '500px',
};
export default GridBox;
