import Link from 'next/link';
import { useRouter } from 'next/router';
import $ from 'jquery';
import { Dropdown } from 'primereact/dropdown';
import nextConfig from 'next.config';
import {
  helpDropdownItems,
  createDropDownItems,
  LOGO,
  BANNER_IMAGE,
} from 'src/constants';
import { getCustomValBasedOnLabel } from 'src/utils';
import { useDefaults } from '@contexts/defaults';
import styles from './index.module.css';
import { useEffect } from 'react';
import { useMenu } from '../../contexts/menu';

const LIST_HIDE_CREATE_HELP_DROPDOWN = /quote-report/;

const PageHeader = ({ header, isBrandLogoReq }) => {
  const { pathname } = useRouter();
  const { user } = useMenu();
  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();
  const { basePath } = nextConfig;
  const CRM_HEADER_LOGO =
    process.env.NODE_ENV === 'development' ? `${basePath}/twdev.png` : LOGO;
  const CRM_BANNER_IMAGE =
    process.env.NODE_ENV === 'development'
      ? `${basePath}/images/header_bg.png`
      : BANNER_IMAGE;
  const pageHeaderImage = {
    height: '100px',
    background: `url(${CRM_BANNER_IMAGE}) right center no-repeat #fff`,
    fontWeight: '600',
    fontSize: '1.25rem',
  };
  const pageHeaderStyle = isBrandLogoReq
    ? pageHeaderImage
    : {
        backgroundColor: 'var(--surface-50)',
        boxShadow: '#000000 0px 1px 1px 0px ',
      };
  const headerClassName = isBrandLogoReq
    ? styles.page_header__main_header
    : 'text-color pl-1';
  const itemTemplate = (item) => {
    return (
      <a
        href={item.link}
        target={item.target}
        id={item.id}
        onClick={item.onClick}
      >
        {item.label}
      </a>
    );
  };
  useEffect(() => {
    const script = document.createElement('script');

    script.src =
      'https://repfabric.atlassian.net/s/d41d8cd98f00b204e9800998ecf8427e-T/-x8fh9u/b/3/06e9607e63881b2465acb87c82a44c6d/_/download/batch/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector.js?collectorId=4cdecc56';
    script.async = true;
    document.body.appendChild(script);

    window.ATL_JQ_PAGE_PROPS = {
      triggerFunction: (showCollectorDialog) => {
        $(document).on('click', '#myCustomTrigger', (e) => {
          e.preventDefault();
          showCollectorDialog();
        });
      },
      fieldValues: {
        //  eslint-disable-next-line camelcase
        customfield_10028: `${user.domain}`,
        fullname: `${user.userName}`,
        email: `${user.userLogin}`,
      },
    };

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  const createDropDownItemList = createDropDownItems.map((item) => {
    // Get Custom labels for Activity Journal and Opportunity
    const [customActJnlLabel, customOppLabel] = getCustomValBasedOnLabel(
      CUSTOM_LABELS,
      ['IDS_ACT_JOURNAL', 'IDS_OPP']
    );
    // Get Dynamic Email URl and change the link
    switch (item.value) {
      case 'email':
        return { ...item, link: `/${user.emailUrl}` };
      case 'activity_journal':
        return {
          ...item,
          label: customActJnlLabel,
        };
      case 'opportunity':
        return {
          ...item,
          label: customOppLabel,
        };
      default:
        return item;
    }
  });
  return (
    <div
      style={pageHeaderStyle}
      className="grid card_box p-0 m-0 text-lg w-full"
    >
      <div className="col-12 p-0 flex justify-content-between  align-items-center">
        <div className="col-10 p-0 flex align-items-center">
          {isBrandLogoReq && (
            <div className="flex ">
              <Link href="/" passHref>
                <img
                  src={CRM_HEADER_LOGO}
                  alt="Repfabric"
                  className={styles.page_header__logo}
                />
              </Link>
            </div>
          )}
          <div className="flex align-items-center m-0 p-0 font-semibold">
            <a className={headerClassName}>{header}</a>
          </div>
        </div>
        <div
          className="col-2  flex justify-content-end mt-auto md:mt-0"
          style={{
            visibility: LIST_HIDE_CREATE_HELP_DROPDOWN.test(pathname)
              ? 'hidden'
              : 'visible',
          }}
        >
          <Dropdown
            options={helpDropdownItems(pathname)}
            itemTemplate={itemTemplate}
            placeholder="Help"
            className="helper_dropdown mr-3 "
            panelClassName="text-sm w-10rem"
          />
          <Dropdown
            options={createDropDownItemList}
            itemTemplate={itemTemplate}
            className="helper_dropdown mr-4"
            placeholder="Create"
            panelClassName="text-sm w-10rem "
            scrollHeight="250px"
          />
        </div>
      </div>
      <style global jsx>{`
        .p-dropdown-panel .p-dropdown-items .p-dropdown-item {
          padding: 0;
        }
        .p-dropdown-panel .p-dropdown-items .p-dropdown-item a {
          white-space: pre-wrap;
          color: #000;
          display: block;
          padding: 0.5rem;
        }
      `}</style>
    </div>
  );
};

export default PageHeader;
