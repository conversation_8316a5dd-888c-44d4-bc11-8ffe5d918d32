.custom_popup.is_active {
  opacity: 1;
  pointer-events: auto;
}
.custom_popup {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1500;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 110.145ms 0ms;
}
.custom_popup.is_active .custom_popup__holder {
  opacity: 1;
  transform: translateY(0);
}
@media screen and (max-width: 980px) {
  .custom_popup__holder {
    width: 90%;
    height: 90vh;
    padding: 25px 15px;
  }
  .custom_popup__close {
    top: 5px;
    right: 5px;
  }
  .custom_popup__title {
    font-size: 14px;
    width: 100%;
  }
  .custom_popup__content {
    font-size: 12px;
    width: 100%;
    overflow-y: auto;
    padding: 0 0 20px;
    margin-bottom: 20px;
  }
  .custom_popup__footer {
    font-size: 14px;
    line-height: 16px;
    width: 100%;
  }
}

.custom_popup__content {
  flex: 1;
  overflow-y: auto;
  width: 100%;
  padding: 0px 25px;
  margin-bottom: 5px;
}
.custom_popup__footer {
  padding: 0px 25px 25px 25px;
}
.custom_popup__title {
  font-size: 17px;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  padding: 15px 0 15px 20px;
  background-color: #c4d9f6 !important;
  margin-bottom: 5px;
}
.custom_popup__holder {
  max-width: 400px;
  width: 100%;
  min-height: fit-content;
  max-height: 50%;
  background: #fff;
  /* padding: 25px; */
  display: flex;
  flex-flow: column wrap;
  justify-content: space-between;
  position: relative;
  opacity: 0;
  transform: translateY(-100px);
  transition: opacity 400ms, transform 400ms;
  transition-delay: 300ms;
}
.custom_popup__fullscreen {
  max-width: 100%;
  height: 100%;
}

.custom_popup__close {
  position: absolute;
  top: 15px;
  right: 20px;
  width: 30px;
  height: 30px;
  cursor: pointer;
  transition: 0.3s transform;
}
.custom_popup__expand {
  position: absolute;
  top: 25px;
  right: 60px;
  width: 14px;
  height: 14px;
  margin: 2px;
  cursor: pointer;
  box-shadow: -6px 6px 0 -4px, 6px -6px 0 -4px;
}
.custom_popup__expand::before {
  content: '';
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 2px;
  height: 22px;
  top: -4px;
  left: 6px;
  transform: rotate(45deg);
  border-top: 9px solid;
  border-bottom: 9px solid;
}
.custom_popup__collapse {
  position: absolute;
  top: 22px;
  right: 65px;
  box-sizing: border-box;
  transform: rotate(45deg);
  display: block;
  width: 2px;
  cursor: pointer;
  height: 26px;
  border-top: 10px solid;
  border-bottom: 10px solid;
}
.custom_popup__collapse::after,
.custom_popup__collapse::before {
  content: '';
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 6px;
  height: 6px;
  transform: rotate(45deg);
  left: -2px;
}
.custom_popup__collapse::after {
  border-bottom: 2px solid;
  border-right: 2px solid;
  top: -5px;
}
.custom_popup__collapse::before {
  border-top: 2px solid;
  border-left: 2px solid;
  bottom: -5px;
}
.custom_popup__close:before,
.custom_popup__close:after {
  content: '';
  position: absolute;
  left: 15px;
  top: 3px;
  display: block;
  width: 2px;
  height: 20px;
  background: #000;
  transform: rotate(45deg);
}
.custom_popup__close::after {
  left: 15px;
  top: 3px;
  transform: rotate(-45deg);
}
