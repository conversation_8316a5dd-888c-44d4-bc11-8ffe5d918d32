/** React JS and Next Js imports */
import { useState, useEffect, useRef } from 'react';

/** Primereact imports */
import { PickList } from 'primereact/picklist';
import { Toast } from 'primereact/toast';

/** Custom Imports */
import { MenuService } from '@services/MenuService';
import ButtonComp from '@components/ui/ButtonComp';
import { useMenu } from '@contexts/menu';
import Modal from '@components/modal';

const MenuPicklist = ({ displayBasic, onHide }) => {
  const [source, setSource] = useState([]);
  const [target, setTarget] = useState([]);
  const toast = useRef(null);
  const { updateFlatMenus } = useMenu();
  const menuService = new MenuService();
  const itemTemplate = (item) => {
    return (
      <div className="product-item">
        <b>{item.name}</b>
      </div>
    );
  };
  const onChange = (event) => {
    setSource(event.source);
    setTarget(event.target);
  };
  useEffect(() => {
    if (!displayBasic) {
      const getData = async () => {
        const getMenus = await menuService.getAllMenus().then((res) => {
          if (res.status === 200) {
            return res.data;
          }
        });

        const { userMenus } = getMenus;
        const allAvailableMenus = userMenus.map((menu) => {
          const menuURLNew = `/${menu.url}`;
          const menuURL = `${menu.url.startsWith('/') ? menu.url : menuURLNew}`;
          return {
            id: menu.id,
            name: menu.menu,
            url: `/RepfabricCRM${menuURL}`,
            isFlatMenu: menu.isFlatMenu,
            sequence: menu.sequence,
          };
        });
        const selectedMenus = allAvailableMenus
          .filter((item) => item.isFlatMenu === 1)
          .sort((a, b) => a.sequence - b.sequence);

        const allMenus = allAvailableMenus.filter(
          (item) => item.isFlatMenu === 0
        );

        setSource(allMenus);
        setTarget(selectedMenus);
      };
      getData();
    }
  }, [displayBasic]);
  const saveMenu = async () => {
    await menuService
      .updateMenuDetails(
        target.map((item, index) => {
          return {
            id: item.id,
            sequence: index,
          };
        })
      )
      .then((res) => {
        if (res.response.status) {
          toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Menu updated Successfully',
            life: 1000,
          });
          onHide();
        }
      });
  };
  const onSourceSelectionChange = (e) => {
    if (e.event?.detail === 2) {
      const element = e.value[0];
      setSource((prev) => prev.filter((item) => item.id !== element.id));
      setTarget((prev) => [...prev, element]);
    }
  };
  const onTargetSelectionChange = (e) => {
    if (e.event?.detail === 2) {
      const element = e.value[0];
      setTarget((prev) => prev.filter((item) => item.id !== element.id));
      setSource((prev) => [...prev, element]);
    }
  };
  return (
    <>
      <Toast
        ref={toast}
        onHide={() => {
          updateFlatMenus(
            target.map((item) => {
              return {
                label: item.name,
                url: item.url,
                className: 'text-sm px-2',
                id: item.id,
              };
            })
          );
        }}
      />
      {displayBasic && (
        <Modal setOpen={onHide} title="Edit Shortcut Menus" autoFocus={false}>
          <div className="absolute">
            <ButtonComp
              icon=" pi-save"
              tooltip="Save"
              className="relative p-button-sm p-button-success"
              onClick={saveMenu}
              style={{ top: '-48px', left: '600px' }}
            />
          </div>
          <PickList
            source={source}
            target={target}
            itemTemplate={itemTemplate}
            sourceHeader="Menus"
            targetHeader="Selected Menus"
            sourceStyle={{ minHeight: '30rem' }}
            targetStyle={{ minHeight: '30rem' }}
            onSourceSelectionChange={onSourceSelectionChange}
            onTargetSelectionChange={onTargetSelectionChange}
            onChange={onChange}
          />
        </Modal>
      )}
    </>
  );
};

export default MenuPicklist;
