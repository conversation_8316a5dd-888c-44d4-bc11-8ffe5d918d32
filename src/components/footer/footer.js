import { useDefaults } from '@contexts/defaults';
import { useRouter } from 'next/router';
import styles from './footer.module.css';
const Footer = () => {
  const router = useRouter();
  const {
    defaults: { VERSION, BRAND_TYPE },
  } = useDefaults();
  const {
    fabricClientName: TITLE_NAME,
    fabricClientType: FOOTER_LOGO_PATH,
    fabricClientUrl: FOOTER_URL,
  } = BRAND_TYPE;
  const isIndexPage = router.pathname === '/';
  return (
    <footer
      className={`${styles.footer_body} ${
        isIndexPage ? 'surface-800' : 'bg-white'
      } `}
    >
      <a target="_blank" href={`https://${FOOTER_URL}`} rel="noreferrer">
        Powered by
        {FOOTER_LOGO_PATH && (
          <img
            className="ml-1 max-w-11rem"
            src={`/RepfabricCRM/javax.faces.resource/${FOOTER_LOGO_PATH}/brand_logo.png.xhtml?ln=images`}
            alt={TITLE_NAME}
          />
        )}
      </a>
      <div>
        <i className={`${isIndexPage ? 'text-400' : 'hidden'}`}>{VERSION}</i>
      </div>
    </footer>
  );
};
export default Footer;
