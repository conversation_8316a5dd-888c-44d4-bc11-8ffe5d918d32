/** Next JS and React imports */
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Link from 'next/link';

/** Primereact Imports */
import { TieredMenu } from 'primereact/tieredmenu';

/** Custom Imports */
import ButtonComp from '@components/ui/ButtonComp';
import SmallModal from '@components/small-modal';
import { Button, SelectBox } from 'devextreme-react';
import { getDeligationUsers } from '@services/api';
import { getTodaysDate } from 'src/utils';
import { useMenu } from '@contexts/menu';

const HeaderLeft = ({ basePath, verticalMenus }) => {
  const { user } = useMenu();
  const sideMenu = useRef(null);
  const [delegationUserList, setDelegationUserList] = useState([]);
  const [isDelgnPopupVisible, setIsDelgnPopupVisible] = useState(false);
  const [selectedDelgnUser, setSelectedDelgnUser] = useState(user.userId);
  const [emailUrl, setEmailUrl] = useState('');
  useEffect(() => {
    const fetchdelegationUsers = async () => {
      try {
        const delegationUsers = await (
          await getDeligationUsers(getTodaysDate().format('MM-DD-YYYY'))
        ).data;
        setDelegationUserList(delegationUsers);
      } catch (error) {
        throw new Error(error);
      }
    };
    fetchdelegationUsers();
  }, []);
  const verticalMenuList = useMemo(
    () =>
      verticalMenus.map((menu) => {
        if (
          menu.label.toLowerCase() === 'email' &&
          delegationUserList.length > 1
        ) {
          const { url, ..._menu } = menu;
          setEmailUrl(url);
          return {
            ..._menu,
            command: () => {
              setIsDelgnPopupVisible(true);
            },
          };
        }
        return menu;
      }),
    [verticalMenus]
  );
  const handleCancelClick = () => {
    setIsDelgnPopupVisible(false);
  };
  const handleOpenClick = () => {
    const modifiedEmailUrl = emailUrl.split('&');
    // 11 - reqId=<5 digits>
    const changeReqId = `${modifiedEmailUrl[1].substring(
      0,
      11
    )}${selectedDelgnUser}${modifiedEmailUrl[1].substring(
      modifiedEmailUrl[1].length - 1
    )}`;
    window.location.href = [
      modifiedEmailUrl[0],
      changeReqId,
      modifiedEmailUrl[2],
    ].join('&');
  };
  const DelegationPopupFooter = (
    <div className="flex">
      <div className="flex-initial flex m-1">
        <Button type="success" text="Open" onClick={handleOpenClick} />
      </div>
      <div className="flex-initial flex m-1">
        <Button
          className="bg-yellow-600 text-0"
          text="Cancel"
          onClick={handleCancelClick}
        />
      </div>
    </div>
  );
  const onDelgnUserChanged = useCallback((e) => {
    setSelectedDelgnUser(e.value);
  }, []);
  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Link href="/" passHref>
        <img
          alt="logo"
          src={`${basePath}/logo.png`}
          height="30"
          width="30"
          className="w-2rem cursor-pointer"
        />
      </Link>
      <div className="absolute">
        <TieredMenu
          model={verticalMenuList}
          popup
          ref={sideMenu}
          id="overlay_tmenu"
          className="border-none fixed block"
          appendTo="self"
        />
      </div>
      <ButtonComp
        icon="pi-bars"
        className="p-button-lg p-button-link mx-0 menu_button text-white"
        onClick={(event) => sideMenu.current.toggle(event)}
        aria-haspopup
        aria-controls="overlay_tmenu"
        tooltip="All Menus"
      />
      {isDelgnPopupVisible && (
        <SmallModal
          title="Delegations"
          footer={DelegationPopupFooter}
          setOpen={setIsDelgnPopupVisible}
        >
          <div className="dx-fieldset mx-0 ">
            <div className="dx-field">
              <div className="dx-field-label font-bold w-6 text-color">
                Open E-mail of
              </div>
              <div className="dx-field-value w-6">
                <SelectBox
                  dataSource={delegationUserList}
                  displayExpr="userName"
                  valueExpr="userId"
                  value={selectedDelgnUser}
                  onValueChanged={onDelgnUserChanged}
                />
              </div>
            </div>
          </div>
        </SmallModal>
      )}
    </div>
  );
};

export default HeaderLeft;
