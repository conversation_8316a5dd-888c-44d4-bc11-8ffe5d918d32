/** Custom Imports */
import nextConfig from 'next.config';
import { useMenu } from '@contexts/menu';
import HeaderLeft from './header-left/header-left';
import HeaderRight from './header-right/header-right';
import Nav from '@components/nav';
import { Toast } from 'primereact/toast';
import { useRef } from 'react';
const Header = () => {
  const toastRef = useRef(null);

  const { basePath } = nextConfig;
  const { menus, user } = useMenu();
  const { flatMenus, sideMenus } = menus;

  return (
    <>
      <Toast ref={toastRef} baseZIndex="100000" />
      <div
        className=" header sticky w-full top-0 py-2 bg-primary-500 "
        style={{ zIndex: '1000' }}
      >
        <div className="p-menubar top-0 bg-primary-500 border-none menu_list shortcut_button">
          <div className="p-menubar-start">
            <HeaderLeft basePath={basePath} verticalMenus={sideMenus} />
          </div>
          {flatMenus.length > 0 && <Nav flatMenus={flatMenus} />}
          <div className="p-menubar-end">
            <HeaderRight user={user} ref={toastRef} />
          </div>
        </div>
      </div>
    </>
  );
};
export default Header;
