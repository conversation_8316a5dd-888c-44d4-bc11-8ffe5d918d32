/**React and Next JS imports */
import { useState, useRef, forwardRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';

/** PrimeReact imports */
import { Avatar } from 'primereact/avatar';
import { OverlayPanel } from 'primereact/overlaypanel';

/**Custom Imports */
import ButtonComp from '@components/ui/ButtonComp';
import { useAuth } from '@contexts/auth';
import MenuPicklist from '@components/menu-pick-list';
import { basePath } from 'next.config';
import { useDefaults } from '@contexts/defaults';
import SmallModal from '@components/small-modal';
import UserPwdPopupBody from './change-user-pwd-body';
import {
  changeUserPassword,
  getSuperUserLoginList,
  putUpdateUserLastSid,
} from '@services/api';
import { useMenu } from '@contexts/menu';
// eslint-disable-next-line camelcase
import { read_cookie } from 'sfcookies';
import Modal from '@components/modal';
import DataGrid, {
  Button,
  Column,
  Editing,
  FilterRow,
  Scrolling,
} from 'devextreme-react/data-grid';
import ConfirmSuperLoginPopupBody from './confirm-super-login-popup-body';
const cookieKey = 'namedOFCookie';

const getUserLastSidBySelected = (selected) => (selected ? 1 : -1);
const HeaderRight = (props, ref) => {
  const router = useRouter();
  const {
    defaults: { EMAIL_ERR_MSG, BRAND_TYPE },
  } = useDefaults();
  const { fabricClientName: TITLE_NAME } = BRAND_TYPE;
  const { user, updateUserLastSid } = useMenu();
  const [displayBasic, setDisplayBasic] = useState(false);
  const [showUserPwd, setShowUserPwd] = useState(false);
  const [superUserPopup, setSuperUserPopup] = useState(false);
  const [confirmSuperLoginPoup, setConfirmSuperLoginPoup] = useState(false);
  const [superUserloginList, setSuperUserLoginList] = useState([]);
  const [selectedSuperUser, setSelectedSuperUser] = useState({});
  const op = useRef(null);
  const { logout } = useAuth();
  const DISPLAY_SUPER_USER_BTN =
    !!user.isSuperUser || user.userName.match(/^Admin$/);
  useEffect(() => {
    //Show Change Password only for userLastSid==-1 and page is index
    if (
      user.userLastSid === -1 &&
      router.pathname === '/' &&
      read_cookie(cookieKey).length === 0
    ) {
      setShowUserPwd(true);
    }
  }, [user.userLastSid, router.pathname]);
  useEffect(() => {
    if (user.isSuperUser) {
      const fetchSuperUserLoginList = async () => {
        const superUsers = await (await getSuperUserLoginList()).data;
        setSuperUserLoginList(superUsers);
      };
      fetchSuperUserLoginList();
    }
  }, [user.isSuperUser]);
  const onHide = () => {
    setDisplayBasic(false);
  };

  const onShortcutItemClick = () => {
    setDisplayBasic(true);
  };

  const menuAvatarByErrMsg = `${basePath}/images/photo${
    EMAIL_ERR_MSG ? '1' : ''
  }.png.xhtml`;

  const onUserPwdClicked = () => {
    setShowUserPwd(true);
  };
  const callChangePwd = async (param) => {
    try {
      await changeUserPassword(param)
        .then((response) => {
          if (response.status == 200 || response.status == 201) {
            ref.current.show({
              severity: 'success',
              summary: 'Success',
              detail: 'Password changed',
              life: 3000,
            });
            setShowUserPwd(false);
          }
        })
        .catch(() => {
          ref.current.show({
            severity: 'error',
            summary: 'Warning',
            detail: 'Current password mismatch',
            life: 3000,
          });
        });
    } catch (e) {
      ref.current.show({
        severity: 'error',
        summary: 'Warning',
        detail: 'Please Try after sometime',
        life: 3000,
      });
    }
  };
  const callUpdateUserLastSid = async (selected) => {
    try {
      const param = {
        userLastSid: getUserLastSidBySelected(selected),
      };
      updateUserLastSid(param.userLastSid);
      await putUpdateUserLastSid(user.userId, param);
    } catch (error) {
      ref.current.show({
        severity: 'error',
        summary: 'Warning',
        detail: 'Please Try after sometime',
        life: 3000,
      });
    }
  };
  const handleSuperUserbtnClick = useCallback(() => {
    setSuperUserPopup(true);
  }, []);
  const handleSuperUserLoginClick = useCallback((e) => {
    setConfirmSuperLoginPoup(true);
    setSelectedSuperUser(e.row.key);
  }, []);

  return (
    <div className="flex align-items-center">
      <ButtonComp
        icon=" pi-sliders-h"
        className="p-button-lg p-button-link shortcut_button text-white"
        onClick={onShortcutItemClick}
        tooltip="Edit Shortcut Menus"
      />
      <MenuPicklist displayBasic={displayBasic} onHide={onHide} />

      <ButtonComp
        className="p-button-lg  google p-0 p-button-sm p-button-link shortcut_button mb-1"
        onClick={(e) => op.current.toggle(e)}
      >
        <Avatar
          className="avatar_icon_style"
          style={{ backgroundImage: `url(${menuAvatarByErrMsg})` }}
        />
        <Avatar
          label={user.userName
            ?.split(/\s/)
            .reduce((response, word) => response + word.slice(0, 1), '')
            .toUpperCase()}
          className="avatar_name_style"
        />
      </ButtonComp>

      <OverlayPanel
        ref={op}
        id="overlay_upanel"
        className="p-0"
        style={{ backgroundColor: '#3cabff' }}
        appendTo="self"
      >
        <div className="p-3 ">
          <Avatar className="mt-1 border-noround avatar_icon_style avatar-icon" />
          <p className="text-white  m-0 mb-1" style={{ fontSize: '17px' }}>
            {user.userName}
          </p>
          <p className="text-white">{user.userLogin}</p>
          <br />
          {EMAIL_ERR_MSG && (
            <div className="flex align-items-center text-red-400 font-bold">
              <i className="dx-icon-info mr-1" />
              <div>{EMAIL_ERR_MSG}</div>
            </div>
          )}
          {DISPLAY_SUPER_USER_BTN && (
            <ButtonComp
              type="button"
              className="p-button-warning text-white p-0"
              onClick={handleSuperUserbtnClick}
            >
              <span className="font-medium p-1" style={{ fontSize: '12px' }}>
                Login as another user
              </span>
            </ButtonComp>
          )}
        </div>
        <div className="p-3 flex bg-white">
          <p className="pt-3 pr-2">Change Password</p>
          <ButtonComp
            type="button"
            icon="pi-user"
            className="p-button-warning text-white"
            style={{ margin: '5px' }}
            tooltip="User Password"
            onClick={onUserPwdClicked}
          />
          <ButtonComp
            type="button"
            icon="pi-sign-out"
            style={{ margin: '5px' }}
            className="p-button-text p-button-primary"
            tooltip="Logout"
            onClick={logout}
          />
        </div>
      </OverlayPanel>

      {showUserPwd && (
        <SmallModal
          title={`Change ${TITLE_NAME} Password`}
          setOpen={setShowUserPwd}
        >
          <UserPwdPopupBody
            ref={ref}
            callChangePwd={callChangePwd}
            updateUserLastSid={callUpdateUserLastSid}
          />
        </SmallModal>
      )}
      {superUserPopup && (
        <Modal title="Users" setOpen={setSuperUserPopup}>
          <DataGrid
            dataSource={superUserloginList}
            showBorders={true}
            id="super_login_popup"
            rowAlternationEnabled
            noDataText="No User Name Found"
          >
            <FilterRow visible={true} />
            <Scrolling mode="virtual" showScrollbar="always" />
            <Editing mode="row" useIcons={true} />
            <Column dataField="userName" caption="User Names" />
            <Column type="buttons" name="action">
              <Button
                icon="pi pi-user-plus bg-green-500"
                onClick={handleSuperUserLoginClick}
              />
            </Column>
          </DataGrid>
        </Modal>
      )}
      {confirmSuperLoginPoup && (
        <SmallModal title="Confirmation" setOpen={setConfirmSuperLoginPoup}>
          <ConfirmSuperLoginPopupBody
            user={selectedSuperUser}
            onNoBtnClick={() => setConfirmSuperLoginPoup(false)}
          />
        </SmallModal>
      )}
      <style global jsx>{`
        .avatar-icon {
          background-image: url(${basePath}/images/photo.png.xhtml);
        }
        .avatar_icon_style {
          background-repeat: no-repeat;
          background-size: 23px;
          height: 24px;
          width: 23px;
          float: left;
          border-radius: 0;
        }

        .avatar_name_style {
          height: 1.7rem;
          text-decoration: solid;
          font-weight: 900;
          color: #345fda;
          background-color: #dde6f3;
          border-radius: 0;
        }
        .p-overlaypanel-content {
          padding: 0px !important;
        }
        #super_login_popup > .dx-datagrid > .dx-datagrid-headers {
          position: -webkit-sticky;
          position: sticky;
          background-color: #fff;
          z-index: 1;
          top: 0;
        }
      `}</style>
    </div>
  );
};

export default forwardRef(HeaderRight);
