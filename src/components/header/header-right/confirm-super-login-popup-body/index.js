import { postLoginAsSuperUser, logoutUser } from '@services/api';
import { Button } from 'devextreme-react';
import crypto from 'crypto';
import nProgress from 'nprogress';
import { basePath } from 'next.config';
function emailRandomidGenerator(userId) {
  const firtsFive = crypto.randomBytes(10).toString('hex').substring(0, 5);
  const lastOne = crypto.randomBytes(10).toString('hex').substring(0, 1);
  return `${firtsFive}${userId}${lastOne}`;
}
const ConfirmSuperLoginPopupBody = ({ user, onNoBtnClick }) => {
  const onConfirmSuperLoginClick = async () => {
    try {
      const LOGOUT = await logoutUser();
      if (LOGOUT.status === 200) {
        nProgress.configure({ easing: 'ease', speed: 500 }).start();
        await postLoginAsSuperUser(emailRandomidGenerator(user.userId))
          .then((res) => {
            setTimeout(
              () =>
                window.open(
                  `${window.location.protocol}//${window.location.hostname}/${basePath}`,
                  '_self'
                ),
              5000
            );
          })
          .catch((err) => console.error(err.message));
      }
    } catch (error) {
      console.error(error.message);
    }
  };
  return (
    <>
      <p className="my-3">Are you sure to Login as {`${user.userName}`}?</p>
      <div className="mx-0 my-auto w-12 flex justify-content-center align-items-center">
        <Button
          type="success"
          text="Yes"
          className="inline-block mx-2 w-3 p-0 h-2rem"
          onClick={onConfirmSuperLoginClick}
        />

        <Button
          type="danger"
          text="No"
          className="inline-block mx-2 w-3 p-0 h-2rem"
          onClick={onNoBtnClick}
        />
      </div>
    </>
  );
};

export default ConfirmSuperLoginPopupBody;
