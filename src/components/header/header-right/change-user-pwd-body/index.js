import { useMenu } from '@contexts/menu';
import { Button, CheckBox, TextBox } from 'devextreme-react';

import { forwardRef, useCallback, useEffect, useState } from 'react';
import { bake_cookie, read_cookie } from 'sfcookies';
const cookieKey = 'namedOFCookie';

const onTextAreaInitialized = (e) => {
  setTimeout(() => {
    e.component.focus();
  }, 0);
};

const EmailPwdPopupBody = ({ callChangePwd, updateUserLastSid }, ref) => {
  const {
    user: { userEmailConfigMessage: emailConfigMsg, userLastSid, userLogin },
  } = useMenu();
  const [password, setPassword] = useState({
    password: '',
    passwordVerify: '',
  });
  const [changePassword, setChangePassword] = useState(false);
  const [showCheckBox, setShowCheckBox] = useState(false);
  const onPasswordChanged = (e) => {
    setPassword((prev) => ({ ...prev, [e.event.target.name]: e.value }));
  };
  const onChangePasswordChanged = useCallback((e) => {
    setChangePassword(e.value);
    updateUserLastSid(e.value);
  }, []);
  useEffect(() => {
    //Show Change Password only for userLastSid==-1 and page is index
    if (userLastSid === -1 && read_cookie(cookieKey).toString().length === 0) {
      setShowCheckBox(true);
      bake_cookie(cookieKey, true);
    }
  }, [userLastSid]);
  const onFormSubmit = (e) => {
    e.preventDefault();
    // Check whether submit is actually disabled
    if (!changePassword) {
      //Call this function when save is clicked
      const currentPwd =
        userLogin == 'Admin' ? password.currentPassword : 'test';
      const newPwd = password.password;
      const confirmPwd = password.passwordVerify;
      if (!currentPwd || !newPwd || !confirmPwd) {
        ref.current.show({
          severity: 'error',
          summary: 'Warning',
          detail: "Password can't be empty",
          life: 3000,
        });
      } else if (newPwd !== confirmPwd) {
        ref.current.show({
          severity: 'error',
          summary: 'Warning',
          detail: 'Please make sure passwords match',
          life: 3000,
        });
      } else if (newPwd.length < 8) {
        ref.current.show({
          severity: 'error',
          summary: 'Warning',
          detail: 'Passwords should have minimum of 8 Characters',
          life: 3000,
        });
      } else if (/\s/.test(newPwd)) {
        ref.current.show({
          severity: 'error',
          summary: 'Warning',
          detail: "Password should't contain whitespaces",
          life: 3000,
        });
      } else if (
        !newPwd.match(
          /^(?!.* )(?=.*?[A-Z])(?=.*?[a-z])(?=.*?\d)(?=.*?[#?!@$%^&*-]).{8,25}$/
        )
      ) {
        ref.current.show({
          severity: 'error',
          summary: 'Warning',
          detail:
            'Password must contain a special Character, a number and a capital letter',
          life: 3000,
        });
      } else {
        updateUserLastSid(1);
        const param = {
          password: newPwd,
          confirmPassword: confirmPwd,
        };
        if (userLogin == 'Admin') param.currentPassword = currentPwd;
        callChangePwd(param);
      }
    }
  };
  return (
    <>
      <form onSubmit={onFormSubmit}>
        <div className="grid w-full">
          <div className="col-12 text-300 p-1 pl-2">
            (Password must be of minimum 8 characters long and should contain
            upper case, lower case, special characters and numeric digits.)
          </div>
          {userLogin == 'Admin' && (
            <>
              <div className="col-5 flex align-items-center p-0 pl-2">
                Current Password <span className="text-red-500">*</span>
              </div>
              <div className="col-7 flex align-items-center p-0">
                <TextBox
                  name="currentPassword"
                  mode="password"
                  onInitialized={onTextAreaInitialized}
                  value={password.currentPassword}
                  onValueChanged={onPasswordChanged}
                  disabled={changePassword}
                />
              </div>
            </>
          )}
          <div className="col-5 flex align-items-center p-0 pl-2 pt-1">
            New Password <span className="text-red-500">*</span>
          </div>
          <div className="col-7 flex align-items-center p-0 pt-1">
            <TextBox
              name="password"
              mode="password"
              value={password.password}
              onValueChanged={onPasswordChanged}
              disabled={changePassword}
            />
          </div>
          <div className="col-5 flex align-items-center p-0 pl-2 pt-1">
            Re-enter Password <span className="text-red-500">*</span>
          </div>
          <div className="col-7 flex align-items-center p-0 pt-1">
            <TextBox
              name="passwordVerify"
              mode="password"
              value={password.passwordVerify}
              onValueChanged={onPasswordChanged}
              disabled={changePassword}
            />
          </div>
          <div className="col-12 flex align-items-center p-1 pl-2">
            <CheckBox
              value={changePassword}
              onValueChanged={onChangePasswordChanged}
              text="I do not wish to change the password"
              visible={showCheckBox}
            />
          </div>
          <div className="col-12 text-300 p-0 pl-2">{emailConfigMsg}</div>
          <div className="col-12 flex justify-content-center mt-2 p-0">
            <Button
              type="success"
              text="Save"
              useSubmitBehavior={true}
              className="p-0"
              disabled={changePassword}
            />
          </div>
        </div>
      </form>
    </>
  );
};

export default forwardRef(EmailPwdPopupBody);
