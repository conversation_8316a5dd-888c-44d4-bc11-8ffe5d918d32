import {
  Chart,
  Series,
  CommonSeriesSettings,
  Label,
  Legend,
  Crosshair,
} from 'devextreme-react/chart';
import { forwardRef, useLayoutEffect } from 'react';
import styles from './index.module.css';

const BarChart = forwardRef(
  ({ data, firstVal, secondVal, arg, firstTitle, secondTitle }, ref) => {
    useLayoutEffect(() => {
      if (ref.current) {
        setTimeout(() => {
          ref.current?.instance.render();
        });
      }
    });
    return (
      <Chart
        className={styles.bar_chart}
        dataSource={data}
        ref={ref}
        stickyHovering={true}
        resolveLabelOverlapping="stack"
      >
        <CommonSeriesSettings
          argumentField="month"
          type="bar"
          hoverMode="allArgumentPoints"
          selectionMode="allArgumentPoints"
          barPadding={0}
        />

        <Crosshair enabled={true}>
          <Label visible={true} format="currency" />
        </Crosshair>
        <Series
          valueField={firstVal}
          argumentField={arg}
          name={firstTitle}
          type="bar"
        />
        <Series valueField={secondVal} name={secondTitle} />
        <Legend verticalAlignment="bottom" horizontalAlignment="top" />
      </Chart>
    );
  }
);
BarChart.defaultProps = {
  data: [],
  firstVal: '',
  secondVal: '',
  arg: '',
  firstTitle: '',
  secondTitle: '',
};
export default BarChart;
