const Loading = () => {
  return (
    <div className="loading-container">
      <div className="loading-spinner">
        {/* <img
          src={`${basePath}/logo.png`}
          alt="Loading..."
          width="100px"
          height="100px"
        /> */}
        <h3 className="uppercase">LOADING...</h3>
      </div>
      <style jsx>{`
        .loading-container {
          position: relative;
          height: 100vh;
        }
        .loading-spinner {
          position: absolute;
          left: 50%;
          text-align: center;
          top: 50%;
          transform: translate(-50%, -50%);
          animation: loading_Blink 1.4s ease infinite;
          opacity: 1;
        }
        @keyframes loading_Blink {
          50% {
            opacity: 0.4;
          }
          100% {
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};

export default Loading;
