import { Dropdown } from 'primereact/dropdown';

const DropdownComp = ({
  value,
  onChange,
  dropdownOptions,
  className,
  placeholder,
  panelClassName,
  disabled,
  tooltip,
  optionLabel,
  filter,
  showClear,
  valueTemplate,
  itemTemplate,
  emptyFilterMessage,
  style,
  panelStyle,
}) => {
  return (
    <Dropdown
      value={value}
      options={dropdownOptions}
      onChange={onChange}
      className={`text-xs p-inputtext-sm  ${className}`}
      placeholder={placeholder}
      panelClassName={panelClassName}
      disabled={disabled}
      tooltip={tooltip}
      tooltipOptions={{ position: 'bottom' }}
      optionLabel={optionLabel}
      filter={filter}
      showClear={showClear}
      valueTemplate={valueTemplate}
      itemTemplate={itemTemplate}
      emptyFilterMessage={emptyFilterMessage}
      style={style}
      panelStyle={panelStyle}
      scrollHeight="300px"
    />
  );
};

export default DropdownComp;
