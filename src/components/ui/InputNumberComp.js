import { InputNumber } from 'primereact/inputnumber';

const InputNumberComp = ({
  children,
  inputId,
  className,
  value,
  onValueChange,
  mode,
  currency,
  locale,
  prefix,
  disabled,
}) => {
  return (
    <InputNumber
      inputId={inputId}
      inputClassName={`p-inputtext-sm text-sm ${className}`}
      value={value}
      onValueChange={onValueChange}
      mode={mode}
      currency={currency}
      locale={locale}
      prefix={prefix}
      disabled={disabled}
    >
      {children}
    </InputNumber>
  );
};
InputNumberComp.defaultProps = {
  className: '',
};
export default InputNumberComp;
