import ButtonComp from '../ButtonComp';

const SocialLoginButton = ({
  onClick,
  buttonSrc,
  title,
  tooltip,
  id,
  loading,
}) => {
  return (
    <ButtonComp
      className="p-button-rounded p-button-info p-button-lg bg-white m-2 p-3"
      onClick={onClick}
      tooltip={tooltip}
      id={id}
      disabled={loading}
    >
      <img
        style={{ cursor: 'pointer' }}
        alt="logo"
        src={buttonSrc}
        height="15"
        width="15"
        title={title}
        id={id}
      />
    </ButtonComp>
  );
};

export default SocialLoginButton;
