import { Button } from 'primereact/button';
const ButtonComp = ({
  children,
  label,
  className,
  icon,
  onClick,
  title,
  type,
  style,
  disabled,
  tooltip,
  loading,
  id,
}) => {
  return (
    <Button
      label={label}
      className={`text-xs ${className}`}
      icon={icon ? `pi ${icon}` : null}
      onClick={onClick}
      title={title}
      type={type}
      style={style}
      disabled={disabled}
      tooltip={tooltip}
      tooltipOptions={{ position: 'bottom' }}
      loading={loading}
      id={id}
    >
      {children}
    </Button>
  );
};

export default ButtonComp;
