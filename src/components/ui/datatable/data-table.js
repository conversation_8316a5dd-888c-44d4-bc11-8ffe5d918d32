import DataGrid, { Column, Scrolling } from 'devextreme-react/data-grid';

import styles from './data-table.module.css';

const DataTable = ({
  allowColumnReordering,
  allowColumnResizing,
  allowScrolling,
  className,
  columns,
  columnAutoWidth,
  dataSource,
  dataRowRender,
  innerRef,
  keyExpr,
  showBorders,
  onOptionChanged,
  onContentReady,
  noDataText,
}) => {
  return (
    <div className={`${styles.data_table}`}>
      <DataGrid
        allowColumnReordering={allowColumnReordering}
        allowColumnResizing={allowColumnResizing}
        allowScrolling={allowScrolling}
        columnAutoWidth={columnAutoWidth}
        className={className}
        dataRowRender={dataRowRender}
        dataSource={dataSource}
        keyExpr={keyExpr}
        onOptionChanged={onOptionChanged}
        onContentReady={onContentReady}
        noDataText={noDataText}
        ref={innerRef}
        showBorders={showBorders}
        wordWrapEnabled={true}
      >
        <Scrolling visible={allowScrolling} showScrollbar="always" />
        {columns.map((column) => (
          <Column
            key={column.key}
            dataField={column.dataField}
            dataType={column.dataType}
            caption={column.caption}
            allowSorting={column.allowSorting}
            visible={column.visible}
            cssClass={column.cssClass}
            width={column.width}
            allowResizing={column.allowResizing}
          />
        ))}
      </DataGrid>
    </div>
  );
};

export default DataTable;
DataTable.defaultProps = {
  className: '',
};
