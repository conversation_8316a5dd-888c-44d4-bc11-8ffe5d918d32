import { InputText } from 'primereact/inputtext';

const InputTextComp = ({
  children,
  id,
  type,
  className,
  value,
  isDisabled,
  icon,
  onChange,
  autoFocus,
  placeholder,
}) => {
  return (
    <InputText
      id={id}
      type={type}
      className={`p-inputtext-sm text-sm ${className}`}
      value={value}
      disabled={isDisabled}
      icon={icon ? `pi ${icon}` : null}
      onChange={onChange}
      autoFocus={autoFocus}
      autoComplete="off"
      placeholder={placeholder}
    >
      {children}
    </InputText>
  );
};
InputTextComp.defaultProps = {
  isDisabled: false,
  className: '',
};
export default InputTextComp;
