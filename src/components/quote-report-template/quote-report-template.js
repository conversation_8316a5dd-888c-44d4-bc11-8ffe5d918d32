import TextBox from 'devextreme-react/text-box';
import TextArea from 'devextreme-react/text-area';
import { quoteData } from '@modules/quotes/data';
import { useDefaults } from '@contexts/defaults';
const DEFAULT_MANUFACTURER = 'Prysmain Draka Group  - ';
const checkCustomSecCustomer = (obj) =>
  obj?.labelDefault == obj?.labelCustom ? false : true;

export const QuoteReportTemplate = ({
  showSecCustomer,
  compInfoAlignRight,
  titleShowManufacturer,
  isTopicVisible,
  previewLogo,
  onSelectFile,
  isCustomAddress,
  visible,
  onInputChange,
  addressBox,
  validFileTypes,
}) => {
  const {
    defaults: { NAME, ADDRESS, CUSTOM_LABELS },
  } = useDefaults();
  const customSecondaryCustomer = CUSTOM_LABELS?.find(
    (it) => it?.labelId === 'IDS_SECOND_CUSTOMER'
  );
  const secondaryCustomerTitle = checkCustomSecCustomer(customSecondaryCustomer)
    ? customSecondaryCustomer.labelCustom
    : 'On behalf of';
  const { showPreparedBy, showSalesTeam } = visible;
  const viewCompanyName = () =>
    visible.companyName ? visible.companyName : NAME;

  const viewCompanyAddress = () => {
    return visible.companyName
      ? visible.companyAddress
          .split('\n')
          .map((it, i) => <p key={`x${i}`}>{it}</p>)
      : ADDRESS.split('\n').map((it, i) => <p key={`x${i}`}>{it}</p>);
  };
  return (
    <>
      <div className="pr-4">
        <div className="d-f jc-sb">
          <label>
            <div className="image-box">
              <img src={previewLogo} alt="" title="Click to upload logo" />
            </div>
            <div className="controls hidden">
              <input
                type="file"
                name="contact_image_1"
                onChange={onSelectFile}
                accept={validFileTypes}
              />
            </div>
          </label>
          <div
            className={`text-${
              compInfoAlignRight ? 'right' : 'left'
            } quote_company_section`}
          >
            {isCustomAddress ? (
              <>
                <TextBox
                  value={visible.companyName}
                  onValueChange={(e) => {
                    onInputChange(e, 'companyName');
                  }}
                  ref={addressBox}
                  valueChangeEvent="keyup"
                  focusStateEnabled
                  placeholder="Company name"
                  RequiredRule={true}
                  className="text-right mb-2"
                />

                <TextArea
                  value={visible.companyAddress}
                  onValueChange={(e) => {
                    onInputChange(e, 'companyAddress');
                  }}
                  placeholder="Company Address"
                  rows="5"
                  autoResizeEnabled
                  disabled={visible.companyName.replace(/\s+/g, '').length == 0}
                />
              </>
            ) : (
              <>
                <p>
                  <b>{viewCompanyName()}</b>
                </p>
                {viewCompanyAddress()}
              </>
            )}
          </div>
        </div>
        <div className="mt-1 ta-c">
          <h5>
            {`${titleShowManufacturer ? DEFAULT_MANUFACTURER : ''}Quotation`}
          </h5>
        </div>
        <div className={`d-f jc-sb mt-1 ${isTopicVisible ? 'mb-2' : ''}`}>
          <div className="quote_company_section">
            <p>Prepared For : </p>
            <p>{quoteData[0].custContactDetail.CONT_FULL_NAME}</p>
            <p>
              {quoteData[0].custCompanyDetail.COMP_NAME}
              <br />
              {quoteData[0].custCompanyDetail.COMP_ADDRESS_1}
              <br />
              {quoteData[0].custCompanyDetail.COMP_CITY}&nbsp;
              {quoteData[0].custCompanyDetail.COMP_STATE}&nbsp;
              {quoteData[0].custCompanyDetail.COMP_ZIP_CODE}
            </p>
            <div>
              {showPreparedBy && (
                <div className="mt-2">
                  <p>Prepared By : </p>
                  <p>
                    {quoteData[0].preparedByDetail.COMP_NAME}
                    <br />
                    Phone : {quoteData[0].preparedByDetail.COMP_PHONE}
                    <br />
                    Email : {quoteData[0].preparedByDetail.COMP_EMAIL}
                  </p>
                </div>
              )}
            </div>
          </div>
          <div>
            <div align="right">
              <table>
                <tbody>
                  <tr>
                    <td className="text-right vertical-align-top">
                      Quote No :
                    </td>
                    <td className="text-left pl-1">
                      <span className="font-bold text-wrapper">
                        {quoteData[0].QUOT_NUMBER}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td className="text-right">Quote Date : </td>
                    <td className="text-left pl-1">{quoteData[0].QUOT_DATE}</td>
                  </tr>
                  <tr>
                    <td className="text-right"> Expiration Date : </td>
                    <td className="text-left pl-1">
                      {quoteData[0].QUOT_EXPIRY}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            {showSalesTeam && (
              <div className="text-right pt-2 ">
                <span>Account Manager : </span>
                {quoteData[0].accountManagerDetails.USER_NAME}
                <p>
                  Phone : {quoteData[0].accountManagerDetails.PHONE}
                  <br />
                  Cell : {quoteData[0].accountManagerDetails.CELL}
                  <br />
                  Email : {quoteData[0].accountManagerDetails.EMAIL}
                </p>
              </div>
            )}
          </div>
        </div>
        {isTopicVisible && (
          <div
            data-cy="quote-topic"
            className={`${!showSecCustomer ? 'mb-2' : ''}`}
          >
            <p>Topic : {quoteData[0].QUOT_CUST_PROGRAM}</p>
          </div>
        )}

        {showSecCustomer && (
          <div className="mb-3" data-cy="quote-topic">
            <p>
              {`${secondaryCustomerTitle} : ${quoteData[0].secondaryCustomerDetail.COMP_NAME}`}
            </p>
          </div>
        )}
      </div>

      <style jsx>
        {`
          table,
          th,
          td {
            border-collapse: collapse;
          }
          #customers {
            border-collapse: separate;
            width: 100%;
            border-spacing: 0 1rem !important;
          }
          .bs {
            border-spacing: 0 1em !important;
          }

          #customers {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12px
            border-collapse: separate;
            width: 100%;
            border-spacing: 0 1em !important;
            border: 1px solid black;
          }

          .bg {
            background-color: #b4c5de !important;
          }
          .w-wr {
            word-wrap: break-word;
          }
          .ta-c {
            text-align: center;
          }
          .d-f {
            display: flex;
            margin-bottom: 2px;
          }
          .jc-sb {
            justify-content: space-between;
            height: 80x;
          }

          .note {
            position: absolute;
            left: 0;
            bottom: 2;
            margin: 0 !important;
          }
          .image-box {
           height : 100%;
           width : 100%;
           cursor: pointer;
           overflow: hidden;
          }          
          img {
            max-width: 200px;
            max-height: 130px;
            object-fit: contain;        
          }
          .quote_company_section {
            width: 32%;
          }
          .visibility_hidden{
            visibility:hidden
          }
        `}
      </style>
    </>
  );
};

export default QuoteReportTemplate;
