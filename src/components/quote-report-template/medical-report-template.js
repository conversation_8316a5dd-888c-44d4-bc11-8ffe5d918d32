import TextBox from 'devextreme-react/text-box';
import TextArea from 'devextreme-react/text-area';
import { quoteData } from '@modules/quotes/data';
import { useDefaults } from '@contexts/defaults';
import nextConfig from 'next.config';
import { LOGO } from 'src/constants';
const DEFAULT_MANUFACTURER = 'Prysmain Draka Group  - ';
const checkCustomSecCustomer = (obj) =>
  obj?.labelDefault == obj?.labelCustom ? false : true;

export const MedicalReportTemplate = ({
  showSecCustomer,
  compInfoAlignRight,
  titleShowManufacturer,
  isTopicVisible,
  previewLogo,
  onSelectFile,
  isCustomAddress,
  visible,
  onInputChange,
  addressBox,
  validFileTypes,
}) => {
  const {
    defaults: { NAME, ADDRESS, CUSTOM_LABELS },
  } = useDefaults();
  const customSecondaryCustomer = CUSTOM_LABELS?.find(
    (it) => it?.labelId === 'IDS_SECOND_CUSTOMER'
  );
  const secondaryCustomerTitle = checkCustomSecCustomer(customSecondaryCustomer)
    ? customSecondaryCustomer.labelCustom
    : 'On behalf of';
  const { showPreparedBy, showSalesTeam } = visible;
  const viewCompanyName = () =>
    visible.companyName ? visible.companyName : NAME;

  const viewCompanyAddress = () => {
    return visible.companyName
      ? visible.companyAddress
          .split('\n')
          .map((it, i) => <p key={`x${i}`}>{it}</p>)
      : ADDRESS.split('\n').map((it, i) => <p key={`x${i}`}>{it}</p>);
  };

  const { basePath } = nextConfig;
  const CRM_HEADER_LOGO =
    process.env.NODE_ENV === 'development' ? `${basePath}/twdev.png` : LOGO;
  return (
    <>
      <div className="pr-4">
        <div className="d-f jc-sb">
          <div>
            <label>
              <div className="image-box">
                <img src={previewLogo} alt="" title="Click to upload logo" />
              </div>
              <div className="controls hidden">
                <input
                  type="file"
                  name="contact_image_1"
                  onChange={onSelectFile}
                  accept={validFileTypes}
                />
              </div>
            </label>

            <p>
              <b>Medfabric Manufacturer</b>
            </p>
            <p>Adddress</p>
            <p>City</p>
            <p>State</p>
            <p>Zip_code</p>
          </div>
          <div
            className={`text-${
              compInfoAlignRight ? 'right' : 'left'
            } quote_company_section`}
          >
            <div className="text-center mb-2">
              <img src={CRM_HEADER_LOGO} alt="" title="logo" />
            </div>
            {isCustomAddress ? (
              <>
                <TextBox
                  value={visible.companyName}
                  onValueChange={(e) => {
                    onInputChange(e, 'companyName');
                  }}
                  ref={addressBox}
                  valueChangeEvent="keyup"
                  focusStateEnabled
                  placeholder="Company name"
                  RequiredRule={true}
                  className="text-right mb-2"
                />

                <TextArea
                  value={visible.companyAddress}
                  onValueChange={(e) => {
                    onInputChange(e, 'companyAddress');
                  }}
                  placeholder="Company Address"
                  rows="5"
                  autoResizeEnabled
                  disabled={visible.companyName.replace(/\s+/g, '').length == 0}
                />
              </>
            ) : (
              <>
                <p>
                  <b>{viewCompanyName()}</b>
                </p>
                {viewCompanyAddress()}
              </>
            )}
          </div>
        </div>
        <div className="mt-1 ta-c">
          <h5>
            {`${titleShowManufacturer ? DEFAULT_MANUFACTURER : ''} Sales Order`}
          </h5>
        </div>
        <div className={`d-f jc-sb mt-1 ${isTopicVisible ? 'mb-2' : ''}`}>
          <div className="quote_company_section">
            <p>Surgeon : </p>
            <p>Dr. Medfabric Doctor</p>
            <div className="mt-2">
              <p>Medfabric Distributor</p>
            </div>
            <div>
              {showPreparedBy && (
                <div className="mt-2">
                  <p>Rep : </p>
                  <p>Medfabric User</p>
                  <p>Phone : +*********</p>
                  <p>Email : <EMAIL></p>
                </div>
              )}
            </div>
          </div>
          <div>
            <div align="right">
              <table>
                <tbody>
                  <tr>
                    <td className="text-right vertical-align-top">SO No :</td>
                    <td className="text-left pl-1">
                      <span className="font-bold text-wrapper">
                        {quoteData[0].QUOT_NUMBER}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td className="text-right">Date of Surgery : </td>
                    <td className="text-left pl-1">{quoteData[0].QUOT_DATE}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            {showSalesTeam && (
              <div className="text-right pt-2 ">
                <span>Account Manager : </span>
                {quoteData[0].accountManagerDetails.USER_NAME}
                <p>
                  Phone : {quoteData[0].accountManagerDetails.PHONE}
                  <br />
                  Cell : {quoteData[0].accountManagerDetails.CELL}
                  <br />
                  Email : {quoteData[0].accountManagerDetails.EMAIL}
                </p>
              </div>
            )}
            <div className="text-right pt-2 ">
              <span className="font-bold">Case Number : </span>
              #12345
            </div>
          </div>
        </div>
        {isTopicVisible && (
          <div
            data-cy="quote-topic"
            className={`${!showSecCustomer ? 'mb-2' : ''}`}
          >
            <p>Procedure : Subtalar Arthrodesis</p>
          </div>
        )}

        {showSecCustomer && (
          <div className="mb-3" data-cy="quote-topic">
            <p>{secondaryCustomerTitle} : Medfabric Customer</p>
          </div>
        )}
      </div>

      <style jsx>
        {`
          table,
          th,
          td {
            border-collapse: collapse;
          }
          #customers {
            border-collapse: separate;
            width: 100%;
            border-spacing: 0 1rem !important;
          }
          .bs {
            border-spacing: 0 1em !important;
          }

          #customers {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12px
            border-collapse: separate;
            width: 100%;
            border-spacing: 0 1em !important;
            border: 1px solid black;
          }

          .bg {
            background-color: #b4c5de !important;
          }
          .w-wr {
            word-wrap: break-word;
          }
          .ta-c {
            text-align: center;
          }
          .d-f {
            display: flex;
            margin-bottom: 2px;
          }
          .jc-sb {
            justify-content: space-between;
            height: 80x;
          }

          .note {
            position: absolute;
            left: 0;
            bottom: 2;
            margin: 0 !important;
          }
          .image-box {
          //  height : 100%;
           width : 100%;
           cursor: pointer;
           overflow: hidden;
          }          
          img {
            max-width: 200px;
            max-height: 130px;
            object-fit: contain;        
          }
          .quote_company_section {
            width: 32%;
          }
          .visibility_hidden{
            visibility:hidden
          }
        `}
      </style>
    </>
  );
};

export default MedicalReportTemplate;
