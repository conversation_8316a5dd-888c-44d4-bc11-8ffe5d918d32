import { Button } from 'devextreme-react';
import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

import styles from './modal.module.css';

const Modal = ({
  setOpen,
  title,
  footer,
  children,
  isExpandCollapse,
  isSortReq,
  onSortBtn,
  isSortDisabled,
  sortHintTitle,
}) => {
  const [isMounted, setIsMounted] = useState(false);
  const [isExpand, setIsExpand] = useState(false);
  const [expandCollapseTitle, expandCollapseClassName] = isExpand
    ? ['Collapse', styles.custom_popup__collapse]
    : ['Expand', styles.custom_popup__expand];
  useEffect(() => setIsMounted(true), []);

  const close = () => {
    setOpen(false);
  };
  const onExpandCollapseChanged = () => {
    setIsExpand(!isExpand);
  };
  let popupHolderClassName = styles.custom_popup__holder;
  if (isExpand) {
    popupHolderClassName += ` ${styles.custom_popup__fullscreen}`;
  }

  return isMounted
    ? createPortal(
        <div
          className={`${styles.custom_popup} js-custom-popup ${styles.is_active}`}
          id="custom-popup"
          data-popup="custom-popup"
        >
          <div className={popupHolderClassName}>
            {isExpandCollapse && (
              <span
                className={expandCollapseClassName}
                onClick={onExpandCollapseChanged}
                title={expandCollapseTitle}
              />
            )}
            <span
              className={`${styles.custom_popup__close} js-close-popup`}
              onClick={close}
              title="Close"
            />
            <div className={`${styles.custom_popup__title}`}>
              <strong>{title}</strong>

              <Button
                className="ml-2"
                icon="pi pi-sort"
                onClick={onSortBtn}
                disabled={isSortDisabled}
                visible={isSortReq}
                hint={sortHintTitle}
              />
            </div>
            <div className={`${styles.custom_popup__content}`}>{children}</div>
            <div className={`${styles.custom_popup__footer}`}>{footer}</div>
          </div>
        </div>,
        document.getElementById('modal')
      )
    : null;
};
Modal.defaultProps = {
  title: 'Default Popup',
  footer: <></>,
  isExpandCollapse: false,
  isSortReq: false,
  isSortDisabled: false,
  sortHintTitle: '',
};
export default Modal;
