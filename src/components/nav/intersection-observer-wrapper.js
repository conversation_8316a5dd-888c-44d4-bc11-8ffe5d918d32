import React, { useRef, useEffect, useState } from 'react';
import classnames from 'classnames';
import OverflowMenu from './overflow-menu';
import styles from './nav.module.css';

const IntersectionObserverWrapper = ({ update, children }) => {
  const navRef = useRef(null);
  const [visibilityMap, setVisibilityMap] = useState({});
  const handleIntersection = (entries) => {
    const updatedEntries = {};
    entries.forEach((entry) => {
      const { targetid } = entry.target.dataset;

      if (entry.isIntersecting) {
        updatedEntries[targetid] = true;
      } else {
        updatedEntries[targetid] = false;
      }
    });

    setVisibilityMap((prev) => ({
      ...prev,
      ...updatedEntries,
    }));
  };
  useEffect(() => {
    const observer = new IntersectionObserver(handleIntersection, {
      root: navRef.current,
      threshold: 1,
    });

    // We are addting observers to child elements of the container div
    // with ref as navRef. Notice that we are adding observers
    // only if we have the data attribute observerid on the child elemeent
    Array.from(navRef.current.children).forEach((item) => {
      if (item.dataset.targetid) {
        observer.observe(item);
      }
    });
    return () => observer.disconnect();
  }, [update]);
  return (
    <div className={styles.menu_toolbarWrapper} ref={navRef}>
      {React.Children.map(children, (child) => {
        return React.cloneElement(child, {
          className: classnames(child.props.className, {
            [styles.menu_visible]:
              !!visibilityMap[child.props['data-targetid']],
            [styles.menu_inVisible]:
              !visibilityMap[child.props['data-targetid']],
          }),
        });
      })}
      <OverflowMenu
        visibilityMap={visibilityMap}
        className={styles.menu_overflowStyle}
      >
        {children}
      </OverflowMenu>
    </div>
  );
};

export default IntersectionObserverWrapper;
