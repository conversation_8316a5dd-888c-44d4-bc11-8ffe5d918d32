.navigation_bar {
  width: calc(75vw);
  background-color: #0078d4;
  margin-left: -5px;
  margin-top: 0px;
}
.menu_list {
  list-style-type: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
.menu_list li {
  float: left;
  width: auto;
}

.menu_list li a {
  display: block;
  color: white;
  text-align: left;
  padding: 10px 15px;
  text-decoration: none;
  line-height: 1.42857143;
  /* width: 6rem;
  overflow: hidden; */
  white-space: nowrap;
  text-overflow: ellipsis;
}
.long-dropdown a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}
.buttonClass {
  border-color: transparent;
  width: auto;
  white-space: nowrap;
  background-color: transparent;
  color: white;
}

.menu_visible {
  order: 0;
  visibility: visible;
  opacity: 1;
}
.menu_inVisible {
  order: 100;
  visibility: hidden;
  pointer-events: none;
}
.menu_toolbarWrapper {
  display: flex;
  overflow: hidden;
  width: 100%;
}
.menu_toolbarWrapper > li {
  font-size: 88%;
}
.menu_overflowStyle {
  order: 99;
  position: fixed;
  right: 8rem;
  top: 10px;
}
.menu_overflow {
  position: absolute;
  top: 100%;
  max-height: 350px;
  overflow-y: auto;
  z-index: 1000;
  min-width: 180px;
  padding: 5px 0;
  margin: 2px 0 0;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}
.menu_overflow a {
  color: black;
  padding: 3px 20px;
  text-decoration: none;
  display: block;
  font-size: 14px;
}
.menu_overflow a:hover {
  background-color: #e1e3e9;
}
.menu_overflowStyle > button {
  padding: 0.5rem 1rem;
  color: white;
}
.menu_overflowStyle > button > span .buttonClass:enabled:active,
.buttonClass:enabled:focus {
  background: white;
  color: #005a9e;
  box-shadow: none;
  border: none;
}
