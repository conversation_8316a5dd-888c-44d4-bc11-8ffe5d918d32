import styles from './nav.module.css';

import IntersectionObserverWrapper from './intersection-observer-wrapper';
import { useEffect, useMemo, useState } from 'react';
import { getDeligationUsers } from '@services/api';
import { getTodaysDate } from 'src/utils';
import SmallModal from '@components/small-modal';
import { Button, SelectBox } from 'devextreme-react';
import { useMenu } from '@contexts/menu';

export default function Nav({ flatMenus }) {
  const { user } = useMenu();
  const [delegationUserList, setDelegationUserList] = useState([]);
  const [emailUrl, setEmailUrl] = useState('');
  const [selectedDelgnUser, setSelectedDelgnUser] = useState(user.userId);
  const [isDelgnPopupVisible, setIsDelgnPopupVisible] = useState(false);
  useEffect(() => {
    const fetchdelegationUsers = async () => {
      try {
        const delegationUsers = await (
          await getDeligationUsers(getTodaysDate().format('MM-DD-YYYY'))
        ).data;
        setDelegationUserList(delegationUsers);
      } catch (error) {
        throw new Error(error);
      }
    };
    fetchdelegationUsers();
  }, []);
  useEffect(() => {
    if (!isDelgnPopupVisible) {
      setSelectedDelgnUser(user.userId);
    }
  }, [isDelgnPopupVisible]);
  const horizontalMenuList = useMemo(
    () =>
      flatMenus.map((menu) => {
        if (
          menu.label.toLowerCase() === 'email' &&
          delegationUserList.length > 1
        ) {
          const { url, ..._menu } = menu;
          setEmailUrl(url);
          return {
            ..._menu,
            url: '#',
            command: () => {
              setIsDelgnPopupVisible(true);
            },
          };
        }
        return menu;
      }),
    [flatMenus, delegationUserList]
  );
  const onDelgnUserChanged = (e) => {
    setSelectedDelgnUser(e.value);
  };
  const handleOpenClick = () => {
    const modifiedEmailUrl = emailUrl.split('&');
    // 11 - reqId=<5 digits>
    const changeReqId = `${modifiedEmailUrl[1].substring(
      0,
      11
    )}${selectedDelgnUser}${modifiedEmailUrl[1].substring(
      modifiedEmailUrl[1].length - 1
    )}`;
    window.location.href = [
      modifiedEmailUrl[0],
      changeReqId,
      modifiedEmailUrl[2],
    ].join('&');
  };
  const handleCancelClick = () => {
    setIsDelgnPopupVisible(false);
  };
  const DelegationPopupFooter = (
    <div className="flex">
      <div className="flex-initial flex m-1">
        <Button type="success" text="Open" onClick={handleOpenClick} />
      </div>
      <div className="flex-initial flex m-1">
        <Button
          className="bg-yellow-600 text-0"
          text="Cancel"
          onClick={handleCancelClick}
        />
      </div>
    </div>
  );
  const [updateMenu, setUpdateMenu] = useState(false);
  useEffect(() => {
    setUpdateMenu(!updateMenu);
  }, [horizontalMenuList]);
  const renderMenuItem = (menu) =>
    menu.length > 17 ? menu.substring(0, 14).concat('..') : menu;

  return (
    <div className={styles.navigation_bar}>
      <ul className={styles.menu_list}>
        <IntersectionObserverWrapper update={updateMenu}>
          {horizontalMenuList.map((item) => {
            return (
              <li
                key={item.id}
                data-targetid={item.label}
                className={styles.buttonClass}
                title={item.label}
              >
                <a href={item.url} title={item.label} onClick={item.command}>
                  {renderMenuItem(item.label)}
                </a>
              </li>
            );
          })}
        </IntersectionObserverWrapper>
      </ul>
      {isDelgnPopupVisible && (
        <SmallModal
          title="Delegations"
          footer={DelegationPopupFooter}
          setOpen={setIsDelgnPopupVisible}
        >
          <div className="dx-fieldset mx-0 ">
            <div className="dx-field">
              <div className="dx-field-label font-bold w-6 text-color">
                Open E-mail of
              </div>
              <div className="dx-field-value w-6">
                <SelectBox
                  dataSource={delegationUserList}
                  displayExpr="userName"
                  valueExpr="userId"
                  value={selectedDelgnUser}
                  onValueChanged={onDelgnUserChanged}
                />
              </div>
            </div>
          </div>
        </SmallModal>
      )}
    </div>
  );
}
