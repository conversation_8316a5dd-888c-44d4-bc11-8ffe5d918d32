import React, { useState, useMemo, useRef } from 'react';

import { Button } from 'primereact/button';
import styles from './nav.module.css';
import { useOnClickOutside } from 'src/hooks/useOnClickOutside';

export default function OverflowMenu({ children, className, visibilityMap }) {
  const [anchorEl, setAnchorEl] = useState(false);

  const dropdownMenuRef = useRef(null);
  useOnClickOutside(dropdownMenuRef, () => setAnchorEl(false));
  const handleClick = () => {
    setAnchorEl(!anchorEl);
  };
  const shouldShowMenu = useMemo(
    () => Object.values(visibilityMap).some((v) => v === false),
    [visibilityMap]
  );
  if (!shouldShowMenu) {
    return null;
  }
  const moreIsNeeded =
    React.Children.map(children, (child) => {
      if (!visibilityMap[child.props['data-targetid']]) {
        return child.props.children;
      }
    }).length === 0;
  return (
    <div className={`${className} flex`}>
      <Button
        aria-label="more"
        aria-controls="long-menu"
        aria-haspopup="true"
        onClick={handleClick}
        style={{
          position: 'relative',
          fontSize: '1rem',
        }}
        className="align-items-center"
        visible={!moreIsNeeded}
      >
        More <span className="dx-icon-spindown" />
      </Button>
      {anchorEl && (
        <div
          ref={dropdownMenuRef}
          id="myDropdown"
          className={styles.menu_overflow}
        >
          {React.Children.map(children, (child) => {
            if (!visibilityMap[child.props['data-targetid']]) {
              return child.props.children;
            }
            return null;
          })}
        </div>
      )}
    </div>
  );
}
