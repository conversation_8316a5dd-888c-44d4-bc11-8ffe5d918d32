import { TabView, TabPanel } from 'primereact/tabview';
import Expense from './expense';
import ExpenseReports from './expenseReports';
import { ExpenseProvider } from '@contexts/expense';

const ExpenseModule = () => {
  return (
    <div className="card tabview-demo p-2">
      <ExpenseProvider>
        <TabView className="tabview-header-icon">
          <TabPanel header="Expense Report" leftIcon="">
            <ExpenseReports status={0} />
          </TabPanel>
          <TabPanel header="Expenses Feed" leftIcon="">
            <Expense />
          </TabPanel>
          <TabPanel header="Waiting for Approval" leftIcon="">
            <ExpenseReports status={1} />
          </TabPanel>

          <TabPanel header="Waiting to be Paid" leftIcon="">
            <ExpenseReports status={2} />
          </TabPanel>
        </TabView>
      </ExpenseProvider>
    </div>
  );
};

export default ExpenseModule;
