import { forwardRef, useCallback, useState } from 'react';
import DataGrid, {
  Pa<PERSON>,
  Scrolling,
  Selection,
  FilterRow,
  Too<PERSON>bar,
  Item,
  Column,
} from 'devextreme-react/data-grid';
import AttendeeTextBox from 'devextreme/ui/text_box';
import TextBox from 'devextreme-react/text-box';
import TagBox from 'devextreme-react/tag-box';
import Button from 'devextreme-react/button';
import DropDownBox from 'devextreme-react/drop-down-box';
import * as dayjs from 'dayjs';
import { getCustomValBasedOnLabel } from 'src/utils';
import { useDefaults } from '@contexts/defaults';
const dropDownOptions = { width: 500 };

const onCellPrepared = (e) => {
  if (e.rowType === 'filter' && e.columnIndex === 1) {
    e.cellElement.classList.add('filter-row-focus');
  }
};

const EmployeeDropDownBoxComponent = forwardRef(
  (
    { props, tagBoxDataSource, dataGridDataSource, isPopup, onAttendeesOpen },
    ref
  ) => {
    const disableAtt =
      props.data.data?.expCategoryBaseId !== 2 &&
      props.data.data?.expCategoryBaseId !== 3 &&
      !isPopup;

    const nonEmptyIdData = props.data.value
      .filter((item) => item.contactId != 0)
      .map((item) => {
        return {
          contactId: item.contactId,
          contactName: item.contactName,
        };
      });
    const emptyIdData = props.data.value
      .filter((item) => item.contactId == 0)
      .map((item, index) => {
        return {
          contactId: `AT_${dayjs().unix()}${index}`,
          contactName: item.contactName,
        };
      });

    const {
      defaults: { CUSTOM_LABELS },
    } = useDefaults();

    const labelId = ['IDS_JOB_TITLE'];
    const [customJobTitle] = getCustomValBasedOnLabel(CUSTOM_LABELS, labelId);
    const [selectedRowKeys, setSelectedRowKeys] = useState(nonEmptyIdData);
    const [custom, setCustom] = useState(emptyIdData);
    const [customData, setCustomData] = useState('');

    const onValueChanged = ({ value }) => {
      const nonEmptyData = value.filter(
        (item) => typeof item.contactId == 'number'
      );
      const emptyData = value.filter(
        (item) => typeof item.contactId == 'string'
      );
      setSelectedRowKeys(nonEmptyData);
      setCustom(emptyData);
      if (isPopup) props.data.setValue([...nonEmptyData, ...emptyData]);
    };
    const renderTagBox = (treeBoxValue, setTreeBoxValue) => () => {
      return (
        <div>
          <TextBox visible={false} />
          <TagBox
            dataSource={tagBoxDataSource}
            value={treeBoxValue}
            displayExpr="contactName"
            onValueChanged={onValueChanged}
            openOnFieldClick={false}
            width="100%"
            showClearButton={true}
            // hint="Clear All"
          />
        </div>
      );
    };

    const syncDataGridSelection = (e) => {
      if (e.value === null) {
        setSelectedRowKeys([]);
      }

      if (!isPopup) props.data.setValue(e.value || []);
    };

    const gridOpened = (e) => {
      setCustomData('');
      setTimeout(function () {
        const textBox = AttendeeTextBox.getInstance(
          document.querySelector('.filter-row-focus .dx-textbox')
        );
        textBox.option('value', '');
        textBox.focus();
      }, 0);
      isPopup && onAttendeesOpen();
    };

    const dataGridOnSelectionChanged = (e) => {
      setSelectedRowKeys(e.selectedRowsData);
    };
    const valueChanged = useCallback(({ value }) => {
      setCustomData(value);
    }, []);
    const handleClick = () => {
      if (customData) {
        const newData = customData?.toString().trim();
        if (newData === '') {
          setCustomData('');
          return;
        }
        const id = `AT_${dayjs().unix()}`;
        setCustom((prev) => [
          ...prev,
          {
            contactId: id,
            contactName: customData.trim(),
          },
        ]);
        setCustomData('');
      }
    };
    const onDropdownClose = () => {
      if (isPopup) {
        props.data.setValue([...selectedRowKeys, ...custom]);
        ref.current?.instance.close();
      }
    };
    return (
      <DropDownBox
        value={[...selectedRowKeys, ...custom]}
        deferRendering={false}
        displayExpr="contactName"
        dataSource={tagBoxDataSource}
        onValueChanged={syncDataGridSelection}
        fieldComponent={renderTagBox(
          [...selectedRowKeys, ...custom],
          setSelectedRowKeys
        )}
        onOpened={gridOpened}
        onClosed={onDropdownClose}
        dropDownOptions={dropDownOptions}
        ref={ref}
        disabled={disableAtt}
      >
        <DataGrid
          dataSource={dataGridDataSource}
          // columns={columns}
          hoverStateEnabled={true}
          remoteOperations={true}
          selectedRowKeys={selectedRowKeys.map((item) => item.contactId)}
          height={250}
          onSelectionChanged={dataGridOnSelectionChanged}
          onCellPrepared={onCellPrepared}
        >
          <Column dataField="contactName" caption="Contact Name" />
          <Column dataField="companyName" caption="Company Name" />
          <Column dataField="jobTitle" caption={customJobTitle} />
          <Selection
            mode="multiple"
            selectAllMode="page"
            allowSelectAll={false}
            showCheckBoxesMode="always"
          />
          <Scrolling mode="virtual" />
          <Paging enabled={true} defaultPageSize={10} />
          <FilterRow visible={true} />
          <Toolbar>
            <Item location="before">
              <TextBox
                value={customData}
                valueChangeEvent="keyup"
                maxLength={200}
                onValueChanged={valueChanged}
              />
            </Item>
            <Item location="before">
              <Button
                icon="add"
                hint="Add Attendee"
                type="default"
                onClick={handleClick}
              />
            </Item>
            <Item location="after">
              <Button
                icon="close"
                hint="Close "
                type="danger"
                onClick={onDropdownClose}
                visible={isPopup}
              />
            </Item>
          </Toolbar>
        </DataGrid>
      </DropDownBox>
    );
  }
);
export default EmployeeDropDownBoxComponent;
EmployeeDropDownBoxComponent.defaultProps = {
  isPopup: false,
};
