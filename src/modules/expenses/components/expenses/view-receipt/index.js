import { useState } from 'react';
import { Button, LoadIndicator, Popup } from 'devextreme-react';
import { confirm } from 'devextreme/ui/dialog';
import PreviewPdf from '@components/preview-pdf';
import { basePath } from 'next.config';
const getReceiptUrlByExtension = (url) => {
  const fileExtension = url
    .split('.')
    .pop()
    .substring(0, url.split('.').pop().indexOf('?'));
  return fileExtension === 'pdf' ? `${basePath}/pdf_logo.png` : url;
};

const deleteAttr = { id: 'delete-btn' };

const ViewReceipt = ({
  loading,
  images,
  imageLoaded,
  enableUpload,
  deleteExpenseReceipt,
}) => {
  const [previewPDF, setPreviewPDF] = useState(false);
  const [pdfURL, setPdfURL] = useState();
  const onAttachClick = (id) => {
    const previewURL = `${
      images.find((attachment) => attachment.id === id).imageUrl
    }`;
    setPdfURL(`${previewURL}#toolbar=0`);
    setPreviewPDF(true);
  };
  const deleteItem = (id) => () => {
    const result = confirm(
      'Please Confirm you wish to Delete Receipt?',
      'Delete Receipts'
    );
    result.then((dialogResult) => {
      if (dialogResult) {
        deleteExpenseReceipt(id);
      }
    });
  };
  const renderPopup = () => <PreviewPdf src={pdfURL} />;
  const onViewReceiptHide = () => setPreviewPDF(false);
  return (
    <>
      <label className="mt-2">Uploaded Receipts</label>
      <div style={{ display: loading ? 'block' : 'none' }}>
        <LoadIndicator id="medium-indicator" height={40} width={40} />
      </div>
      <div
        className=" mx-auto border-1 p-2 overflow-auto"
        style={{
          height: '200px',
          visibility: loading ? 'hidden' : 'visible',
        }}
      >
        {images.map((it) => {
          const url = getReceiptUrlByExtension(it.imageUrl);
          return (
            <div
              className="flex align-items-center flex-wrap"
              key={it.imageUrl}
            >
              <div
                className="flex align-items-center mx-auto mb-2"
                style={{ width: '80%' }}
              >
                <img
                  src={url}
                  width="50"
                  height="50"
                  alt="receipts"
                  onLoad={imageLoaded}
                  onClick={() => onAttachClick(it.id)}
                  className="cursor-pointer"
                  title="View receipt"
                />

                <Button
                  type="danger"
                  hint="Delete Receipt"
                  icon="trash"
                  className="p-button-outlined p-button-rounded p-button-danger ml-auto"
                  onClick={deleteItem(it.id)}
                  visible={enableUpload}
                  elementAttr={deleteAttr}
                />
              </div>
            </div>
          );
        })}
      </div>
      <Popup
        width={700}
        height="45rem"
        showTitle={true}
        title="View receipt"
        dragEnabled={false}
        onHiding={onViewReceiptHide}
        hideOnOutsideClick={false}
        visible={previewPDF}
        shadingColor="#00000090"
        contentRender={renderPopup}
      />
    </>
  );
};

export default ViewReceipt;
