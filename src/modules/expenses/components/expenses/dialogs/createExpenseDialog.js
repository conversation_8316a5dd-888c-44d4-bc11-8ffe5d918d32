import { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { Calendar } from 'primereact/calendar';
import { Toast } from 'primereact/toast';
import Compressor from 'compressorjs';
import { LoadPanel } from 'devextreme-react/load-panel';
import CustomStore from 'devextreme/data/custom_store';
import { Column } from 'devextreme-react/data-grid';
import {
  NumberBox,
  SelectBox,
  TextArea,
  TextBox,
  Button,
  Popup,
  ScrollView,
} from 'devextreme-react';
import DropDownTagBox from '@components/dropdownTagBox/DropdownTagBox';
import HotelDialogTemplate from './hotelDialog';
import MealDialogTemplate from './mealDialog';
import MilageDialogTemplate from './milageDialog';
import TransportationDialogTemplate from './transportationDialog';
import FormUpload from '../uploadForm';
import { ExpenseService } from '@services/expenseService';
import { getParamConfigRate } from '@services/api';
import { useDefaults } from '@contexts/defaults';
import { getCustomValBasedOnLabel } from 'src/utils';
import * as dayjs from 'dayjs';
import { AutoComplete } from 'primereact/autocomplete';
import { useExpense } from '@contexts/expense';

const position = { of: '#upload' };
function handleErrors(response) {
  if (response.status !== 200) {
    throw Error(response.statusText);
  }
  return response.data;
}

const expenseService = new ExpenseService();

const companyList = new CustomStore({
  key: 'companyId',
  loadMode: 'raw',
  load: async () => {
    return expenseService
      .getDropdowns('companies')
      .then(handleErrors)
      .catch(() => {
        throw new Error('Network Error');
      });
  },
});
const VALID_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/jpg',
  'image/bmp',
  'application/pdf',
];

const checkForValidAmtRecipt = (categoryData, expenseData, files, isEdit) => {
  const category = categoryData.find(
    (item) => item.REC_ID === expenseData.expCategoryId.REC_ID
  );
  if (!category) return false;
  const { CATEGORY_RECEIPT_REQ_FLAG, CATEGORY_AJLINK_AMT } = category;
  if (CATEGORY_RECEIPT_REQ_FLAG == 1) {
    const categoryAmount = Number(CATEGORY_AJLINK_AMT) || 0;
    const expenseAmount = Number(expenseData.expAmount) || 0;
    const condtionCheck =
      expenseAmount > categoryAmount &&
      (typeof files === 'undefined' ||
        !files ||
        (Array.isArray(files) && files?.length === 0));
    return isEdit
      ? condtionCheck && expenseData.expAttachments == 0
      : condtionCheck;
  }
  return false;
};

const CreateExpenseDialog = ({
  visible,
  onHide,
  expenses,
  modeData,
  subGrid,
  expenseReportRef,
  selectedExpense,
  merchantList,
  categoryList,
  userList,
  //  companyList,
  attendeeList,
  customTax,
  customTax1,
  refreshMerchant,
  HandleTypeChange,
}) => {
  userList = userList.__rawData;
  merchantList = merchantList.__rawData;
  categoryList =
    categoryList.__rawData &&
    categoryList.__rawData.map((item) => {
      return {
        REC_ID: item.REC_ID,
        CATEGORY_NAME: item.CATEGORY_NAME,
        CATEGORY_BASE_ID: item.CATEGORY_BASE_ID,
        CATEGORY_HIDE_FLAG: item.CATEGORY_HIDE_FLAG,
        CATEGORY_AJLINK_AMT: item.CATEGORY_AJLINK_AMT,
        CATEGORY_RECEIPT_REQ_FLAG: item.CATEGORY_RECEIPT_REQ_FLAG,
      };
    });
  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();
  const {
    defaults: { taxColumnVisibible },
  } = useExpense();

  const labelId = ['IDS_STATE'];
  const [customState] = getCustomValBasedOnLabel(CUSTOM_LABELS, labelId);
  const [expenseData, setExpenseData] = useState(expenses);
  const [fileUploadLoading, setFileUploadLoading] = useState(false);
  const [files, setFiles] = useState({});
  const [disableBtn, setDisableBtn] = useState(true);
  const [isValidate, setIsValidate] = useState(false);
  const [filteredMerchant, setFilteredMerchant] = useState(null);

  const textBox = useRef(null);
  const calendarRef = useRef(null);
  const toast = useRef(null);

  const scrollViewRef = useRef(null);
  const categoryDropdownRef = useRef(null);
  const createdForDropdownRef = useRef(null);
  const reimbursableRef = useRef(null);
  const merchantRef = useRef(null);
  const companiesRef = useRef(null);
  const attendeeRef = useRef(null);

  useEffect(() => {
    document.onscroll = () => {
      if (merchantRef.current) {
        merchantRef.current.hide();
      }
      if (calendarRef.current) {
        calendarRef.current.hide();
      }
    };
  }, []);

  useEffect(() => {
    if (
      expenses.expContacts?.filter(
        ({ contactId: id1 }) =>
          !expenseData.expContacts.some(({ contactId: id2 }) => id2 === id1)
      ).length > 0 ||
      expenseData.expContacts?.filter(
        ({ contactId: id1 }) =>
          !expenses.expContacts.some(({ contactId: id2 }) => id2 === id1)
      ).length > 0
    ) {
      const fetchCompany = async () => {
        const param = expenseData.expContacts
          .filter((contact) => typeof contact.contactId === 'number')
          .map((contact) => contact.contactId);
        const COMPANY_BY_ATTENDEE = await expenseService
          .attendeesCompany(param)
          .then(handleErrors);
        setExpenseData((prev) => {
          let uniqueIds = new Set();
          let uniqueArr = [...prev.expCompanies, ...COMPANY_BY_ATTENDEE].filter(
            (item) => {
              if (uniqueIds.has(item.companyId)) {
                return false;
              }
              uniqueIds.add(item.companyId);
              return true;
            }
          );
          return {
            ...prev,
            expCompanies: uniqueArr,
          };
        });
      };
      fetchCompany();
    }
  }, [expenseData.expContacts]);
  const companyValue = useMemo(() => {
    return expenseData.expCompanies?.map((item) => item.companyId);
  }, [expenseData.expCompanies]);

  useEffect(() => {
    if (
      modeData.mode === 'create' ||
      expenseData.expRate == 0 ||
      expenseData.expRate == null
    ) {
      getParamConfigRate()
        .then((rate) => {
          const milageRate = rate.data[0].value;
          setExpenseData((_expenseData) => ({
            ..._expenseData,
            expRate:
              isNaN(milageRate) || milageRate <= 0 ? 1 : parseFloat(milageRate),
          }));
        })
        .catch((error) => console.log('ooops :(', error));
    }
    expenseData.expDate =
      modeData.mode === 'updateMulti' ? [] : expenseData.expDate;
    expenseData.expReimbursable =
      modeData.mode === 'updateMulti' ? '' : expenseData.expReimbursable;
    expenseData.expCreatedForId &&
      expenseData.expDescription &&
      setDisableBtn(false);
  }, []);

  const searchMerchant = (event) => {
    setTimeout(() => {
      const results = merchantList.filter((merch) =>
        merch.merchant.toLowerCase().startsWith(event.query.toLowerCase())
      );
      setFilteredMerchant(results);
    }, 250);
  };

  const getDialogHeight = () => {
    return modeData.mode == 'create' || modeData.mode == 'update'
      ? '90vh'
      : 'auto';
  };

  const compressImage = (file) => {
    /* <2.5  
      2.5 - 6 MB to 40%
      6MB to 12MB - 50%
      > 12MB - 60%
    */
    let quality;
    if (file.size < 2500) {
      return file;
    } else if (file.size >= 2500 && file.size < 6000) {
      quality = 0.4;
    } else if (file.size >= 6000 && file.size < 12000) {
      quality = 0.5;
    } else {
      quality = 0.6;
    }

    return new Promise((resolve, reject) => {
      try {
        new Compressor(file, {
          quality: quality,
          success: (result) => {
            resolve(result);
          },
          error: (error) => reject(error),
        });
      } catch (error) {
        return error;
      }
    });
  };

  const uploadReciptMethod = async (e, id) => {
    if (e.files) {
      let skipLoopIfNotValidImage = false;
      const arr = Array.from(e.files);
      arr.forEach((file) => {
        if (!VALID_FILE_TYPES.includes(file.type)) {
          skipLoopIfNotValidImage += true;
        }
      });
      if (skipLoopIfNotValidImage) {
        toast.current.show({
          severity: 'error',
          summary: 'Invalid Image Format',
          detail:
            'Invalid file type. Only JPEG,JPG,BMP,PNG and PDF are allowed!',
          life: 3000,
        });
      } else {
        setFileUploadLoading(true);
        const compressPromises = [];
        for (const file of arr) {
          if (file.type === 'application/pdf') {
            compressPromises.push(file);
          } else {
            compressPromises.push(compressImage(file));
          }
        }
        Promise.all(compressPromises)
          .then((compressedFiles) => {
            const formData = new FormData();
            compressedFiles.forEach((compFile) => {
              let _file = compFile;
              if (compFile.type !== 'application/pdf') {
                _file = new File([compFile], compFile.name, {
                  type: compFile.type,
                  lastModified: compFile.lastModified,
                });
              }
              formData.append('image', _file);
            });
            formData.append('expId', id);
            expenseService.uploadImage(id, formData).then((response) => {
              if (response.status === 200) {
                if (modeData.mode === 'attach') {
                  toast.current.show({
                    severity: 'success',
                    summary: 'Successful',
                    detail: `File upload successful`,
                    life: 3000,
                    style: { zIndex: 2000 },
                  });
                  setTimeout(() => {
                    setFileUploadLoading(false);
                    onHide();
                  }, 3001);
                }
                subGrid.current.instance.refresh();
              }
            });
          })
          .catch((error) => console.log('ooops :(', error));
      }
    }
  };

  const onUpload = (e) => {
    if (modeData.mode === 'attach') {
      uploadReciptMethod(e, expenseData.expId);
    } else {
      if (e.objectURL) {
        const final = {
          files: files.files.filter((file) => e.objectURL !== file.objectURL),
        };
        setFiles(final);
      } else setFiles(e);
    }
  };

  const onDateChange = (e) => {
    let expDate;
    if (e?.CATEGORY_BASE_ID) {
      if (e.CATEGORY_BASE_ID === 1) {
        if (Array.isArray(expenseData.expDate)) {
          expDate = [
            dayjs(expenseData.expDate[0]).toDate(),
            dayjs(expenseData.expDate[1]).toDate(),
          ];
        } else {
          expDate = [
            dayjs(expenseData.expDate).toDate(),
            dayjs(expenseData.expDate).add(1, 'day').toDate(),
          ];
        }
      } else {
        if (Array.isArray(expenseData.expDate)) {
          expDate = dayjs(expenseData.expDate[0]).toDate();
        } else {
          expDate = dayjs(expenseData.expDate).toDate();
        }
      }
    }
    return expDate;
  };

  const onInputChange = useCallback(
    (e, name) => {
      setDisableBtn(false);
      if (name === 'expCategoryId') {
        e = categoryList.find((item) => item.REC_ID === e);
        if (e.CATEGORY_BASE_ID === 5) {
          const expAmount = expenseData.expMiles * expenseData.expRate;
          setExpenseData((_expenseData) => ({
            ..._expenseData,
            [name]: e,
            expAmount: expAmount,
          }));
        }
      } else if (name === 'expCreatedForId') {
        e = userList.find((item) => item.USER_ID === e);
      }
      // For Meal and Entertainment make 2 dates
      const expDate = onDateChange(e);
      if (expDate) {
        setExpenseData((_expenseData) => ({
          ..._expenseData,
          [name]: e,
          expDate: expDate,
        }));
      } else if (name == 'expMiles' && expenseData.expRate) {
        const expAmount = e * expenseData.expRate;
        setExpenseData((_expenseDataNew) => ({
          ..._expenseDataNew,
          [name]: e,
          expAmount: expAmount,
        }));
      } else {
        setExpenseData((_expenseData) => ({
          ..._expenseData,
          [name]: e,
        }));
      }
    },
    [expenseData]
  );

  const setAjValue = useCallback((value) => {
    onInputChange(value, 'expCompanies');
  }, []);

  const refreshData = (params) => {
    // Refresh the datagrid for changes effected and refresh Merchant List
    if (!params.expMerchantId && expenses.expMerchant != params.expMerchant) {
      refreshMerchant();
    }
    if (expenseReportRef) {
      expenseReportRef.current.instance.refresh();
    }
  };

  const setSaveParams = (data, createMode, saveExpData) => {
    if (createMode.mode === 'create') {
      return {
        ...data,
        expAmount: saveExpData.expAmount ?? 0,
        expCategoryId: saveExpData.expCategoryId.REC_ID,
        expCreatedForId: saveExpData.expCreatedForId.USER_ID,
        expDescription: saveExpData.expDescription?.toString().trim(),
        expStartDate:
          saveExpData.expCategoryId.CATEGORY_BASE_ID === 1
            ? dayjs(saveExpData.expDate[0]).format('MM-DD-YYYY')
            : dayjs(saveExpData.expDate).format('MM-DD-YYYY'),
        expEndDate:
          saveExpData.expCategoryId.CATEGORY_BASE_ID === 1
            ? dayjs(saveExpData.expDate[1] ?? saveExpData.expDate[0]).format(
                'MM-DD-YYYY'
              )
            : dayjs(saveExpData.expDate).format('MM-DD-YYYY'),
        expNights: saveExpData.expNights,
        expFromPlace: saveExpData.expFromPlace?.toString().trim(),
        expToPlace: saveExpData.expToPlace?.toString().trim(),
        expMiles: saveExpData.expMiles,
        expRate: saveExpData.expRate,
        expTravelMode: saveExpData.expTravelMode?.toString().trim(),
        expReimbursable: saveExpData.expReimbursable,
        expReportId: saveExpData.expReportId,
        expMerchantId:
          saveExpData.expCategoryId.CATEGORY_BASE_ID === 5
            ? ''
            : data.expMerchantId ?? 0,
        expMerchant:
          saveExpData.expCategoryId.CATEGORY_BASE_ID === 5
            ? ''
            : data.expMerchant,
        expTax1: saveExpData.expTax1,
        expTax: saveExpData.expTax,
      };
    }
  };

  const saveExpense = () => {
    setIsValidate(false);
    setDisableBtn(true);
    const data = {};
    let isInvalid=false

    if (modeData.mode === 'update' || modeData.mode === 'create') {
     isInvalid = checkForValidAmtRecipt(
        categoryList,
        expenseData,
        files?.files,
        modeData.mode === 'update'
      );
    }

    if (typeof expenseData.expMerchant === 'object') {
      data.expMerchantId = expenseData.expMerchant.id;
      data.expMerchant = expenseData.expMerchant.merchant;
    } else if (typeof expenseData.expMerchant === 'string') {
      data.expMerchant = expenseData.expMerchant.trim();
      data.expMerchantId = '';
    }

    if (expenseData.expContacts.length > 0) {
      data.expContacts = expenseData.expContacts.map((itemContact) => {
        return {
          contactId:
            typeof itemContact.contactId !== 'number'
              ? 0
              : itemContact.contactId,
          contactName: itemContact.contactName,
        };
      });
    }
    if (expenseData.expCompanies.length > 0) {
      data.expCompanies = expenseData.expCompanies.map((itemComp) => {
        return {
          companyId: itemComp.companyId,
          companyName: itemComp.companyName,
        };
      });
    } else {
      data.expCompanies = [];
    }

    const params = setSaveParams(data, modeData, expenseData);

    expenseService.saveExpenseDetails(params).then((response) => {
      if (response.status === 200) {
        if (isInvalid) {
          toast.current.show({
            severity: 'error',
            summary: 'Receipt Required',
            detail: 'Uploading receipts is mandatory',
            life: 3000,
          });
        }
        if (Object.keys(files).length) {
          uploadReciptMethod(files, response.data);
        } else {
          subGrid.current.instance.refresh();
        }
        refreshData(params);
        HandleTypeChange();
        setTimeout(onHide, 3001);
      }
    });
  };

  const setFormatParams = (element) => {
    if (element.expMerchant && typeof element.expMerchant === 'object') {
      element.expMerchantId = element.expMerchant.id;
      element.expMerchant = element.expMerchant.merchant;
    } else if (element.expMerchant && typeof element.expMerchant === 'string') {
      element.expMerchantId = '';
      element.expMerchant = element.expMerchant.trim();
    }
    if (element.expContacts && element.expContacts.length > 0) {
      element.expContacts = element.expContacts.map((item) => {
        return {
          contactId: typeof item.contactId !== 'number' ? 0 : item.contactId,
          contactName: item.contactName,
        };
      });
    }
    if (element.expCompanies && element.expCompanies.length > 0) {
      element.expCompanies = element.expCompanies.map((item) => {
        return {
          companyId: item.companyId,
          companyName: item.companyName,
        };
      });
    } else {
      element.expCompanies = [];
    }
    return element;
  };

  const customCompany = (element, updateExpData) => {
    if (updateExpData.expCompanies?.length > 0) {
      return updateExpData.expCompanies.map((itemNew) => {
        return {
          companyId: itemNew.companyId,
          companyName: itemNew.companyName,
        };
      });
    } else {
      if (modeData.mode == 'updateMulti') {
        return element.expCompanies;
      } else {
        return [];
      }
    }
  };

  const setUpdateParams = (element, updateExpData) => {
    updateExpData.expDate[0] =
      updateExpData.expDate.length == undefined
        ? updateExpData.expDate
        : updateExpData.expDate[0];

    return {
      id: element.id,
      expAmount:
        modeData.mode === 'updateMulti' && updateExpData.expAmount == 0
          ? element.expAmount
          : updateExpData.expAmount ?? 0,
      expCategoryId:
        modeData.mode === 'update'
          ? element.expCategoryId.REC_ID
          : element.expCategoryId,
      expCreatedForId:
        modeData.mode === 'update'
          ? element.expCreatedForId.USER_ID
          : element.expCreatedForId,
      expMerchantId:
        element.expCategoryId.CATEGORY_BASE_ID === 5
          ? ''
          : element.expMerchantId ?? 0,
      expMerchant:
        element.expCategoryId.CATEGORY_BASE_ID === 5 ? '' : element.expMerchant,
      expContacts: element.expContacts,
      expCompanies: customCompany(element, updateExpData),
      expDescription:
        updateExpData.expDescription?.toString().trim() === ''
          ? element.expDescription?.toString().trim()
          : updateExpData.expDescription?.toString().trim(),
      expStartDate:
        updateExpData.expDate[0] == undefined
          ? element.expStartDate
          : dayjs(updateExpData.expDate[0]).format('MM-DD-YYYY'),
      expEndDate:
        updateExpData.expDate[0] == undefined
          ? element.expEndDate
          : dayjs(updateExpData.expDate[1] ?? updateExpData.expDate[0]).format(
              'MM-DD-YYYY'
            ),
      expNights: element.expNights,
      expFromPlace: element.expFromPlace?.toString().trim(),
      expToPlace: element.expToPlace?.toString().trim(),
      expMiles: element.expMiles,
      expRate: element.expRate,
      expTravelMode: element.expTravelMode?.toString().trim(),
      expReimbursable:
        updateExpData.expReimbursable === ''
          ? element.expReimbursable
          : updateExpData.expReimbursable,
      expId: element.expId,
      expTax1:
        modeData.mode === 'updateMulti' && updateExpData.expTax1 == 0
          ? element.expTax1
          : updateExpData.expTax1 ?? 0,
      expTax:
        modeData.mode === 'updateMulti' && updateExpData.expTax == 0
          ? element.expTax
          : updateExpData.expTax ?? 0,
    };
  };

  const updateExpense = () => {
    setIsValidate(false);
    setDisableBtn(true);
    let isInvalid = false;
    const data = modeData.mode === 'update' ? [expenseData] : selectedExpense;

    const params = data.map((element) => {
      const newElement = setFormatParams(element);
      return setUpdateParams(newElement, expenseData);
    });
    if (modeData.mode === 'update' || modeData.mode === 'create') {
       isInvalid = checkForValidAmtRecipt(
        categoryList,
        expenseData,
        files?.files,
        modeData.mode === 'update'
      );
    }
    expenseService.updateExpenseDetails(params).then((response) => {
      if (response.status === 200) {
        if (isInvalid) {
          toast.current.show({
            severity: 'error',
            summary: 'Receipt Required',
            detail: 'Uploading receipts is mandatory',
            life: 3000,
          });
        
          setTimeout(() => {
            toast.current.show({
              severity: 'success',
              summary: 'Successful',
              detail: `Expense updated Successfully`,
              life: 3000,
            });
          }, 1000); 
        } else {
          toast.current.show({
            severity: 'success',
            summary: 'Successful',
            detail: `Expense updated Successfully`,
            life: 3000,
          });
        }
        if (Object.keys(files).length) {
          uploadReciptMethod(files, expenseData.expId);
        } else {
          subGrid.current.instance.refresh();
        }
        refreshData(params[0]);
        setTimeout(() => {
          onHide();
        }, 3001);
      }
    });

    subGrid.current.instance.deselectAll();
  };

  const validationClicked = () => {
    setIsValidate(true);
    textBox.current.instance.option('value', '');
    textBox.current?.instance.focus();
  };
  const onOpened = () => setDisableBtn(true);
  const updateButton = () => {
    if (modeData.mode === 'create') {
      return (
        <Button
          type="default"
          text="Save"
          icon="pi pi-check"
          onClick={
            expenseData.expDescription?.toString().trim() == ''
              ? validationClicked
              : saveExpense
          }
          className="bg-primary mr-2"
          disabled={
            !expenseData.expCreatedForId ||
            !expenseData.expDescription ||
            disableBtn
          }
        />
      );
    } else {
      return (
        <Button
          type="default"
          text="Update"
          icon="pi pi-check"
          onClick={
            modeData.mode !== 'updateMulti' &&
            expenseData.expDescription?.toString().trim() == ''
              ? validationClicked
              : updateExpense
          }
          className="bg-primary mr-2"
          disabled={disableBtn}
        />
      );
    }
  };
  const createDialogFooter = (
    <>
      {updateButton()}
      <Button
        type="danger"
        text="Cancel"
        icon="pi pi-times"
        className="bg-error mr-2"
        onClick={onHide}
      />
    </>
  );

  const onPopupScrolled = useCallback(() => {
    categoryDropdownRef.current?.instance.close();
    createdForDropdownRef.current?.instance.close();
    calendarRef.current?.hide();
    reimbursableRef.current?.instance.close();
    merchantRef.current?.hide();
    attendeeRef.current?.instance.close();
    companiesRef.current?.instance.close();
  }, []);

  const renderCategoryItems = () => {
    let filteredCategoryList = categoryList;
    if (modeData.mode === 'create') {
      filteredCategoryList = categoryList.filter(
        (item) => item.CATEGORY_HIDE_FLAG !== 1
      );
    } else if (modeData.mode === 'update') {
      const selectedCategoryId = expenseData.expCategoryId.REC_ID;

      filteredCategoryList = categoryList.filter((item) => {
        if (
          item.REC_ID === selectedCategoryId &&
          item.CATEGORY_HIDE_FLAG === 1
        ) {
          return true;
        }

        return item.CATEGORY_HIDE_FLAG !== 1;
      });
    }
    return (
      <>
        <div className="formgrid grid m-0">
          <div className="field col">
            <label htmlFor="expCategoryId">Category</label>
            <SelectBox
              dataSource={filteredCategoryList}
              displayExpr="CATEGORY_NAME"
              valueExpr="REC_ID"
              value={expenseData.expCategoryId?.REC_ID}
              onValueChanged={(e) => onInputChange(e.value, 'expCategoryId')}
              autoFocus
              ref={categoryDropdownRef}
            />
          </div>
        </div>
        <div className="formgrid grid m-0">
          <div className="field col">
            <label htmlFor="expCreatedBy">Created By</label>

            <TextBox value={expenseData.expCreatedBy} readOnly={true} />
          </div>
          <div className="field col">
            <label htmlFor="expCreatedForId">Created For *</label>

            <SelectBox
              dataSource={userList}
              displayExpr="USER_NAME"
              valueExpr="USER_ID"
              value={expenseData.expCreatedForId?.USER_ID}
              onValueChanged={(e) => onInputChange(e.value, 'expCreatedForId')}
              placeholder="Select User"
              searchEnabled={true}
              searchMode="contains"
              searchExpr="USER_NAME"
              searchTimeout={200}
              minSearchLength={0}
              showDataBeforeSearch={true}
              focusStateEnabled={true}
              ref={createdForDropdownRef}
              disabled={expenseData.expReportId !== 0}
            />
          </div>
        </div>

        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID !== 5 && (
            <div className="formgrid grid m-0">
              <div className="field col">
                <label htmlFor="expMerchant">Merchant</label>
                <br />
                <AutoComplete
                  value={expenseData.expMerchant}
                  suggestions={filteredMerchant}
                  completeMethod={searchMerchant}
                  field="merchant"
                  dropdown
                  placeholder="Select Merchant"
                  onChange={(e) => onInputChange(e.value, 'expMerchant')}
                  aria-label="User List"
                  ref={merchantRef}
                />
              </div>
            </div>
          )}

        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID === 1 && (
            <HotelDialogTemplate data={expenseData} onChange={onInputChange} />
          )}
        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID === 2 && (
            <MealDialogTemplate
              data={expenseData}
              onChange={onInputChange}
              attendeeRef={attendeeRef}
              onAttendeesOpen={onOpened}
            />
          )}
        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID === 3 && (
            <MealDialogTemplate
              data={expenseData}
              onChange={onInputChange}
              attendeeRef={attendeeRef}
              onAttendeesOpen={onOpened}
            />
          )}
        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID === 4 && (
            <TransportationDialogTemplate
              data={expenseData}
              onChange={onInputChange}
            />
          )}
        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID === 5 && (
            <MilageDialogTemplate data={expenseData} onChange={onInputChange} />
          )}
      </>
    );
  };

  const renderCommonItems = () => {
    return (
      <>
        <div className="formgrid grid m-0">
          <div className="field col-12">
            <label htmlFor="date">Date</label>
            <Calendar
              id="range"
              ref={calendarRef}
              value={expenseData.expDate}
              onChange={(e) => {
                onInputChange(e.value, 'expDate');
              }}
              selectionMode={
                modeData.mode !== 'updateMulti' &&
                expenseData.expCategoryId.CATEGORY_BASE_ID === 1
                  ? 'range'
                  : 'single'
              }
              placeholder="MM/DD/YYYY"
              showIcon
              showOnFocus={false}
              panelClassName="text-sm"
              todayButtonClassName="m-0 "
              clearButtonClassName="m-0 p-button-danger "
              onSelect={() => {
                if (expenseData.expDate[1] === null)
                  calendarRef.current.hide(2);
              }}
              readOnlyInput={true}
            />
          </div>
          <div className={`field ${taxColumnVisibible ? 'col-6' : 'col'}`}>
            <label htmlFor="expAmount">Amount</label>

            <NumberBox
              value={parseFloat(expenseData.expAmount)}
              onValueChanged={(e) => onInputChange(e.value, 'expAmount')}
              format="$ #,##0.00"
              min={0}
              max={9999999999}
              readOnly={
                modeData.mode !== 'updateMulti' &&
                expenseData.expCategoryId?.CATEGORY_BASE_ID === 5
              }
            />
          </div>
          <div className={`field ${taxColumnVisibible ? 'col-3' : 'hidden'}`}>
            <label htmlFor="expTax1">{customTax1}</label>

            <NumberBox
              value={parseFloat(expenseData.expTax1)}
              onValueChanged={(e) => onInputChange(e.value, 'expTax1')}
              format="$ #,##0.00"
              min={0}
              max={9999999999}
            />
          </div>
          <div className={`field ${taxColumnVisibible ? 'col-3' : 'hidden'}`}>
            <label htmlFor="expTax">{customTax}</label>

            <NumberBox
              value={parseFloat(expenseData.expTax)}
              onValueChanged={(e) => onInputChange(e.value, 'expTax')}
              format="$ #,##0.00"
              min={0}
              max={9999999999}
            />
          </div>
        </div>
        <div className="formgrid grid m-0">
          <div className="field col">
            <label htmlFor="expCompanies">Companies</label>
            <br />

            <DropDownTagBox
              dataSource={companyList}
              paramId="companyId"
              paramValue="companyName"
              values={companyValue}
              setValue={setAjValue}
              placeholder="Select Companies"
              ref={companiesRef}
            >
              <Column dataField="companyName" caption="Name" />
              <Column dataField="companyAddress" caption="Address" />
              <Column dataField="companyCity" caption={customState} />
            </DropDownTagBox>
          </div>
        </div>
        <div className="formgrid grid m-0">
          <div className="field col">
            <label htmlFor="expDescription">
              Description {modeData.mode !== 'updateMulti' && '*'}
            </label>
            <br />

            <TextArea
              value={expenseData.expDescription}
              ref={textBox}
              onValueChanged={(e) => onInputChange(e.value, 'expDescription')}
              onKeyUp={() => {
                setIsValidate(false);
              }}
              maxLength={200}
              placeholder="Enter Description"
              required={true}
              minHeight="60px"
              autoResizeEnabled={true}
              className={isValidate ? 'border-red-500' : ''}
              name="expDescription"
              valueChangeEvent="keyup"
            />
          </div>
        </div>
        <div className="formgrid grid m-0">
          <div className="field col">
            <label htmlFor="expReimbursable">Reimbursable</label>

            <SelectBox
              dataSource={[
                {
                  label: 'Yes',
                  value: 1,
                },
                {
                  label: 'No',
                  value: 0,
                },
              ]}
              displayExpr="label"
              valueExpr="value"
              value={expenseData.expReimbursable}
              onValueChanged={(e) => onInputChange(e.value, 'expReimbursable')}
              placeholder="Select Option"
              ref={reimbursableRef}
            />
          </div>
        </div>
      </>
    );
  };

  const renderPopup = () => {
    return (
      <>
        <Toast ref={toast} />
        <ScrollView
          width="100%"
          height="100%"
          onScroll={onPopupScrolled}
          ref={scrollViewRef}
        >
          <div className="mt-2 mb-6 pr-3">
            {modeData.attach === false &&
              modeData.mode !== 'updateMulti' &&
              renderCategoryItems()}
            {modeData.attach === false && renderCommonItems()}
            {modeData.mode !== 'updateMulti' && (
              <>
                <FormUpload
                  expenseData={expenseData}
                  onUpload={onUpload}
                  mode={modeData.mode}
                  enableUpload={modeData.enableUpload}
                  subGrid={subGrid}
                  files={files}
                  categoryData={categoryList}
                  toast={toast}
                />
              </>
            )}
          </div>
        </ScrollView>
        <div className="p-dialog-footer popup text-right">
          {modeData.attach === false && createDialogFooter}
        </div>
        {modeData.mode === 'attach' && (
          <LoadPanel
            shadingColor="transparent"
            position={position}
            visible={fileUploadLoading}
            showIndicator={true}
            shading={true}
            showPane={true}
            hideOnOutsideClick={false}
          />
        )}
      </>
    );
  };

  return (
    <>
      <Popup
        width={500}
        height={getDialogHeight}
        // height="auto"
        maxHeight="100%"
        showTitle={true}
        title={modeData.title}
        dragEnabled={false}
        hideOnOutsideClick={false}
        visible={visible}
        onHiding={onHide}
        shadingColor="#00000090"
        contentRender={renderPopup}
      />
    </>
  );
};

export default CreateExpenseDialog;
