import { DataGrid, Column } from 'devextreme-react/data-grid';
import Popup from 'devextreme-react/popup';
import ScrollView from 'devextreme-react/scroll-view';

import { useDefaults } from '@contexts/defaults';
import { convertDateTimeFormat } from 'src/utils';

const cellRender = (data) => {
  const approverNames = data.value
    .map((approver) => approver.approver)
    .join(', ');
  return <span>{approverNames}</span>;
};

const SummaryDialog = ({ confirmMode, data, visible, onHide, footer }) => {
  const {
    defaults: { DATE_FORMAT, TIME_FORMAT },
  } = useDefaults();
  const dateCellTemplate = ({ value }) =>
    value ? convertDateTimeFormat(value, DATE_FORMAT, TIME_FORMAT) : '';
  const renderPopup = () => {
    return (
      <>
        <ScrollView width="100%" height="100%">
          <div className="confirmation-content flex mt-3">
            {/* <i className="pi pi-check-circle mr-2" style={{ fontSize: '3rem' }} /> */}
            {data && (
              <div className="field col">
                {confirmMode.type === 'summary' ? (
                  <DataGrid
                    dataSource={data}
                    allowColumnReordering={true}
                    rowAlternationEnabled={true}
                    showBorders={true}
                    remoteOperations={false}
                    allowColumnResizing={true}
                    columnAutoWidth={true}
                    hoverStateEnabled={true}
                    wordWrapEnabled={true}
                    className="mb-4"
                  />
                ) : (
                  <DataGrid
                    dataSource={data}
                    allowColumnReordering={true}
                    rowAlternationEnabled={true}
                    showBorders={true}
                    remoteOperations={false}
                    allowColumnResizing={true}
                    columnAutoWidth={true}
                    hoverStateEnabled={true}
                    wordWrapEnabled={true}
                    className="mb-4"
                  >
                    <Column dataField="status" caption="Status" />
                    <Column dataField="submittedBy" caption="Submitted By" />
                    <Column
                      dataField="submittedTo"
                      caption="Submitted To"
                      cellRender={cellRender}
                    />
                    <Column
                      dataField="submittedDate"
                      caption="Submit Date"
                      customizeText={dateCellTemplate}
                    />
                    <Column dataField="approvedBy" caption="App/Rej/Paid By" />
                    <Column
                      dataField="approvedDate"
                      caption="App/Rej/Paid Date"
                      customizeText={dateCellTemplate}
                    />
                    <Column
                      dataField="comments"
                      caption="Comments"
                      width={250}
                    />
                  </DataGrid>
                )}
              </div>
            )}
          </div>
          {footer && (
            <div className="p-dialog-footer popup text-right">{footer}</div>
          )}
        </ScrollView>
      </>
    );
  };

  return (
    <>
      <Popup
        width={confirmMode.width}
        height="auto"
        showTitle={true}
        title={confirmMode.header}
        dragEnabled={false}
        hideOnOutsideClick={true}
        visible={visible}
        onHiding={onHide}
        shadingColor="#00000090"
        contentRender={renderPopup}
      />
      <style global jsx>{`
        .dx-datagrid > .dx-datagrid-headers {
          position: -webkit-sticky;
          position: sticky;
          background-color: #fff;
          z-index: 1;
          top: 0px;
        }

        .dx-popup-content {
          min-height: 100px !important;
          padding: 0 !important;
        }
        .dx-scrollable {
          max-height: 500px;
        }
      `}</style>
    </>
  );
};

export default SummaryDialog;
