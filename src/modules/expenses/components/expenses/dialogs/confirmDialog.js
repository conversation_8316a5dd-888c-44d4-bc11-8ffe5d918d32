import { DataGrid, Column } from 'devextreme-react/data-grid';
import Popup from 'devextreme-react/popup';
import { useCallback, useState } from 'react';

import { Button } from 'devextreme-react/button';
import { TextArea } from 'devextreme-react';
import { amountFormat } from 'src/constants';

const approversCellTemplate = ({ data }) => {
  const approverNames = data.approver
    .map((approver) => approver.approver)
    .join(', ');
  return <span>{approverNames}</span>;
};

const ConfirmDialog = ({ confirmMode, data, visible, onHide, onSave }) => {
  const [comment, setComment] = useState('');

  const onCommentChange = useCallback((e) => setComment(e.value), []);
  const renderPopup = () => {
    return (
      <>
        <div className="confirmation-content flex mt-3 mb-7">
          <i className="pi pi-check-circle mr-2" style={{ fontSize: '3rem' }} />
          {data && (
            <div className="field col">
              {confirmMode.status !== 0 && (
                <DataGrid
                  dataSource={[data]}
                  hoverStateEnabled={true}
                  className="mb-4"
                  remoteOperations={false}
                  wordWrapEnabled
                >
                  <Column dataField="description" width={200} />
                  <Column
                    dataField="total"
                    format={amountFormat}
                    dataType="number"
                  />
                  {confirmMode.status === 1 && (
                    <Column
                      dataField="approver"
                      caption="Approver"
                      cellRender={approversCellTemplate}
                    />
                  )}
                </DataGrid>
              )}
              <span className="flex align-items-center">
                {confirmMode.message}
              </span>

              {confirmMode.status !== 1 && confirmMode.status !== 0 && (
                <div className="mt-4">
                  <TextArea
                    value={comment}
                    onValueChanged={onCommentChange}
                    valueChangeEvent="keyup"
                    maxLength={200}
                    placeholder="Enter Comments"
                    minHeight="60px"
                    autoResizeEnabled={true}
                  />
                </div>
              )}
            </div>
          )}
        </div>
        <div className="p-dialog-footer popup text-right">
          <Button
            text={confirmMode.status === 1 ? 'Submit' : 'Yes'}
            icon="pi pi-check"
            type="default"
            className="bg-primary mr-2"
            onClick={(e) => onSave(e, data, comment)}
            disabled={confirmMode.status === 3 && comment === ''}
          />
          <Button
            text={confirmMode.status === 1 ? 'Cancel' : 'No'}
            icon="pi pi-times"
            type="danger"
            className="bg-error mr-2"
            onClick={onHide}
          />
        </div>
      </>
    );
  };

  return (
    <>
      <Popup
        width={550}
        height="auto"
        showTitle={true}
        title={confirmMode.header}
        dragEnabled={false}
        hideOnOutsideClick={false}
        visible={visible}
        onHiding={onHide}
        shadingColor="#00000090"
        contentRender={renderPopup}
      />
    </>
  );
};

export default ConfirmDialog;
