import { useMemo } from 'react';
import EmployeeDropDownBoxComponent from '../dropDownComponent';
import CustomStore from 'devextreme/data/custom_store';
import { ExpenseService } from '@services/expenseService';

const expenseService = new ExpenseService();

function handleErrors(response) {
  if (response.status !== 200) {
    throw Error(response.statusText);
  }
  return response.data;
}

const attendees = new CustomStore({
  key: 'contactId',
  loadMode: 'raw',
  load: () => {
    return expenseService
      .getDropdowns('contacts')
      .then(handleErrors)
      .catch(() => {
        throw new Error('Network Error');
      });
  },
});

const MealDialog = ({ data, onChange, attendeeRef, onAttendeesOpen }) => {
  const attendeeData = useMemo(() => {
    return {
      data: {
        value: data.expContacts,
        setValue: (e) => {
          onChange(e, 'expContacts');
        },
      },
    };
  }, []);
  return (
    <>
      <div className="formgrid grid m-0">
        <div className="field col">
          <label htmlFor="attendees">Attendees</label>
          <br />
          <EmployeeDropDownBoxComponent
            props={attendeeData}
            tagBoxDataSource={attendees}
            dataGridDataSource={attendees}
            isPopup
            ref={attendeeRef}
            onAttendeesOpen={onAttendeesOpen}
          />
        </div>
      </div>
    </>
  );
};

export default MealDialog;
