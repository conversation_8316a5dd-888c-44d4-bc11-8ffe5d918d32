import { NumberBox, TextBox } from 'devextreme-react';

const fromPlaceAttr = { id: 'fromPlace' };
const toPlaceAttr = { id: 'toPlace' };
const milesAttr = { id: 'miles' };
const rateAttr = { id: 'rate' };

const MilageDialog = ({ data, onChange }) => {
  return (
    <>
      <div className="formgrid grid m-0">
        <div className="field col">
          <label htmlFor="expFromPlace">From Place</label>
          <TextBox
            value={data.expFromPlace}
            id="expFromPlace"
            className="p-inputtext-sm"
            placeholder="Enter From Place"
            maxLength={100}
            onValueChanged={(e) => onChange(e.value, 'expFromPlace')}
            elementAttr={fromPlaceAttr}
          />
        </div>
        <div className="field col">
          <label htmlFor="expToPlace">To Place</label>
          <TextBox
            value={data.expToPlace}
            id="expToPlace"
            className="p-inputtext-sm"
            placeholder="Enter From Place"
            maxLength={100}
            onValueChanged={(e) => onChange(e.value, 'expToPlace')}
            elementAttr={toPlaceAttr}
          />
        </div>
      </div>
      <div className="formgrid grid m-0">
        <div className="field col">
          <label htmlFor="expMiles">Miles</label>
          <NumberBox
            value={parseFloat(data.expMiles)}
            onValueChanged={(e) => onChange(e.value, 'expMiles')}
            min={0}
            max={999999}
            format="#0 mi"
            elementAttr={milesAttr}
          />
        </div>
        <div className="field col">
          <label htmlFor="expRate">Rate</label>
          <NumberBox
            value={parseFloat(data.expRate)}
            min={0}
            max={999999}
            readOnly
            elementAttr={rateAttr}
          />
        </div>
      </div>
    </>
  );
};

export default MilageDialog;
