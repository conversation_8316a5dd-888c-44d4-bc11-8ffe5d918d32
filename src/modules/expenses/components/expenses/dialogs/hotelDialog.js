import { NumberBox } from 'devextreme-react';

const nightsAttr = { id: 'nights' };

const HotelDialog = ({ data, onChange, disabled = false }) => {
  return (
    <>
      <div className="formgrid grid m-0">
        <div className="field col">
          <label htmlFor="expNights">Nights</label>
          <NumberBox
            min={0}
            max={99}
            value={parseInt(data.expNights)}
            onValueChanged={(e) => onChange(e.value, 'expNights')}
            elementAttr={nightsAttr}
            disabled={disabled}
          />
        </div>
      </div>
    </>
  );
};

export default HotelDialog;