import { TextBox } from 'devextreme-react';

const fromPlaceAttr = { id: 'fromPlace' };
const toPlaceAttr = { id: 'toPlace' };
const travelModeAttr = { id: 'travelMode' };

const TransportationDialog = ({ data, onChange }) => {
  return (
    <>
      <div className="formgrid grid m-0">
        <div className="field col">
          <label htmlFor="expFromPlace">From Place</label>
          <TextBox
            value={data ? data.expFromPlace : ''}
            id="expFromPlace"
            className="p-inputtext-sm"
            placeholder="Enter From Place"
            maxLength={100}
            onValueChanged={(e) => onChange(e.value, 'expFromPlace')}
            elementAttr={fromPlaceAttr}
          />
        </div>
        <div className="field col">
          <label htmlFor="expToPlace">To Place</label>

          <TextBox
            value={data ? data.expToPlace : ''}
            id="expToPlace"
            className="p-inputtext-sm"
            placeholder="Enter To Place"
            maxLength={100}
            onValueChanged={(e) => onChange(e.value, 'expToPlace')}
            elementAttr={toPlaceAttr}
          />
        </div>
      </div>
      <div className="formgrid grid m-0">
        <div className="field col">
          <label htmlFor="expTravelMode">Form of Travel</label>
          <TextBox
            value={data ? data.expTravelMode : ''}
            id="expTravelMode"
            className="p-inputtext-sm"
            placeholder="Enter Travel Mode"
            maxLength={100}
            onValueChanged={(e) => onChange(e.value, 'expTravelMode')}
            elementAttr={travelModeAttr}
          />
        </div>
      </div>
    </>
  );
};

export default TransportationDialog;
