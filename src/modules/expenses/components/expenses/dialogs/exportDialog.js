import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Popup from 'devextreme-react/popup';
import ScrollView from 'devextreme-react/scroll-view';
import { Button, SelectBox } from 'devextreme-react';
import CustomStore from 'devextreme/data/custom_store';
import { Column } from 'devextreme-react/data-grid';
import { Calendar } from 'primereact/calendar';
import { Toast } from 'primereact/toast';
import { ExpenseService } from '@services/expenseService';
import { handleErrors } from '@modules/medical/utils';
import DropDownTagBox from '@components/dropdownTagBox/DropdownTagBox';
import { useMenu } from '@contexts/menu';
import * as dayjs from 'dayjs';
import { exportFormatList } from 'src/constants';

const expenseService = new ExpenseService();

const userList = new CustomStore({
  key: 'USER_ID',
  loadMode: 'raw',
  load: () => {
    return expenseService
      .getAllUserList()
      .then(handleErrors)
      .catch(() => {
        throw Error('Network Error');
      });
  },
});

const ExportDialog = ({ visible, onHide }) => {
  const { user } = useMenu();
  const calendarRef = useRef(null);
  const toast = useRef(null);
  const [exportData, setExportData] = useState({
    date: [dayjs().add(-30, 'day').toDate(), dayjs().toDate()],
    category: 0,
    user: !user.isOwner || !user.isAdmin ? [{ USER_ID: user.userId }] : [],
    isUnFormatted: 1,
  });
  const [categoryList, setCategoryList] = useState([]);
  const newCategory = { REC_ID: 0, CATEGORY_NAME: 'All' };

  useEffect(() => {
    document.onscroll = () => {
      if (calendarRef.current) {
        calendarRef.current.hide();
      }
    };
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      const categoryData = await (
        await expenseService.getExpenseCategory()
      ).data;
      setCategoryList([...categoryData, newCategory]);
    };
    fetchData();
  }, []);

  const onInputChange = (e, name) => {
    setExportData((_expData) => ({
      ..._expData,
      [name]: e,
    }));
  };

  const companyValue = useMemo(() => {
    return exportData.user?.map((item) => item.USER_ID);
  }, [exportData.user]);

  const setUserValue = useCallback((value) => {
    onInputChange(value, 'user');
  }, []);

  const exportExpense = () => {
    const params = {
      fromDate: dayjs(exportData.date[0]).format('MM-DD-YYYY'),
      toDate: exportData.date[1]
        ? dayjs(exportData.date[1]).format('MM-DD-YYYY')
        : dayjs(exportData.date[0]).format('MM-DD-YYYY'),
      category: exportData.category,
      user: exportData.user.map((data) => data.USER_ID).join(', '),
      isUnFormatted: exportData.isUnFormatted,
    };
    expenseService
      .exportToExcel(params)
      .then((response) => {
        if (response.status === 200) {
          const excelURL = window.URL.createObjectURL(
            new Blob([response.data])
          );
          const link = document.createElement('a');
          link.href = excelURL;
          link.download = 'Expense_Report.xlsx';
          document.body.appendChild(link);
          link.click();
          window.URL.revokeObjectURL(excelURL);
        } else if (response.status === 204) {
          toast.current.show({
            severity: 'error',
            summary: 'Data not found',
            detail:
              'The date you have chosen have no records to download. Please review the date range and resubmit.',
            life: 3000,
          });
        }
      })
      .catch(() => {
        toast.current.show({
          severity: 'error',
          summary: 'Excel download failed',
          detail: 'Please try after sometime',
          life: 3000,
        });
      });
  };

  const footer = (
    <>
      <Button
        type="default"
        text="Submit"
        icon="pi pi-check"
        onClick={exportExpense}
        className="bg-primary mr-2"
        disabled={!exportData.date}
      />
      <Button
        type="danger"
        text="Cancel"
        icon="pi pi-times"
        className="bg-error mr-2"
        onClick={onHide}
      />
    </>
  );

  const renderPopup = () => {
    return (
      <>
        <ScrollView width="100%" height="100%">
          <div className="confirmation-content mt-3">
            <div className="formgrid grid m-0 ">
              <div className="field col-12">
                <label htmlFor="date">Date</label>
                <Calendar
                  id="range"
                  ref={calendarRef}
                  value={exportData.date}
                  onChange={(e) => onInputChange(e.value, 'date')}
                  selectionMode="range"
                  placeholder="MM/DD/YYYY"
                  showIcon
                  showOnFocus={false}
                  panelClassName="text-sm"
                  todayButtonClassName="m-0 "
                  clearButtonClassName="m-0 p-button-danger "
                  readOnlyInput={true}
                  onSelect={() => {
                    if (exportData?.date[1] === null)
                      calendarRef.current.hide(2);
                  }}
                />
              </div>
              <div className="field col-6">
                <label htmlFor="category">Category</label>
                <SelectBox
                  dataSource={categoryList}
                  displayExpr="CATEGORY_NAME"
                  valueExpr="REC_ID"
                  value={exportData.category}
                  onValueChanged={(e) => onInputChange(e.value, 'category')}
                  placeholder="Select Category"
                />
              </div>
              <div className="field col-6 user-field">
                <label htmlFor="user">User</label>
                <DropDownTagBox
                  dataSource={userList}
                  paramId="USER_ID"
                  paramValue="USER_NAME"
                  values={companyValue}
                  setValue={setUserValue}
                  placeholder="All"
                  disabled={!user.isOwner || !user.isAdmin}
                >
                  <Column dataField="USER_NAME" caption="Name" />
                </DropDownTagBox>
              </div>
              <div className="field col-6">
                <label htmlFor="format">Format</label>
                <SelectBox
                  dataSource={exportFormatList}
                  displayExpr="value"
                  valueExpr="id"
                  value={exportData.isUnFormatted}
                  onValueChanged={(e) =>
                    onInputChange(e.value, 'isUnFormatted')
                  }
                  placeholder="Select Format"
                />
              </div>
            </div>
          </div>
          {footer && (
            <div className="p-dialog-footer popup text-right">{footer}</div>
          )}
        </ScrollView>
      </>
    );
  };

  return (
    <>
      <Toast ref={toast} baseZIndex="1000" />
      <Popup
        width="400"
        height="320"
        showTitle={true}
        title="Export Expense Report"
        dragEnabled={false}
        hideOnOutsideClick={false}
        visible={visible}
        onHiding={onHide}
        shadingColor="#00000090"
        contentRender={renderPopup}
      />
    </>
  );
};

export default ExportDialog;
