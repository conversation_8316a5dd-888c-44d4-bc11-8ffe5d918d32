import Popup from 'devextreme-react/popup';

const DeleteRecordDialog = ({ header, message, visible, onHide, footer }) => {
  const renderPopup = () => {
    return (
      <>
        <div className="confirmation-content flex my-2">
          <i
            className="pi pi-exclamation-triangle mx-3"
            style={{ fontSize: '3rem' }}
          />
          <span className="flex align-items-center">{message}</span>
        </div>

        <div className="p-dialog-footer popup text-right">{footer}</div>
      </>
    );
  };
  return (
    <Popup
      width={550}
      height={170}
      showTitle={true}
      title={header}
      dragEnabled={false}
      hideOnOutsideClick={true}
      visible={visible}
      onHiding={onHide}
      shadingColor="#00000090"
      contentRender={renderPopup}
    />
  );
};

export default DeleteRecordDialog;
