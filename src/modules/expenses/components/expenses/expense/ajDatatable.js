/** Next JS and React JS imports */
import { useRef, useMemo, useCallback } from 'react';

/** PrimeReact imports */
import { Toast } from 'primereact/toast';

/** DevExtreme imports */
import DataGrid, { Column } from 'devextreme-react/data-grid';

import CustomStore from 'devextreme/data/custom_store';

/** Custom Service */
import { ExpenseService } from '@services/expenseService';
import { useDefaults } from '@contexts/defaults';
import { getCustomValBasedOnLabel } from 'src/utils';

import * as dayjs from 'dayjs';
const utc = require('dayjs/plugin/utc');
dayjs.extend(utc);

/** Utility Function */
function handleErrors(response) {
  if (response.status !== 200) {
    throw Error(response.statusText);
  }
  return response.data;
}

const expenseService = new ExpenseService();

let dataGrid;
function onInitialized(e) {
  dataGrid = e.component;
}

const AJDatatable = ({ reportId, subGrid }) => {
  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();

  const [customActJournal, customPrincipal] = getCustomValBasedOnLabel(
    CUSTOM_LABELS,
    ['IDS_ACT_JOURNAL', 'IDS_PRINCI']
  );

  const toast = useRef(null);

  const ajData = useMemo(
    () =>
      new CustomStore({
        key: 'id',
        load: () => {
          return expenseService
            .getActivityJournalDetails(reportId)
            .then(handleErrors)
            .catch(() => []);
        },
      }),
    [reportId]
  );

  const customizeDate = useCallback((cellInfo, index) => {
    return (
      <span>
        {cellInfo.value && dayjs.utc(cellInfo.value).format('MM-DD-YYYY')}
      </span>
    );
  }, []);

  return (
    <div className="datatable-rowexpansion-demo sub-table mt-1 mb-3">
      <Toast ref={toast} position="top-right" />
      <DataGrid
        id={reportId === 0 ? 'grid-container' : 'expanded-row'}
        ref={subGrid}
        dataSource={ajData}
        allowColumnReordering={true}
        rowAlternationEnabled={true}
        showBorders={true}
        remoteOperations={false}
        allowColumnResizing={true}
        columnAutoWidth={true}
        noDataText={`No ${customActJournal} Found`}
        onInitialized={onInitialized}
        // onEditorPreparing={onEditorPrepare}
        // onCellPrepared={onCellPrepared}
        // visible={!loading}
      >
        <Column dataField="id" caption="ID" visible={false} />
        <Column width={150} dataField="principal" caption={customPrincipal} />
        <Column width={200} dataField="comments" caption="Comments" />
        <Column dataField="followup" cellRender={customizeDate} />
      </DataGrid>
    </div>
  );
};

export default AJDatatable;
