/** Next JS and React JS imports */
import { useState, useRef, useCallback, useMemo } from 'react';

/** PrimeReact imports */
import { Toast } from 'primereact/toast';

/** DevExtreme imports */
import DataGrid, {
  Column,
  Selection,
  Editing,
  Toolbar,
  Item,
  Button,
  Lookup,
  FilterRow,
  HeaderFilter,
  RequiredRule,
  SearchPanel,
  Summary,
  TotalItem,
  StringLengthRule,
  Scrolling,
} from 'devextreme-react/data-grid';
import { DateBox, SelectBox, Button as DxButton } from 'devextreme-react';
import DataSource from 'devextreme/data/data_source';
import CustomStore from 'devextreme/data/custom_store';

/** Other Third party packages */
import * as dayjs from 'dayjs';

/** Custom Hooks and components */
import EmployeeDropDownBoxComponent from '../dropDownComponent';
import LinkExpenseDialog from './dialogs/linkExpense';
import LinkExpenseAJDialog from './dialogs/linkExpenseAJ';
import CreateExpenseDialog from '../dialogs/createExpenseDialog';
import DeleteRecordDialogTemplate from '../dialogs/deleteRecordDialog';
import { useExpense } from '@contexts/expense';
import { useMenu } from '@contexts/menu';

/** Custom Service */
import { ExpenseService } from '@services/expenseService';
import { getParamConfigRate } from '@services/api';
import { useDefaults } from '@contexts/defaults';
import { getCustomValBasedOnLabel } from 'src/utils';
import {
  amountFormat,
  reimbursableStatus,
  summaryFormat,
  typeList,
} from 'src/constants';

/** Utility Function */
function handleErrors(response) {
  if (response.status !== 200) {
    throw Error(response.statusText);
  }
  return response.data;
}

const todaysDate = dayjs().format('MM-DD-YYYY');
const tomorrowsDate = dayjs().add(1, 'day').format('MM-DD-YYYY');
let dataGrid;
const getRowData = async (ref, keyValue) => {
  const dataGrid = ref.current?.instance;
  if (dataGrid) {
    return dataGrid.getDataByKeys([keyValue]);
  }
};
const attendeeCellTemplate = (data) => {
  const text = (data.value || [])

    .map((element) => {
      return element.contactName;
    })
    .join(', ');
  return <>{text}</>;
};
const itemRender = (data) => {
  if (data !== null) {
    return (
      <div>
        {/* <img src={imageSource} className="status-icon middle"></img> */}
        <span className="middle">{data.name}</span>
      </div>
    );
  }
  return <span className="middle">Yes</span>;
};
const reimbursableEditorRender = (props) => {
  const onValueChanged = (e) => {
    props.setValue(e.value);
  };
  return (
    <SelectBox
      defaultValue={props.value}
      {...props.column.lookup}
      onValueChanged={onValueChanged}
      itemRender={itemRender}
    />
  );
};
const expenseService = new ExpenseService();
const categoryList = new CustomStore({
  key: 'REC_ID',
  loadMode: 'raw',
  load: () => {
    return expenseService
      .getExpenseCategory()
      .then(handleErrors)
      .catch(() => {
        throw Error('Network Error');
      });
  },
});
const userList = new CustomStore({
  key: 'USER_ID',
  loadMode: 'raw',
  load: () => {
    return expenseService
      .getAllUserList()
      .then(handleErrors)
      .catch(() => {
        throw Error('Network Error');
      });
  },
});

const merchantList = new CustomStore({
  key: 'id',
  loadMode: 'raw',
  load: () => {
    return expenseService
      .getMerchants()
      .then(handleErrors)
      .catch(() => {
        throw Error('Network Error');
      });
  },
});
const attendeeList = new CustomStore({
  key: 'contactId',
  loadMode: 'raw',
  load: async () => {
    return expenseService
      .getDropdowns('contacts')
      .then(handleErrors)
      .catch(() => {
        throw Error('Network Error');
      });
  },
});

function calculateFilterExpression(filterValue, target) {
  if (target === 'search' && typeof filterValue === 'string') {
    return [this.dataField, 'contains', filterValue];
  }
  return function (data) {
    return (
      (data.expContacts.map((d) => d.contactId) || []).indexOf(filterValue) !==
      -1
    );
  };
}

const dateCellTemplate = (cellInfo) => {
  if (cellInfo.expCategoryBaseId === 1) {
    return `${dayjs(cellInfo.expStartDate, 'MM-DD-YYYY').format(
      'MM-DD-YYYY'
    )} - ${dayjs(cellInfo.expEndDate, 'MM-DD-YYYY').format('MM-DD-YYYY')}`;
  } else {
    return `${dayjs(cellInfo.expStartDate, 'MM-DD-YYYY').format(
      'MM-DD-YYYY'
    )} `;
  }
};

const merchantCellValue = (cellInfo) => {
  return cellInfo.data.expMerchant;
};

const buttonAttach = (e) => {
  return (
    <DxButton
      icon="upload"
      type="default"
      className={
        e.data.data.expAttachments !== 0 ? 'bg-green-600 ' : 'bg-red-500'
      }
    />
  );
};

const disableUpload = ['OPEN', 'REJECTED'];

const ExpenseDatatable = ({
  reportId,
  subGrid,
  status,
  expenseReport,
  dataKey,
  expenseReportRef,
  reportStatus,
}) => {
  const rpStatus = reportId == 0 ? 'OPEN' : reportStatus;
  const { defaults, onValueChange } = useExpense();

  const {
    expenseDefaultType,
    category,
    milageRate,
    taxColumnVisibible,
    expenseNonReimbursableParam,
  } = defaults;

  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();

  const [customActJournal, customTax1, customTax] = getCustomValBasedOnLabel(
    CUSTOM_LABELS,
    ['IDS_ACT_JOURNAL', 'IDS_EXP_TAX1', 'IDS_EXP_TAX2']
  );

  const [linkDialog, setLinkDialog] = useState(false);
  const [linkAJDialog, setLinkAJDialog] = useState(false);
  const [ExpenseDialog, setExpenseDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [expenseData, setExpenseData] = useState([]);
  const [modeData, setModeData] = useState([]);
  const [selectedExpense, setSelectedExpense] = useState([]);
  const [reportData, setReportData] = useState([]);
  const { user } = useMenu();

  const toast = useRef(null);

  const emptyData = {
    expStartDate: todaysDate,
    expEndDate: tomorrowsDate,
    expCategoryId: 1,
    expCreatedForId: 1,
    expAmount: 0,
    expReportId: 0,
    expReimbursable: expenseNonReimbursableParam?.userParamStatus == 1 ? 0 : 1,
    expLinkedDocs: [],
    expDescription: '',
  };

  // My Expenses option in the TypeList should be visible only for owner
  const typeListNew = typeList.filter((item) =>
    user.isOwner != 1 ? item.value != 'myExpenses' : item
  );

  function onInitialized(e) {
    dataGrid = e.component;
  }

  const onEditorPrepare = (e) => {
    if (e.parentType === 'dataRow') {
      if (e.dataField === 'expDescription') {
        // your column to set character limit.
        e.editorOptions.maxLength = 200;
      } else if (e.dataField === 'expAmount') {
        // your column to set character limit.
        e.editorOptions.max = *********;
        // Disable Amount Column Edit for Mileage category
        if (e.row.data.expCategoryBaseId === 5) {
          e.editorOptions.disabled = true;
        }
      } else if (e.dataField === 'expCreatedForId' && reportId != 0) {
        e.editorOptions.disabled = true;
      } else if (e.dataField === 'expCategoryId') {
        const clonedLookups = JSON.parse(JSON.stringify(e.lookup.items));
        e.editorOptions.dataSource = clonedLookups.filter((item) => {
          if (
            item.REC_ID === e.row.data.expCategoryId &&
            item.CATEGORY_HIDE_FLAG === 1
          ) {
            return true;
          }
          return item.CATEGORY_HIDE_FLAG !== 1;
        });
      }
    }
  };

  let emptyExpenseData = {
    expReportId: 0,
    expAmount: 0,
    expTax1: 0,
    expTax: 0,
    expCategoryId: {
      REC_ID: 1,
      CATEGORY_NAME: 'Hotel',
      CATEGORY_BASE_ID: 1,
    },
    expCreatedBy: user.userName,
    expCreatedForId: { USER_ID: 1, USER_NAME: 'Admin' },
    expDescription: '',
    expNights: 0,
    expFromPlace: '',
    expToPlace: '',
    expMiles: 0,
    expRate: isNaN(milageRate) || milageRate <= 0 ? 1 : parseFloat(milageRate),
    expTravelMode: '',
    expMerchantId: '',
    expMerchant: '',
    expReimbursable: expenseNonReimbursableParam?.userParamStatus == 1 ? 0 : 1,
    expContacts: [],
    expCompanies: [],
  };

  const onInitNewRow = (e) => {
    for (const key in emptyData) {
      e.data[key] = emptyData[key];
    }
  };

  const addExpense = () => {
    const _emptyExpenseData = {
      ...emptyExpenseData,
      expCategoryId: {
        REC_ID: category.REC_ID,
        CATEGORY_NAME: category.CATEGORY_NAME,
        CATEGORY_BASE_ID: category.CATEGORY_BASE_ID,
      },
      category,
      expCreatedBy: user.userName,
      expCreatedForId: { USER_ID: user.userId, USER_NAME: user.userName },
      expDate:
        category.CATEGORY_BASE_ID === 1
          ? [
              dayjs.utc(todaysDate, 'MM-DD-YYYY').toDate(),
              dayjs.utc(tomorrowsDate, 'MM-DD-YYYY').toDate(),
            ]
          : dayjs.utc(todaysDate, 'MM-DD-YYYY').toDate(),
    };
    setExpenseData(_emptyExpenseData);
    setModeData({
      mode: 'create',
      attach: false,
      title: 'Create Expense',
      enableUpload: isUploadEnabled,
    });
    setExpenseDialog(true);
  };

  const linkExpense = (e) => {
    setReportData({
      description: ``,
      date: [
        dayjs.utc(todaysDate, 'MM-DD-YYYY').toDate(),
        dayjs.utc(tomorrowsDate, 'MM-DD-YYYY').toDate(),
      ],
      total: 0,
      isLink: [],
    });
    if (e.row) {
      setReportData((prev) => {
        return {
          ...prev,
          createdForId: e.row.data.expCreatedForId,
          createdFor: e.row.data.expCreatedFor,
        };
      });
      setExpenseData(e.row.data);
      setLinkDialog(true);
    } else {
      dataGrid
        .getSelectedRowsData()
        .then((rowData) => {
          return rowData;
        })
        .then((data) => {
          if (data.length > 0) {
            const createdForIds = data.map((item) => item.expCreatedForId);
            const arrayResult = createdForIds.every(
              (item) => item == createdForIds[0]
            );
            if (!arrayResult) {
              toast.current.show({
                severity: 'error',
                summary: 'Error',
                detail: `Please Select rows with same owner`,
                life: 10000,
              });
              subGrid.current.instance.deselectAll();
              return;
            } else {
              setReportData((prev) => {
                return {
                  ...prev,
                  createdForId: data[0].expCreatedForId,
                  createdFor: data[0].expCreatedFor,
                };
              });
              setExpenseData(data);
              setLinkDialog(true);
            }
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Please Select rows for linking`,
              life: 10000,
            });
          }
        });
    }
  };

  const linkReportMethod = async (reportIdItem) => {
    try {
      setLinkDialog(false);

      const data = expenseData.length ? expenseData : [expenseData];

      const params = data.map((exp) => ({
        id: exp.id,
        expReportId: reportIdItem,
      }));
      const { data: linkExpenseValidate } =
        await expenseService.linkExpenseValidation({
          expIds: data.map((exp) => exp.expId),
        });


      const response = await expenseService.linkExpenseDetails(params);

      if (response.status === 200) {

      if (linkExpenseValidate?.isError) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: linkExpenseValidate.message,
          life: 3000,
        });
        setTimeout(() => {
          toast.current.show({
            severity: 'success',
            summary: 'Successful',
            detail: `Expense Linked successfully`,
            life: 3000,
          })},1000)
      }else{
        toast.current.show({
          severity: 'success',
          summary: 'Successful',
          detail: `Expense Linked successfully`,
          life: 3000,
        });
      }
        // Refresh the datagrid for changes effected
        subGrid.current.instance.refresh();
        subGrid.current.instance.deselectAll();
        setSelectedExpense([]);
      }
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Something went wrong while linking expenses.',
        life: 5000,
      });
    }
  };

  const linkExpenseAJ = useCallback((e) => {
    if (e.row) {
      setExpenseData(e.row.data);
      setLinkAJDialog(true);
    } else {
      dataGrid
        .getSelectedRowsData()
        .then((rowData) => {
          return rowData;
        })
        .then((data) => {
          if (data.length > 0) {
            const isAlreadyLinked = data.some(
              (item) => item.expLinkedDocs.length > 0
            );
            if (isAlreadyLinked) {
              toast.current.show({
                severity: 'error',
                summary: 'Error',
                detail: `Already linked record exist`,
                life: 10000,
              });
            } else {
              setExpenseData(data);
              setLinkAJDialog(true);
            }
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Please Select rows for linking`,
              life: 10000,
            });
          }
        });
    }
  }, []);

  const linkAJMethod = (ajParams) => {
    setLinkAJDialog(false);
    let params = [],
      newParam = [];
    const data = !expenseData.length ? [expenseData] : expenseData;

    newParam = data.map((exp) => {
      if (ajParams.length) {
        params = ajParams.map((item) => {
          return {
            expId: exp.expId,
            expActivityJournalId: item.expActivityJournalId,
            expDocType: item.expDocType,
            compId: item.compId,
            contId: item.contId,
            contName: item.contName,
          };
        });
      } else {
        params = [{ expId: exp.expId }];
      }
      return params;
    });
    newParam = newParam.map((o) => o).flat();

    expenseService.linkActivityJournal(newParam).then((response) => {
      if (response.status === 200) {
        toast.current.show({
          severity: 'success',
          summary: 'Successful',
          detail: `Expense Linked to AJ successfully`,
          life: 3000,
        });
        subGrid.current.instance.refresh();
      }
    });
    subGrid.current.instance.deselectAll();
    setSelectedExpense([]);
  };

  const attachExpense = (e) => {
    setExpenseData({ expId: e.row.data.expId,expCategoryId: {REC_ID: e.row.data.expCategoryId, },expAmount:e.row.data.expAmount});
    setModeData({
      mode: 'attach',
      attach: true,
      title: isUploadEnabled ? 'Attach Receipts' : 'View Receipts',
      enableUpload: isUploadEnabled,
    });
    setExpenseDialog(true);
  };

  const editExpense = (e) => {
    if (e.row) {
      setSelectedExpense([]);
      const _emptyExpenseData = {
        ...emptyExpenseData,
        expReportId: e.row.data.expReportId,
        expCategoryId: {
          REC_ID: e.row.data.expCategoryId,
          CATEGORY_NAME: e.row.data.expCategory,
          CATEGORY_BASE_ID: e.row.data.expCategoryBaseId,
        },
        expAmount: parseFloat(e.row.data.expAmount ?? 0),

        expDescription: e.row.data.expDescription,
        expCreatedBy: e.row.data.expCreatedBy,
        expCreatedForId: {
          USER_ID: e.row.data.expCreatedForId,
          USER_NAME: e.row.data.expCreatedFor,
        },
        expDate:
          e.row.data.expCategoryBaseId === 1
            ? [
                dayjs.utc(e.row.data.expStartDate, 'MM-DD-YYYY').toDate(),
                dayjs.utc(e.row.data.expEndDate, 'MM-DD-YYYY').toDate(),
              ]
            : dayjs.utc(e.row.data.expStartDate, 'MM-DD-YYYY').toDate(),
        expNights: e.row.data.expNights,
        expFromPlace: e.row.data.expFromPlace,
        expToPlace: e.row.data.expToPlace,
        expMiles: parseFloat(e.row.data.expMiles ?? 0),
        expRate: parseFloat(e.row.data.expRate ?? 0),
        expTravelMode: e.row.data.expTravelMode,
        expMerchantId: e.row.data.expMerchantId,
        expMerchant: e.row.data.expMerchant,
        expReimbursable: e.row.data.expReimbursable,
        expContacts: e.row.data.expContacts,
        expCompanies: e.row.data.expCompanies,
        expId: e.row.data.expId,
        id: e.row.data.id,
        expTax1: e.row.data.expTax1,
        expTax: e.row.data.expTax,
        expAttachments: e.row.data.expAttachments,
      };
      setExpenseData(_emptyExpenseData);
      setModeData({
        mode: 'update',
        attach: false,
        title: 'Edit Expense',
        enableUpload: isUploadEnabled,
      });
      setExpenseDialog(true);
    } else {
      dataGrid
        .getSelectedRowsData()
        .then((rowData) => {
          return rowData;
        })
        .then((data) => {
          if (data.length > 0) {
            const _emptyExpenseData = {
              ...emptyExpenseData,
              expCategoryId: {
                REC_ID: category.REC_ID,
                CATEGORY_NAME: category.CATEGORY_NAME,
                CATEGORY_BASE_ID: category.CATEGORY_BASE_ID,
              },
              expDate: [
                dayjs.utc(todaysDate, 'MM-DD-YYYY').toDate(),
                dayjs.utc(tomorrowsDate, 'MM-DD-YYYY').toDate(),
              ],
            };
            setExpenseData(_emptyExpenseData);
            setSelectedExpense(data);
            setModeData({
              mode: 'updateMulti',
              attach: false,
              title: 'Edit Multiple Expense',
              enableUpload: isUploadEnabled,
            });
            setExpenseDialog(true);
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Please Select rows for editing`,
              life: 10000,
            });
          }
        });
    }
  };

  const refreshMerchant = () => {
    const merchants = new DataSource({
      store: merchantList,
    });
    merchants.reload();
    merchantList.load();
    subGrid.current.instance.refresh();
  };

  const confirmDelete = (e) => {
    if (e.row) {
      setExpenseData([e.row.data]);
      setDeleteDialog(true);
    } else {
      dataGrid
        .getSelectedRowsData()
        .then((rowData) => {
          return rowData;
        })
        .then((data) => {
          if (data.length > 0) {
            setExpenseData(data);
            setDeleteDialog(true);
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Please Select rows for deleting`,
              life: 10000,
            });
          }
        });
    }
  };

  const resetDelete = () => {
    // Refresh the datagrid for changes effected

    subGrid.current.instance.refresh();
    subGrid.current.instance.deselectAll();
    if (expenseReportRef) {
      expenseReportRef.current.instance.refresh();
    }
    setDeleteDialog(false);
    setSelectedExpense([]);
  };

  const deleteRecords = (link = 'delete') => {
    const params = {
      expenseIds: expenseData.map((item) => item.id),
      expUnlink: link == 'unlink' ? 1 : 0,
    };
    return expenseService.deleteExpenseDetails(params).then((response) => {
      if (response.status === 200) {
        toast.current.show({
          severity: 'success',
          summary: 'Successful',
          detail: `Expense deleted Successfully`,
          life: 3000,
        });
      }
      resetDelete();
    });
  };

  const reportDialogTemplate = (
    <>
      <DxButton
        icon="pi pi-link"
        type="danger"
        text="Unlink"
        className="bg-primary mr-2"
        onClick={() => {
          deleteRecords('unlink');
        }}
        visible={expenseData[0] && expenseData[0].expReportId}
      />

      <DxButton
        icon="pi pi-check"
        type="danger"
        text="Delete"
        className="bg-error mr-2"
        onClick={deleteRecords}
      />
      <DxButton
        icon="pi pi-times"
        type="default"
        text="Cancel"
        className="bg-yellow-600 mr-2"
        onClick={() => {
          setDeleteDialog(false);
        }}
      />
    </>
  );

  const customAttendeList = attendeeList;

  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');

  const minDateChangeHandler = useCallback(
    (e) => {
      if (e.value) setFromDate(e.value);
      if (e.value && toDate < e.value) setToDate(e.value);
    },
    [fromDate, toDate]
  );
  const maxDateChangeHandler = useCallback((e) => {
    setToDate(e.value);
  }, []);

  const clearFilter = () => {
    subGrid.current.instance.clearFilter();
    setFromDate('');
    setToDate('');
  };

  const fetchCompany = async (expCompanies, expContacts) => {
    const param = expContacts
      .filter(
        (contact) =>
          typeof contact.contactId === 'number' && contact.contactId !== 0
      )
      .map((contact) => contact.contactId);
    const COMPANY_BY_ATTENDEE = await expenseService
      .attendeesCompany(param)
      .then(handleErrors);
    let uniqueIds = new Set();
    return [...expCompanies, ...COMPANY_BY_ATTENDEE].filter((item) => {
      if (uniqueIds.has(item.companyId)) {
        return false;
      }
      uniqueIds.add(item.companyId);
      return true;
    });
  };

  const expense = useMemo(
    () =>
      new CustomStore({
        key: 'id',
        load: () => {
          return expenseService
            .getExpenseList(
              reportId ? 'all' : expenseDefaultType,
              reportId,
              fromDate && toDate ? dayjs(fromDate).format('MM-DD-YYYY') : '',
              fromDate && toDate ? dayjs(toDate).format('MM-DD-YYYY') : ''
            )
            .then(handleErrors)
            .catch(() => []);
        },

        update: async (key, values) => {
          const row = subGrid.current.instance.cellValue(
            subGrid.current.instance.getRowIndexByKey(key),
            'date'
          );
          const expMerchantId = subGrid.current.instance.cellValue(
            subGrid.current.instance.getRowIndexByKey(key),
            'expMerchantId'
          );
          const expId = subGrid.current.instance.cellValue(
            subGrid.current.instance.getRowIndexByKey(key),
            'expId'
          );
          let expCompanies = subGrid.current.instance.cellValue(
            subGrid.current.instance.getRowIndexByKey(key),
            'expCompanies'
          );
          const { expAttachments, expAmount } = await getRowData(
            subGrid,
            key
          ).then((data) => ({
            expAttachments: data[0].expAttachments,
            expAmount: data[0].expAmount,
          }));
          const expCategoryId = subGrid.current.instance.cellValue(
            subGrid.current.instance.getRowIndexByKey(key),
            'expCategoryId'
          );
          const params = {
            id: key,
            ...values,
          };

          if (values.hasOwnProperty('date')) {
            const [start, end] = values.date.split(' - ');
            params.expStartDate = dayjs(start, 'MM-DD-YYYY').format(
              'MM-DD-YYYY'
            );
            params.expEndDate =
              end != 'Invalid Date'
                ? dayjs(end, 'MM-DD-YYYY').format('MM-DD-YYYY')
                : dayjs(start, 'MM-DD-YYYY').add(1, 'day').format('MM-DD-YYYY');
            delete params.date;
          } else {
            const [start, end] = row.split(' - ');
            params.expStartDate = dayjs(start, 'MM-DD-YYYY').format(
              'MM-DD-YYYY'
            );
            params.expEndDate = end
              ? dayjs(end, 'MM-DD-YYYY').format('MM-DD-YYYY')
              : dayjs(start, 'MM-DD-YYYY').add(1, 'day').format('MM-DD-YYYY');
          }

          if (values.hasOwnProperty('expContacts')) {
            if (
              values.expContacts.length > 0 &&
              !values.expContacts.some(
                (item) => typeof item.contactId === 'string'
              )
            ) {
              params.expContacts = values.expContacts.map((id) => {
                return {
                  contactId: id.contactId,
                  contactName: id.contactName,
                };
              });
            } else if (
              values.expContacts.length > 0 &&
              values.expContacts.some(
                (item) => typeof item.contactId === 'string'
              )
            ) {
              params.expContacts = values.expContacts.map((id) => {
                return {
                  contactId:
                    typeof id.contactId !== 'number' ? 0 : id.contactId,
                  contactName: id.contactName,
                };
              });
            } else {
              params.expContacts = [];
            }
            expCompanies = await fetchCompany(expCompanies, params.expContacts);
            params.expCompanies = expCompanies.length > 0 ? expCompanies : [];
          }
          if (
            values.hasOwnProperty('expMerchantId') &&
            values.expMerchantId !== null
          ) {
            params.expMerchantId = values.expMerchantId;
            params.expMerchant = merchantList.__rawData?.find(
              (item) => item.id === values.expMerchantId && item.merchant
            )?.merchant;
          } else {
            params.expMerchantId = expMerchantId;
            if (params.expMerchantId == null) {
              params.expMerchant = '';
            }
          }
          if (values.hasOwnProperty('expAmount')) {
            const category = categoryList.__rawData.find(
              (item) => item.REC_ID == expCategoryId
            );
            const { CATEGORY_RECEIPT_REQ_FLAG, CATEGORY_AJLINK_AMT } = category;
            if (CATEGORY_RECEIPT_REQ_FLAG == 1) {
              const categoryAmount = Number(CATEGORY_AJLINK_AMT) || 0;
              const expenseAmount = Number(values.expAmount) || 0;

              if (expenseAmount > categoryAmount && expAttachments == 0) {
                toast.current.show({
                  severity: 'error',
                  summary: 'Error',
                  detail: `Uploading receipts is mandatory`,
                  life: 3000,
                });
              }
            }
          }
          if (values.hasOwnProperty('expAmount') && reportId != 0) {
            const amount = subGrid.current.instance
              .getVisibleRows()
              .map((item) => Number(item.data.expAmount))
              .reduce((x, y) => x + y);
            expenseReport.update(dataKey, { total: amount }).then(() => {
              expenseReportRef.current.instance.refresh();
            });
          }
          if (values.hasOwnProperty('expDescription')) {
            params.expDescription = values.expDescription?.toString().trim();
          }
          if (values.hasOwnProperty('expMerchant')) {
            params.expMerchant = values.expMerchant?.toString().trim();
          }
          if (params.expEndDate == 'Invalid Date') {
            params.expEndDate = params.expStartDate;
          }
          if (values.hasOwnProperty('expCategoryId')) {
            const category = categoryList.__rawData.find(
              (item) => item.REC_ID == params.expCategoryId
            );
            const { CATEGORY_RECEIPT_REQ_FLAG, CATEGORY_AJLINK_AMT } = category;
            if (CATEGORY_RECEIPT_REQ_FLAG == 1) {
              const categoryAmount = Number(CATEGORY_AJLINK_AMT) || 0;
              const expenseAmount = Number(expAmount) || 0;

              if (expenseAmount > categoryAmount && expAttachments == 0) {
                toast.current.show({
                  severity: 'error',
                  summary: 'Error',
                  detail: `Uploading receipts is mandatory`,
                  life: 3000,
                });
              }
            }
          }
          if (
            values.hasOwnProperty('expCategoryId') &&
            categoryList.__rawData.find(
              (item) => item.REC_ID == params.expCategoryId
            ).CATEGORY_BASE_ID === 5
          ) {
            const expMiles = subGrid.current.instance.cellValue(
              subGrid.current.instance.getRowIndexByKey(key),
              'expMiles'
            );
            const expRate = subGrid.current.instance.cellValue(
              subGrid.current.instance.getRowIndexByKey(key),
              'expRate'
            );
            const expAmount = expMiles * expRate;
            params.expAmount = expAmount;
            params.expMerchantId = '';
            params.expMerchant = '';
          }
          params.expId = expId;
          return expenseService
            .updateExpenseDetails([params])

            .then((response) => {
              if (response.status === 200) {
                if (expenseReportRef) {
                  expenseReportRef.current.instance.refresh();
                }
              }
            })

            .catch(() => {
              throw Error('Network Error');
            });
        },
        remove: (key) => {
          const params = {
            expenseIds: [key],
          };
          return expenseService
            .deleteExpenseDetails(params)
            .then((response) => {
              if (response.status === 200) {
                toast.current.show({
                  severity: 'success',
                  summary: 'Successful',
                  detail: `Expense deleted Successfully`,
                  life: 3000,
                });
                if (expenseReportRef) {
                  expenseReportRef.current.instance.refresh();
                }
              }
            });
        },
        insert: (values) => {
          return expenseService
            .saveExpenseDetails(values)
            .then(handleErrors)
            .catch(() => {
              throw Error('Network Error');
            });
        },
      }),
    [expenseDefaultType, fromDate, toDate]
  );

  const merchantComp = (props) => {
    const onValueChanged = (e) => {
      if (e.event?.type != 'change') {
        let merchantVal =
          typeof e.value === 'string' ? e.value.trim() : e.value;
        props.data.setValue(merchantVal);
      }
    };

    const onMerchantCustomAdd = (args) => {
      if (!args.text) {
        args.customItem = '';
      }
      const { key } = props.data;
      const newItem = {
        expMerchant: args.text,
        expMerchantId: '',
      };
      const productIds = merchantList.__rawData?.map((item) => item.id);
      const incrementedId = Math.max.apply(null, productIds) + 1;
      const finalItem = {
        merchant: args.text,
        id: incrementedId,
      };
      expense
        .update(key, newItem)
        .then(() => {
          refreshMerchant();
        })
        .catch((error) => {
          throw error;
        });
      args.customItem = finalItem;
    };

    const disableMerchant = props.data.data.expCategoryBaseId === 5;

    const merchantObject = {
      id: props?.data?.data?.expMerchantId,
      merchant: props?.data?.data?.expMerchant,
    };

    const existingData = merchantList.__rawData?.find(
      (item) => item.id === props?.data?.data?.expMerchantId
    );
    let merchantData = merchantList.__rawData;
    if (!existingData) merchantData = [...merchantData, merchantObject];
    return (
      <SelectBox
        dataSource={merchantData}
        displayExpr="merchant"
        valueExpr="id"
        acceptCustomValue={true}
        searchEnabled={true}
        defaultValue={props.data.value == 0 ? null : props.data.value}
        onCustomItemCreating={onMerchantCustomAdd}
        onValueChanged={onValueChanged}
        disabled={disableMerchant}
      />
    );
  };

  const HandleTypeChange = () => {
    if (expenseDefaultType == 'other') {
      toast.current.show([
        {
          severity: 'success',
          summary: 'Successful',
          detail: `Expense Saved Successfully`,
          life: 3000,
        },
        {
          severity: 'warn',
          summary: 'Load Failed',
          detail: 'Change the filter to view the added expense',
          life: 6000,
        },
      ]);
    } else {
      toast.current.show({
        severity: 'success',
        summary: 'Successful',
        detail: `Expense Saved Successfully`,
        life: 3000,
      });
    }
  };

  const addNewRow = () => {
    getParamConfigRate()
      .then((rate) => {
        const customMilageRate = rate.data[0].value;
        const _emptyData = {
          ...emptyData,
          expCategoryId: category.REC_ID,
          expCreatedBy: user.userName,
          expCreatedForId: user.userId,
          expMiles: 0,
          expRate:
            isNaN(customMilageRate) || customMilageRate <= 0
              ? 1
              : parseFloat(customMilageRate),
          reportId: reportId,
        };
        expense.insert(_emptyData).then(() => {
          HandleTypeChange();
          subGrid.current.instance.refresh().then(() => {
            expenseDefaultType != 'other' &&
              subGrid.current.instance?.editCell(0, 'Description');
          });
        });
      })
      .catch((error) => console.log('ooops :(', error));
  };

  const DateRangeEditor = (props) => {
    const [start, end] = props.data.value.split(' - ');
    const [startValue, setStartValue] = useState(
      dayjs.utc(start, 'MM-DD-YYYY').toDate()
    );
    const [endValue, setEndValue] = useState(
      dayjs.utc(end, 'MM-DD-YYYY').toDate()
    );
    const minChangeHandler = (e) => {
      setStartValue(e.value);
      props.data.setValue(
        `${dayjs(e.value).format('MM-DD-YYYY')} - ${dayjs(endValue).format(
          'MM-DD-YYYY'
        )}`
      );
    };
    const maxChangeHandler = (e) => {
      setEndValue(e.value);
      props.data.setValue(
        `${dayjs(startValue).format('MM-DD-YYYY')} - ${dayjs(e.value).format(
          'MM-DD-YYYY'
        )}`
      );
    };
    return (
      <>
        <DateBox
          value={startValue}
          onValueChanged={minChangeHandler}
          displayFormat="MM-dd-yyyy"
        />
        {props.data.data.expCategoryBaseId === 1 && (
          <DateBox
            value={endValue}
            min={startValue}
            onValueChanged={maxChangeHandler}
            displayFormat="MM-dd-yyyy"
          />
        )}
      </>
    );
  };

  const isUploadEnabled = disableUpload.includes(rpStatus);

  const isAJLinkIconDisabled = (e) => {
    return e.row.data.expLinkedDocs.length === 0 && isUploadEnabled;
  };

  const isAJViewIconDisabled = (e) => {
    return e.row.data.expLinkedDocs.length !== 0;
  };

  const AttendeeComponent = (data) => {
    return (
      <EmployeeDropDownBoxComponent
        props={data}
        tagBoxDataSource={customAttendeList}
        dataGridDataSource={attendeeList}
      />
    );
  };
  const onDefaultSelectionChanged = useCallback((e) => {
    onValueChange('expenseDefaultType', e);
    expenseService.createUpdateFilter({
      filterName: 'expense',
      filterData: e,
    });
  }, []);
  const onDeleteDialogHide = useCallback(() => {
    setDeleteDialog(false);
  }, []);

  const onLinkDialogHide = () => {
    setLinkAJDialog(false);
  };
  const onCellPrepared = (e) => {
    if (e.rowType === 'filter' && e.column.dataField === 'expContacts') {
      e.cellElement.innerHTML = '';
    }
  };

  const startDateValue = isUploadEnabled ? dayjs().format('YYYY-MM-DD') : null;

  return (
    <div className="datatable-rowexpansion-demo sub-table mt-1 mb-3">
      <Toast ref={toast} position="top-right" />
      <DataGrid
        id={reportId === 0 ? 'grid-container' : 'expanded-row'}
        ref={subGrid}
        dataSource={expense}
        allowColumnReordering={true}
        rowAlternationEnabled={true}
        showBorders={true}
        remoteOperations={false}
        onInitNewRow={onInitNewRow}
        allowColumnResizing={true}
        columnAutoWidth={true}
        noDataText="No Expense Found"
        onInitialized={onInitialized}
        onEditorPreparing={onEditorPrepare}
        onCellPrepared={onCellPrepared}
        // visible={!loading}
      >
        <Editing
          mode="cell"
          useIcons={true}
          allowUpdating={isUploadEnabled}
          allowDeleting={false}
          selectTextOnEditStart={true}
        />

        {/* Commented for flickering */}

        <Scrolling showScrollbar="always" />
        {reportId === 0 && (
          <Selection
            mode="multiple"
            width={300}
            deferred={true}
            showCheckBoxesMode="always"
          />
        )}
        <FilterRow visible />
        <SearchPanel visible={reportId === 0} width="200" />
        <HeaderFilter visible />

        <Column type="buttons" name="action">
          {reportId === 0 && (
            <Button
              hint="Link to Expense Report"
              icon="link"
              type="success"
              stylingMode="text"
              onClick={linkExpense}
            />
          )}
          <Button
            hint="Edit Expense"
            icon="edit"
            onClick={editExpense}
            visible={isUploadEnabled}
          />
          {/* <Button name="edit" /> */}
          {reportId !== 0 && status === 0 && (
            <Button
              name="delete"
              visible={isUploadEnabled}
              onClick={confirmDelete}
            />
          )}

          <Button
            hint={`Link to ${customActJournal}`}
            icon="newfolder"
            visible={isAJLinkIconDisabled}
            onClick={linkExpenseAJ}
            className="aj-btn"
            elementAttr={{ class: 'aj-btn' }}
          />

          <Button
            hint={`View ${customActJournal}`}
            icon="activefolder"
            visible={isAJViewIconDisabled}
            onClick={linkExpenseAJ}
            className="aj-btn"
            elementAttr={{ class: 'aj-btn' }}
          />
        </Column>

        <Column dataField="expCreatedForId" caption="Created For">
          <Lookup
            dataSource={userList}
            valueExpr="USER_ID"
            displayExpr="USER_NAME"
          />
          <RequiredRule message="Create For is required" />
        </Column>
        <Column dataField="expDescription" caption="Description" width={300}>
          <RequiredRule />
          <StringLengthRule
            max={200}
            message="Description maximum limit crossed"
          />
        </Column>
        <Column dataField="expCategoryId" caption="Category">
          <Lookup
            dataSource={categoryList}
            valueExpr="REC_ID"
            displayExpr="CATEGORY_NAME"
          />
          <RequiredRule message="Category is required" />
        </Column>

        <Column
          dataField={status === 1 ? 'expMerchant' : 'expMerchantId'}
          caption="Merchant"
          alignment="left"
          editCellComponent={merchantComp}
          cellRender={merchantCellValue}
        >
          {status !== 1 && (
            <Lookup
              dataSource={merchantList}
              valueExpr="id"
              displayExpr="merchant"
            />
          )}
        </Column>
        <Column
          dataField="expContacts"
          width={300}
          caption="Attendees"
          allowHeaderFiltering={false}
          allowGrouping={true}
          allowSorting={false}
          editCellComponent={AttendeeComponent}
          cellRender={attendeeCellTemplate}
          calculateFilterExpression={calculateFilterExpression}
        >
          <Lookup
            dataSource={attendeeList}
            valueExpr="contactId"
            displayExpr="contactName"
          />
        </Column>

        <Column dataField="expId" caption="Expense Id" visible={false} />
        <Column
          dataField="expCompanies"
          caption="Expense Companies"
          visible={false}
        />

        <Column
          dataField="expReimbursable"
          caption="Reimbursable"
          editCellRender={reimbursableEditorRender}
        >
          <Lookup
            dataSource={reimbursableStatus}
            valueExpr="id"
            displayExpr="name"
          />
        </Column>
        <Column
          dataField="date"
          caption="Date"
          dataType="string"
          editCellComponent={DateRangeEditor}
          calculateCellValue={dateCellTemplate}
        />
        <Column
          dataField="expAmount"
          caption="Total Amount"
          format={amountFormat}
          alignment="right"
          dataType="number"
          max={*********}
        >
          <RequiredRule />
        </Column>
        <Column
          dataField="expTax1"
          caption={customTax1}
          format={amountFormat}
          alignment="right"
          dataType="number"
          max={*********}
          visible={taxColumnVisibible}
        >
          <RequiredRule />
        </Column>
        <Column
          dataField="expTax"
          caption={customTax}
          format={amountFormat}
          alignment="right"
          dataType="number"
          max={*********}
          visible={taxColumnVisibible}
        >
          <RequiredRule />
        </Column>
        <Column type="buttons" name="attach" visible={isUploadEnabled}>
          <Button
            hint="Attach Receipts"
            icon="upload"
            onClick={attachExpense}
            component={buttonAttach}
          />
        </Column>

        <Column type="buttons" name="view" visible={!isUploadEnabled}>
          <Button
            hint="View Receipts"
            icon="eye"
            onClick={attachExpense}
            component={buttonAttach}
          />
        </Column>
        <Column dataField="expMiles" visible={false} />
        <Column dataField="expRate" visible={false} />

        <Summary>
          <TotalItem
            column="expAmount"
            summaryType="sum"
            valueFormat={summaryFormat}
            precision="2"
          />
        </Summary>

        {reportId === 0 && (
          <Toolbar
          // visible={!loading}
          >
            <Item location="before">
              <SelectBox
                width="130"
                items={typeListNew}
                displayExpr="label"
                valueExpr="value"
                value={expenseDefaultType}
                onValueChanged={(e) => onDefaultSelectionChanged(e.value)}
                hint="Select Type"
              />
            </Item>

            <Item location="before">
              <DxButton
                text="Create Expense"
                icon="add"
                type="default"
                className="bg-primary"
                onClick={() => addExpense()}
              />
            </Item>
            <Item location="before">
              <DxButton
                icon="add"
                type="default"
                className="bg-primary"
                onClick={addNewRow}
                text="Add Row"
              />
            </Item>
            <Item location="before">
              <DxButton
                icon="link"
                type="success"
                onClick={linkExpense}
                hint="Link to Expense Report"
              />
            </Item>
            <Item location="before">
              <DxButton
                type="default"
                className="bg-primary aj-link-btn"
                onClick={linkExpenseAJ}
                hint={`Link to ${customActJournal}`}
              />
            </Item>
            <Item location="before">
              <DxButton
                icon="edit"
                type="default"
                onClick={editExpense}
                hint="Edit Multiple Expense"
              />
            </Item>
            <Item location="before">
              <DxButton
                onClick={confirmDelete}
                icon="trash"
                type="danger"
                hint="Delete Selected Expenses"
              />
            </Item>
            <Item location="before">
              <div className="flex w-7">
                <DateBox
                  value={fromDate}
                  onValueChanged={minDateChangeHandler}
                  displayFormat="MM-dd-yyyy"
                  placeholder="From Date"
                  className="mr-2"
                />
                <DateBox
                  value={toDate}
                  min={fromDate}
                  onValueChanged={maxDateChangeHandler}
                  displayFormat="MM-dd-yyyy"
                  placeholder="To Date"
                  className="mr-2"
                />
              </div>
            </Item>
            <Item location="after" name="searchPanel" disabled={false} />
            <Item location="after">
              <DxButton
                icon="clear"
                onClick={clearFilter}
                hint="Clear All Filters"
              />
            </Item>
          </Toolbar>
        )}
      </DataGrid>

      {ExpenseDialog && (
        <CreateExpenseDialog
          visible={ExpenseDialog}
          onHide={() => {
            setExpenseDialog(false);
          }}
          expenses={expenseData}
          modeData={modeData}
          subGrid={subGrid}
          expenseReportRef={expenseReportRef}
          selectedExpense={selectedExpense}
          merchantList={merchantList}
          categoryList={categoryList}
          userList={userList}
          //  companyList={companyList}
          attendeeList={attendeeList}
          customTax={customTax}
          customTax1={customTax1}
          refreshMerchant={refreshMerchant}
          HandleTypeChange={HandleTypeChange}
        />
      )}

      {linkDialog && (
        <LinkExpenseDialog
          linkDialog={linkDialog}
          onHide={() => {
            setLinkDialog(false);
          }}
          linkReportMethod={linkReportMethod}
          data={reportData}
          selectedExpense={!expenseData.length ? [expenseData] : expenseData}
          subGrid={subGrid}
        />
      )}

      {linkAJDialog && (
        <LinkExpenseAJDialog
          linkAJDialog={linkAJDialog}
          onHide={onLinkDialogHide}
          linkAJMethod={linkAJMethod}
          AJDocs={!expenseData.length ? expenseData.expLinkedDocs : []}
          startDate={startDateValue}
          createdForId={!expenseData.length ? expenseData.expCreatedForId : 1}
          expId={!expenseData.length ? expenseData.expId : 0}
          customActJournal={customActJournal}
          isAJEditEnabled={isUploadEnabled}
        />
      )}

      {deleteDialog && (
        <DeleteRecordDialogTemplate
          header="Confirm delete"
          message={
            expenseData[0] && expenseData[0].expReportId
              ? ' Please Confirm you wish to Unlink/Delete the Expense ?'
              : 'Please Confirm you wish to Delete the Expense ?'
          }
          visible={deleteDialog}
          onHide={onDeleteDialogHide}
          footer={reportDialogTemplate}
        />
      )}
      <style global jsx>{`
        #grid-container > .dx-datagrid > .dx-datagrid-headers {
          position: -webkit-sticky;
          position: sticky;
          background-color: #fff;
          z-index: 1;
          top: 50px;
        }

        .master-detail-caption {
          padding: 0 0 5px 10px;
          font-size: 14px;
          font-weight: bold;
        }

        .dx-scrollbar-horizontal {
          // position: -webkit-sticky;
          // position: sticky;
          top: 0;
        }

        .dx-show-clear-button .dx-icon-clear {
          top: 18px;
        }
        .dx-texteditor-buttons-container {
          height: 35px;
        }
      `}</style>
    </div>
  );
};

export default ExpenseDatatable;
