import { useCallback, useMemo, useRef, useState } from 'react';
import DataGrid, {
  Column,
  Selection,
  FilterRow,
  Scrolling,
  MasterDetail,
} from 'devextreme-react/data-grid';
import { Popup, Button } from 'devextreme-react';
import { ExpenseService } from '@services/expenseService';
import CustomStore from 'devextreme/data/custom_store';
import AJDatatable from '../ajDatatable';

import * as dayjs from 'dayjs';
const utc = require('dayjs/plugin/utc');
dayjs.extend(utc);
const expService = new ExpenseService();
const LinkExpenseAJ = ({
  linkAJDialog,
  onHide,
  linkAJMethod,
  AJDocs,
  startDate,
  createdForId,
  expId,
  customActJournal,
  isAJEditEnabled,
}) => {
  const poupTitle =
    AJDocs.length > 0 ? `View ${customActJournal}` : `Link ${customActJournal}`;
  const [ajId, setAjId] = useState(AJDocs);
  const [btnDisabled, setBtnDisabled] = useState(false);
  const [type] = useState('AJ');
  const subGrid = useRef(null);

  const onSaveButtonClick = () => {
    setBtnDisabled(true);
    const ajParams = ajId.map((item) => {
      return {
        expActivityJournalId: item.id,
        compId: item.compId,
        contId: item.contId,
        contName: item.contName,
        expDocType: type,
      };
    });
    linkAJMethod(ajParams);
  };
  const DialogFooter = (
    <>
      <Button
        type="default"
        text="Save"
        icon="pi pi-check"
        onClick={onSaveButtonClick}
        className="bg-primary mr-2"
        disabled={(!ajId && !type) || btnDisabled}
      />
      <Button
        type="danger"
        text="Cancel"
        icon="pi pi-times"
        className="bg-error  mr-2"
        onClick={onHide}
      />
    </>
  );
  function handleErrors(response) {
    if (response.status !== 200) {
      throw Error(response.statusText);
    }
    return response.data;
  }
  const ajDataSource = useMemo(
    () =>
      new CustomStore({
        key: 'id',
        loadMode: 'raw',
        load: async () => {
          return expService
            .getActivityJournal(startDate, createdForId, expId)
            .then(handleErrors)
            .catch(() => {
              throw new Error('Network Error');
            });
        },
      }),
    []
  );
  const setAjValue = useCallback((value) => {
    setAjId(value.selectedRowsData);
  }, []);
  const customizeDate = useCallback((cellInfo, index) => {
    return (
      <span
        style={{ fontStyle: index === 0 || index === 6 ? 'italic' : 'normal' }}
      >
        {dayjs.utc(cellInfo.value).format('MM-DD-YYYY')}
      </span>
    );
  }, []);

  const rowExpandedTemplate = ({ data }) => {
    return (
      <AJDatatable
        reportId={data.data.id}
        subGrid={subGrid}
        dataKey={data.data.id}
      />
    );
  };

  const expandSingleRow = (e) => {
    e.component.collapseAll(-1);
  };

  const renderPopup = () => {
    return (
      <>
        {/* <ScrollView width="100%" height="100%"> */}
        <div className="formgrid grid mx-0 h-full">
          <div className="field col h-full">
            <DataGrid
              dataSource={ajDataSource}
              selectedRowKeys={isAJEditEnabled && ajId.map((i) => i.id)}
              onSelectionChanged={setAjValue}
              height="100%"
              showBorders={true}
              className="pb-4"
              onRowExpanding={expandSingleRow}
            >
              <FilterRow visible />
              {/* <Scrolling
                  mode="virtual"
                  showScrollbar="always"
                   rowRenderingMode="virtual"
                /> */}
              <Scrolling mode="infinite" showScrollbar="always" />
              {isAJEditEnabled && (
                <Selection
                  mode="multiple"
                  showCheckBoxesMode="always"
                  allowSelectAll={false}
                />
              )}
              <MasterDetail enabled={true} component={rowExpandedTemplate} />

              <Column
                dataField="title"
                cellRender={(cellData) => {
                  return (
                    <a
                      href={`/RepfabricCRM/Journal/JournalEntry.xhtml?id=${cellData.data.id}`}
                      target="_blank"
                      rel="noreferrer"
                      textDecoration="none"
                    >
                      <strong>{cellData.data.title}</strong>
                    </a>
                  );
                }}
              />
              <Column dataField="companyName" />
              <Column dataField="date" cellRender={customizeDate} />
            </DataGrid>
          </div>
        </div>
        {/* </ScrollView> */}

        <div className="p-dialog-footer popup text-right">
          {isAJEditEnabled && DialogFooter}
        </div>
      </>
    );
  };
  return (
    <Popup
      width={550}
      height="90vh"
      maxHeight="100%"
      showTitle={true}
      title={poupTitle}
      dragEnabled={false}
      hideOnOutsideClick={true}
      visible={linkAJDialog}
      onHiding={onHide}
      shadingColor="#00000090"
      contentRender={renderPopup}
    />
  );
};

export default LinkExpenseAJ;
