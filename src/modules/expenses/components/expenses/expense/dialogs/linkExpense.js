import { useCallback, useMemo, useRef, useState } from 'react';
import CustomStore from 'devextreme/data/custom_store';
import { Popup, Button, TextBox, SelectBox } from 'devextreme-react';
import { Toast } from 'primereact/toast';
import ExpenseDialogTemplate from '../../expenseReports/dialogs/expenseReportDialog';
import { ExpenseService } from '@services/expenseService';
import * as dayjs from 'dayjs';

function handleErrors(response) {
  if (response.status !== 200) {
    throw Error(response.statusText);
  }
  return response.data;
}
const expenseService = new ExpenseService();
const LinkExpense = ({
  linkDialog,
  onHide,
  linkReportMethod,
  data,
  selectedExpense,
  subGrid,
}) => {
  const customParseFormat = require('dayjs/plugin/customParseFormat');
  dayjs.extend(customParseFormat);
  const [reportId, setReportId] = useState(null);
  const [reportData, setReportData] = useState(data);
  const [reportDialog, setReportDialog] = useState(false);
  const [disableBtn, setDisableBtn] = useState(false);
  const toast = useRef(null);

  const reportList = useMemo(
    () =>
      new CustomStore({
        key: 'id',
        loadMode: 'raw',
        load: () => {
          return expenseService
            .getExpReportById(reportData.createdForId)
            .then(handleErrors)
            .catch(() => {
              throw Error('Network Error');
            });
        },
      }),
    []
  );
  const createReport = () => {
    setReportDialog(true);
  };

  const onInputChange = (e, name) => {
    setReportData({
      ...reportData,

      [name]: e,
    });
  };

  const saveReport = async () => {
    setDisableBtn(true);
    setReportData({
      ...reportData,
    });
    const [start, end] = reportData.date;
    reportData.startDate = dayjs(start).format('MM-DD-YYYY');
    reportData.endDate = dayjs(end ?? start).format('MM-DD-YYYY');
    reportData.createdFor = reportData.createdForId;
    reportData.isLink = selectedExpense.map((item) => {
      return { expId: item.expId };
    });
    reportData.isLink = await expenseService
      .createExpenseReport(reportData)
      .then((response) => {
        if (response.status === 200) {
          toast.current.show({
            severity: 'success',
            summary: 'Successful',
            detail: `Expense Linked to New Report successfully`,
            life: 3000,
          });
        }
      })
      .catch(() => {
        toast.current.show({
          severity: 'error',
          summary: 'Duplicate Reoprt',
          detail: `Duplicate Report Name`,
          life: 3000,
        });
      });

    setTimeout(() => {
      onHide();
    }, 3001);
    subGrid.current.instance.refresh();
    subGrid.current.instance.deselectAll();
  };

  const DialogFooter = (
    <>
      <Button
        type="success"
        text="Link to New Report"
        icon="pi pi-plus"
        className="bg-success mr-2"
        onClick={createReport}
      />
      <Button
        type="default"
        text="Save"
        icon="pi pi-check"
        onClick={() => {
          const { expReportId } = reportId;
          linkReportMethod(expReportId);
        }}
        className="bg-primary mr-2"
        disabled={!reportId}
      />
      <Button
        type="danger"
        text="Cancel"
        icon="pi pi-times"
        className="bg-error  mr-2"
        onClick={onHide}
      />
    </>
  );
  const onChange = useCallback((e) => {
    setReportId(e.value);
  }, []);
  const renderPopup = () => {
    return (
      <>
        <div className="formgrid grid my-4 mx-0">
          <div className="field col">
            <label htmlFor="expReportId">Report</label>
            <SelectBox
              dataSource={reportList}
              displayExpr="expDescription"
              searchEnabled={true}
              searchMode="contains"
              searchExpr="expDescription"
              searchTimeout={200}
              minSearchLength={0}
              showDataBeforeSearch={true}
              onValueChanged={onChange}
            />
          </div>
          {reportData.createdForId && (
            <div className="field col">
              <label htmlFor="createdForId">Created For</label>
              <br />
              <TextBox value={reportData.createdFor} readOnly={true} />
            </div>
          )}
        </div>
        <div className="p-dialog-footer popup text-right">{DialogFooter}</div>
      </>
    );
  };
  return (
    <>
      <Toast ref={toast} baseZIndex="1600" />
      <Popup
        width={550}
        height={250}
        showTitle={true}
        title={'Link Expense'}
        dragEnabled={false}
        hideOnOutsideClick={true}
        visible={linkDialog}
        onHiding={onHide}
        shadingColor="#00000090"
        contentRender={renderPopup}
      />

      {reportDialog && (
        <ExpenseDialogTemplate
          header="Create Expense Report"
          visible={reportDialog}
          onHide={() => {
            setReportDialog(false);
            onHide();
          }}
          reportData={reportData}
          onInputChange={onInputChange}
          saveReport={saveReport}
          disableBtn={disableBtn}
        />
      )}
    </>
  );
};

export default LinkExpense;
