import { useState, useRef, useEffect } from 'react';

import { FileUpload } from 'primereact/fileupload';
import { Tag } from 'primereact/tag';
import { Button } from 'devextreme-react';

import { delExpenseReceipt, getExpenseReceipt } from '@services/api';
import ViewReceipt from '../view-receipt';
import { basePath } from 'next.config';
const getFileUrlByType = (file) =>
  file.type === 'application/pdf' ? `${basePath}/pdf_logo.png` : file.objectURL;
const FormUpload = ({ expenseData, onUpload, mode, enableUpload, subGrid,files,categoryData,toast }) => {
  const counter = useRef(0);
  const fileUploadRef = useRef(null);
  const [images, setImages] = useState([]);
  const [fileCount, setFileCount] = useState(0);
  const [loading, setLoading] = useState(true);
  
  const fetchData = async () => {
    try {
      const RECEIPTS = await (await getExpenseReceipt(expenseData.expId)).data;
      if (RECEIPTS.length === 0) {
        setLoading(false);
      }
      setImages(RECEIPTS);
    } catch (error) {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (expenseData.expId) {
      fetchData();
    } else {
      setLoading(false);
    }
  }, []);
  const isReceiptDeletionInvalid = (
    idToDelete,
  ) => {
    const remainingImages = images.filter((image) => image.id !== idToDelete);
  
    const category = categoryData.find(
      (item) => item.REC_ID === expenseData.expCategoryId.REC_ID
    );
    if (!category) return false;
  
    const { CATEGORY_RECEIPT_REQ_FLAG, CATEGORY_AJLINK_AMT } = category;

    if (CATEGORY_RECEIPT_REQ_FLAG == 1) {
      const categoryAmount = Number(CATEGORY_AJLINK_AMT) || 0;
      const expenseAmount = Number(expenseData.expAmount) || 0;
      const noFilesUploaded =
      !files || 
      (Array.isArray(files) && files.length === 0) || 
      (typeof files === 'object' && !Array.isArray(files) && Object.keys(files).length === 0);
    
      const condtionCheck =
        expenseAmount > categoryAmount &&
        noFilesUploaded

      return condtionCheck && remainingImages.length === 0;
    }
  
    return false;
  };
  
  const deleteExpenseReceipt = async (id) => {
    try {
      const isInvalid = isReceiptDeletionInvalid(
       id
      );

      if (isInvalid) {
        toast.current.show({
          severity: 'error',
          summary: 'Receipt Required',
          detail: 'Uploading receipts is mandatory',
          life: 3000,
        });
      }
          
      await delExpenseReceipt(id);
      setImages((prev) => prev.filter((image) => image.id !== id));
      subGrid.current.instance.refresh();
    } catch (error) {
      console.log(error);
    }
  };

  const imageLoaded = () => {
    counter.current += 1;
    if (counter.current >= images.length) {
      setLoading(false);
    }
  };
  const headerTemplate = (options) => {
    const { className, chooseButton, uploadButton } = options;
    return (
      <div
        className={`${className} justify-content-between`}
        style={{
          backgroundColor: 'transparent',
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <div className="flex justify-content-between">{chooseButton}</div>
        <div
          className={'flex justify-content-between'}
        >{`${fileCount} file selected`}</div>
        {mode === 'attach' && fileCount > 0 && (
          <div className="flex justify-content-between">{uploadButton}</div>
        )}
      </div>
    );
  };
  const onTemplateRemove = (file, props) => {
    props.onRemove();
    setFileCount((fileCnt) => fileCnt - 1);
    if (mode !== 'attach') {
      onUpload(file);
    }
  };
  const itemTemplate = (file, props) => {
    const FILE_NAME = file.name.substring(0, 10);
    const FILE_URL = getFileUrlByType(file);
    return (
      <div className="flex align-items-center flex-wrap">
        <div className="flex align-items-center" style={{ width: '40%' }}>
          <img
            alt={FILE_NAME}
            role="presentation"
            src={FILE_URL}
            width={50}
            height={50}
            title={file.name}
          />
        </div>
        <Tag
          value={props.formatSize}
          severity="warning"
          className="px-2 py-1"
        />
        <Button
          type="danger"
          hint="Remove Receipt"
          icon="trash"
          className="p-button-outlined p-button-rounded p-button-danger ml-auto"
          onClick={() => onTemplateRemove(file, props)}
        />
      </div>
    );
  };
  const onFilesSelect = (e) => {
    setFileCount((fileCountNew) => fileCountNew + e.files.length);
  };

  return (
    <div className="formgrid grid m-0">
      <div className="field col" id="upload">
        {enableUpload && (
          <>
            <label htmlFor="fileUpload">Upload Receipts</label>
            <FileUpload
              ref={fileUploadRef}
              mode="advanced"
              uploadHandler={onUpload}
              customUpload
              multiple
              auto={mode !== 'attach'}
              accept=".png,.jpg,.jpeg,.bmp,.pdf"
              maxFileSize={26_214_400}
              headerTemplate={headerTemplate}
              itemTemplate={itemTemplate}
              onSelect={onFilesSelect}
              disabled={loading}
            />
          </>
        )}
        <ViewReceipt
          loading={loading}
          images={images}
          imageLoaded={imageLoaded}
          enableUpload={enableUpload}
          deleteExpenseReceipt={deleteExpenseReceipt}
        />
      </div>
    </div>
  );
};

export default FormUpload;
