import { useState } from 'react';
import { Calendar } from 'primereact/calendar';
import { Popup, Button, TextArea, TextBox } from 'devextreme-react';
import * as dayjs from 'dayjs';

const ExpenseReportDialog = ({
  header,
  visible,
  onHide,
  reportData,
  onInputChange,
  saveReport,
  disableBtn,
}) => {
  const utc = require('dayjs/plugin/utc');
  dayjs.extend(utc);

  const [dateVisibility, setDateVisibility] = useState(true);

  const DialogFooter = (
    <>
      <Button
        text="Save"
        icon="pi pi-check"
        type="default"
        className="bg-primary mr-2"
        onClick={saveReport}
        disabled={
          !reportData.description ||
          !reportData.createdFor ||
          !reportData.date ||
          disableBtn
        }
      />
      <Button
        text="Cancel"
        icon="pi pi-times"
        type="danger"
        className="bg-error mr-2"
        onClick={onHide}
      />
    </>
  );

  const renderPopup = () => {
    return (
      <>
        <div className="grid mt-2">
          <div className="col-12">
            <div className="p-fluid formgrid grid">
              <div className="field p-inputtext-sm text-sm col-12 mt-2">
                <label htmlFor="description">Report Name / Description *</label>

                <TextArea
                  value={reportData.description}
                  onValueChanged={(e) => onInputChange(e.value, 'description')}
                  valueChangeEvent="keyup"
                  maxLength={200}
                  placeholder="Enter Description"
                  minHeight="60px"
                  autoResizeEnabled={true}
                />
              </div>
              <div className="field p-inputtext-sm text-sm col-6">
                {reportData.createdForId && (
                  <div className="field col">
                    <label htmlFor="createdForId">Created For</label>
                    <br />
                    <TextBox value={reportData.createdFor} readOnly={true} />
                  </div>
                )}
              </div>
              <div className="field p-inputtext-sm text-sm col-6 ">
                <label htmlFor="date">Date *</label>
                <Calendar
                  id="range"
                  value={reportData.date}
                  visible={dateVisibility}
                  onHide={() => setDateVisibility(false)}
                  onChange={(e) => onInputChange(e.value, 'date')}
                  selectionMode="range"
                  placeholder="mm/dd/yyyy"
                  onSelect={(e) => {
                    if (reportData.date?.length === 2) {
                      setDateVisibility(false);
                    }
                  }}
                  // showIcon
                  // showOnFocus={false}
                  panelClassName="text-sm"
                  todayButtonClassName="m-0 "
                  clearButtonClassName="m-0 p-button-danger "
                />
              </div>
            </div>
          </div>
        </div>
        <div className="p-dialog-footer popup text-right">{DialogFooter}</div>
      </>
    );
  };

  return (
    <Popup
      width={550}
      height={300}
      showTitle={true}
      title={header}
      dragEnabled={false}
      hideOnOutsideClick={false}
      visible={visible}
      onHiding={onHide}
      shadingColor="#00000090"
      contentRender={renderPopup}
    />
  );
};

export default ExpenseReportDialog;
