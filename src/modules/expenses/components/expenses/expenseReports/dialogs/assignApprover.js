import { useCallback, useState } from 'react';
import { Popup, Button, SelectBox } from 'devextreme-react';
import CustomStore from 'devextreme/data/custom_store';
import { ExpenseService } from '@services/expenseService';

const expenseService = new ExpenseService();
function handleErrors(response) {
  if (response.status !== 200) {
    throw Error(response.statusText);
  }
  return response.data;
}
const userList = new CustomStore({
  key: 'USER_ID',
  loadMode: 'raw',
  load: () => {
    return expenseService
      .getAllUserList()
      .then(handleErrors)
      .catch(() => {
        throw Error('Network Error');
      });
  },
});
const AssignApprover = ({ approverDialog, approverMethod, onHide }) => {
  const [userId, setUserId] = useState(null);
  const DialogFooter = (
    <>
      <Button
        type="default"
        text="Save"
        icon="pi pi-check"
        onClick={() => {
          const { USER_ID } = userId;
          approverMethod(USER_ID);
        }}
        className="bg-primary mr-2"
        disabled={!userId}
      />
      <Button
        type="error"
        text="Cancel"
        icon="pi pi-times"
        className="bg-error mr-2"
        onClick={onHide}
      />
    </>
  );
  const onChange = useCallback((e) => {
    setUserId(e.value);
  }, []);
  const renderPopup = () => {
    return (
      <>
        <div className="formgrid grid my-4 mx-0">
          <div className="field col">
            <label htmlFor="expReportId">Approver</label>
            <SelectBox
              dataSource={userList}
              displayExpr="USER_NAME"
              searchEnabled={true}
              searchMode="contains"
              searchExpr="USER_NAME"
              searchTimeout={200}
              minSearchLength={0}
              showDataBeforeSearch={true}
              onValueChanged={onChange}
            />
          </div>
        </div>
        <div className="p-dialog-footer popup text-right">{DialogFooter}</div>
      </>
    );
  };
  return (
    <Popup
      width={550}
      height={250}
      showTitle={true}
      title={'Assign Approver'}
      dragEnabled={false}
      hideOnOutsideClick={true}
      visible={approverDialog}
      onHiding={onHide}
      shadingColor="#00000090"
      contentRender={renderPopup}
    />
  );
};

export default AssignApprover;
