/** Next JS and React JS imports */
import { useState, useRef, useCallback, useMemo } from 'react';

/** PrimeReact imports */
import { Toast } from 'primereact/toast';

/** DevExtreme imports */
import DataGrid, {
  Column,
  Selection,
  FilterRow,
  <PERSON>erFilter,
  MasterDetail,
  Editing,
  Lookup,
  Toolbar,
  Item,
  RequiredRule,
  SearchPanel,
  Button,
  StringLengthRule,
} from 'devextreme-react/data-grid';
import {
  Button as DxButton,
  CheckBox,
  SelectBox,
  DateBox,
} from 'devextreme-react';
import CustomStore from 'devextreme/data/custom_store';

/** Other Third party packages */
import crypto from 'crypto';
import * as dayjs from 'dayjs';
import nProgress from 'nprogress';

/** Custom Hooks and components */
import { useMenu } from '@contexts/menu';
import { useExpense } from '@contexts/expense';
import ExpenseDatatable from '../../expense/expenseDatatable';
import AssignApprover from '../dialogs/assignApprover';
import ConfirmDialog from '../../dialogs/confirmDialog';
import SummaryDialogTemplate from '../../dialogs/summaryDialog';
import DeleteRecordDialogTemplate from '../../dialogs/deleteRecordDialog';
import ExportDialog from '../../dialogs/exportDialog';

/** Custom Service */
import SmallModal from '@components/small-modal';
import { ExpenseService } from '@services/expenseService';
import { getParamConfigRate } from '@services/api';
import { amountFormat, statusArray, typeList } from 'src/constants';
import ExpenseDetails from '../ViewExpenseDetails';


/** Utility Function */
function handleErrors(response) {
  if (response.status !== 200) {
    throw Error(response.statusText);
  }
  return response.data;
}
const expenseService = new ExpenseService();
const userList = new CustomStore({
  key: 'USER_ID',
  loadMode: 'raw',
  load: async () => {
    return expenseService
      .getAllUserList()
      .then(handleErrors)
      .catch(() => {
        throw Error('Network Error');
      });
  },
});
const customParseFormat = require('dayjs/plugin/customParseFormat');
dayjs.extend(customParseFormat);
const todaysDate = dayjs().format('MM-DD-YYYY');
const tomorrowsDate = dayjs().add(1, 'day').format('MM-DD-YYYY');
const emptyData = {
  description: '',
  createdFor: '',
  startDate: todaysDate,
  endDate: tomorrowsDate,
  total: 0,
  isLink: [],
};
let dataGrid;
function onInitialized(e) {
  dataGrid = e.component;
}
const isApproveExpense = (e) => {
  return (
    e.row.data.total == 0 ||
    (e.row.data.status !== 'OPEN' && e.row.data.status !== 'REJECTED')
  );
};
function onCellPrepared(e) {
  if (e.rowType === 'data' && e.column.name === 'submit') {
    const btnElement = e.cellElement.lastElementChild;
    btnElement.className = isApproveExpense(e)
      ? `${btnElement.className} dx-state-disabled`
      : `${btnElement.className} dx-state-enabled`;
    if (isApproveExpense(e)) {
      const wrapperDiv = document.createElement('div');
      wrapperDiv.style.display = 'inline-block';
      wrapperDiv.title =
        e.row.data.total == 0
          ? 'Total Amount is 0.00'
          : e.row.data.status.charAt(0).toUpperCase() +
            e.row.data.status.slice(1).toLowerCase();
      btnElement.parentNode.replaceChild(wrapperDiv, btnElement);
      wrapperDiv.appendChild(btnElement);
    }
    e.watch(
      function () {
        return e.row.data.total;
      },
      function () {
        btnElement.className = isApproveExpense(e)
          ? `${btnElement.className} dx-state-disabled`
          : `${btnElement.className} dx-state-enabled`;
        if (
          isApproveExpense(e) &&
          !['SUBMITTED', 'APPROVED'].includes(e.row.data.status)
        ) {
          const wrapperDiv = document.createElement('div');
          wrapperDiv.title = 'More details to be filled in';
          wrapperDiv.style.display = 'inline-block';
          btnElement.parentNode.replaceChild(wrapperDiv, btnElement);
          wrapperDiv.appendChild(btnElement);
        }
      }
    );
  } else if (e.rowType === 'data' && e.column.dataField === 'createdFor') {
    const btnClass = e.cellElement.className;
    e.cellElement.className =
      e.data.expenseCount > 0
        ? `${btnClass} dx-state-disabled`
        : `${btnClass} dx-state-enabled`;
    e.watch(
      function () {
        return e.data.expenseCount;
      },
      function () {
        e.cellElement.className =
          e.data.expenseCount > 0
            ? `${btnClass} dx-state-disabled`
            : `${btnClass} dx-state-enabled`;
      }
    );
  }
}
const ExpenseReport = ({ approveStatus }) => {
  const dt = useRef(null);
  const { user } = useMenu();
  const { defaults, onValueChange } = useExpense();
  const {
    reportDefaultType,
    category,
    taxColumnVisibible,
    expenseNonReimbursableParam,
  } = defaults;
  const [approverDialog, setApproverDialog] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState(false);
  const [confirmDialogForPayment, setConfirmDialogForPayment] = useState(false);

  const [summaryDialog, setSummaryDialog] = useState(false);
  const [attachConfirmDialog, setAttachConfirmDialog] = useState(false);
  const [printData, setPrintData] = useState([]);
  const [summary, setSummary] = useState([]);
  const [expenseRow, setExpenseRow] = useState(false);
  const [printDisable, setPrintDisable] = useState(false);
  const [confirmMode, setConfirmMode] = useState([]);
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [displayReceipt, setDisplayReceipt] = useState(true);
  const [delExpenseReport, setDelExpenseReport] = useState(null);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [showExpenseDetails, setShowExpenseDetails] = useState(false);
  const [selectedExpenseReport, setSelectedExpenseReport] = useState(null);
  const subGrid = useRef(null);
  const toast = useRef(null);
  const toolbarRef = useRef(null);

  const expenseSummary = () => {
    expenseService
      .expenseSummary(reportDefaultType, user.userId)
      .then((response) => {
        if (response.status === 200) {
          setSummary(response.data);
          setConfirmMode({
            header: 'Expense Summary',
            type: 'summary',
            width: '500',
          });
          setSummaryDialog(true);
        }
      });
  };

  const expenseHistory = (e, data) => {
    const { expReportId, approver } = data.data;
    expenseService.expenseHistory(expReportId).then((response) => {
      if (response.status === 200) {
        const tableData = response.data.map((data) => ({
          ...data,
          submittedTo: approver,
        }));

        setSummary(tableData);
        if (data.data.status === 'APPROVED') {
          setConfirmMode({
            header: 'Expense Approval History',
            type: 'history',
            width: '1000',
          });
        } else if (data.data.status === 'REJECTED') {
          setConfirmMode({
            header: 'Expense Reject History',
            type: 'history',
            width: '1000',
          });
        } else if (data.data.status === 'PAID') {
          setConfirmMode({
            header: 'Expense Paid History',
            type: 'history',
            width: '1000',
          });
        } else {
          setConfirmMode({
            header: 'Expense History',
            type: 'history',
            width: '1000',
          });
        }
        setSummaryDialog(true);
      }
    });
  };

  const approverMethod = (approverId) => {
    setApproverDialog(false);
    const params = { userId: user.userId, approverId: approverId };
    expenseService.addApprover(params).then((response) => {
      if (response.status === 200) {
        toast.current.show({
          severity: 'success',
          summary: 'Successful',
          detail: `Approver Assigned successfully`,
          life: 3000,
        });
      }
    });
  };

  const confirmApprove = useCallback((e, status, header, message) => {
    e && setExpenseRow(e);
    setConfirmMode({
      header: header,
      message: message,
      status: status,
      comment: '',
    });
    setConfirmDialog(true);
  }, []);

  const approveExpense = (e, data, comment) => {
    setConfirmDialog(false);
    const row = expenseRow;
    const params = {
      id: row.id,
      expReportId: row.expReportId,
      submittedDate:
        confirmMode.status === 1 ? dayjs().format('MM/DD/YYYY HH:mm') : '',
      approvedDate:
        confirmMode.status !== 1 ? dayjs().format('MM/DD/YYYY HH:mm') : '',
      approvedStatus: confirmMode.status,
      comments: comment?.toString().trim(),
      createdFor: row.createdFor,
    };
    let statusText = '';
    if (confirmMode.status == 1) {
      statusText = 'Expense Submitted Successfully';
    } else if (confirmMode.status == 2) {
      statusText = 'Expense Approved Successfully';
    } else {
      statusText = 'Expense Rejected Successfully';
    }

    expenseService
      .approve(params)
      .then((response) => {
        if (response.status === 200) {
          dt.current.instance.refresh();
          toast.current.show({
            severity: 'success',
            summary: 'Successful',
            detail: statusText,
            life: 3000,
          });
        }
      })
      .catch((ex) => {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: ex.response?.data,
          life: 10000,
        });
      });
  };

  const confirmPayment = useCallback((e, status, header, message) => {
    e && setExpenseRow(e);
    setConfirmMode({
      header: header,
      message: message,
      status: status,
      comment: '',
    });
    setConfirmDialogForPayment(true);
  }, []);

  const approvePayment = (e, data, comment) => {
    setConfirmDialogForPayment(false);
    const row = expenseRow;
    const params = {
      id: row.id,
      expReportId: row.expReportId,
      submittedDate:
        confirmMode.status === 1 ? dayjs().format('MM/DD/YYYY HH:mm') : '',
      approvedDate:
        confirmMode.status !== 1 ? dayjs().format('MM/DD/YYYY HH:mm') : '',
      approvedStatus: confirmMode.status,
      comments: comment?.toString().trim(),
      createdFor: row.createdFor,
    };
    let statusText = '';

    if (confirmMode.status == 4) {
      statusText = 'Expense Paid Successfully';
    }
    // if (confirmMode.status == 1) {
    //   statusText = 'Expense Submitted Successfully';
    // } else if (confirmMode.status == 2) {
    //   statusText = 'Expense Approved Successfully';
    // } else {
    //   statusText = 'Expense Rejected Successfully';
    // }

    expenseService
      .approve(params)
      .then((response) => {
        if (response.status === 200) {
          dt.current.instance.refresh();
          toast.current.show({
            severity: 'success',
            summary: 'Successful',
            detail: statusText,
            life: 3000,
          });
        }
      })
      .catch((ex) => {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: ex.response?.data,
          life: 10000,
        });
      });
  };

  const onInputChange = useCallback((e) => {
    setConfirmMode((prev) => {
      return { ...prev, comment: e };
    });
  }, []);
  const onInitNewRow = (e) => {
    for (const key in emptyData) {
      e.data[key] = emptyData[key];
    }
  };
  const disableDelApprStatus = (e) => e.row.data.status === 'APPROVED';
  const isNotOpenExpense = (e) => {
    return e.row.data.status !== 'OPEN' && e.row.data.status !== 'REJECTED';
  };
  const isOpenExpense = (e) => {
    return e.row?.data.status === 'OPEN' || e.row?.data.status === 'REJECTED';
  };

  const disableApprove = (e) =>
    !e.row.data.approver.some(
      (approver) => approver.approverId === user.userId
    );

  const minDateChangeHandler = useCallback(
    (e) => {
      if (e.value) setFromDate(e.value);
      if (e.value && toDate < e.value) setToDate(e.value);
    },
    [fromDate, toDate]
  );
  const maxDateChangeHandler = useCallback((e) => {
    setToDate(e.value);
  }, []);

  const clearFilter = () => {
    dt.current.instance.clearFilter();
    // change toolbar items value of searchInput to '' (dont hard coded)
    console.log(toolbarRef.current);
    setFromDate('');
    setToDate('');
  };
  const expenseReport = useMemo(
    () =>
      new CustomStore({
        key: 'id',
        load: async () => {
          return expenseService
            .getExpenseReport(
              reportDefaultType,
              approveStatus.status,
              fromDate && toDate ? dayjs(fromDate).format('MM-DD-YYYY') : '',
              fromDate && toDate ? dayjs(toDate).format('MM-DD-YYYY') : ''
            )
            .then(handleErrors)
            .catch(() => []);
        },
        update: async (key, values) => {
          // get row data from store
          const row = dt.current.instance.cellValue(
            dt.current.instance.getRowIndexByKey(key),
            'covers'
          );
          const desc = dt.current.instance.cellValue(
            dt.current.instance.getRowIndexByKey(key),
            'description'
          );
          const createdFor = dt.current.instance.cellValue(
            dt.current.instance.getRowIndexByKey(key),
            'createdFor'
          );
          const expReportId = dt.current.instance.cellValue(
            dt.current.instance.getRowIndexByKey(key),
            'expReportId'
          );
          const params = {
            id: key,
            ...values,
          };
          if (values.hasOwnProperty('covers')) {
            const [start, end] = values.covers.split(' - ');
            params.startDate = dayjs(start, 'MM-DD-YYYY').format('MM-DD-YYYY');
            params.endDate = dayjs(end, 'MM-DD-YYYY').format('MM-DD-YYYY');
            delete params.covers;
          } else {
            const [start, end] = row.split(' - ');
            params.startDate = dayjs(start, 'MM-DD-YYYY').format('MM-DD-YYYY');
            params.endDate = dayjs(end ?? start, 'MM-DD-YYYY').format(
              'MM-DD-YYYY'
            );
          }

          if (params.endDate == 'Invalid Date') {
            params.endDate = params.startDate;
          }

          params.expReportId = expReportId;
          params.description = desc?.toString().trim();
          params.createdFor = createdFor;
          return expenseService
            .updateExpenseReport(params)
            .then(handleErrors)
            .catch(() => {
              throw Error('Network Error');
            });
        },
        remove: async (key) => {
          return expenseService
            .deleteExpenseReport(key)
            .then(handleErrors)
            .catch(() => {
              throw Error('Network Error');
            });
        },
        insert: async (values) => {
          if (values.hasOwnProperty('covers')) {
            const [start, end] = values.covers.split(' - ');
            values.startDate = dayjs(start, 'MM-DD-YYYY').format('MM-DD-YYYY');
            values.endDate = dayjs(end ?? start, 'MM-DD-YYYY').format(
              'MM-DD-YYYY'
            );
            delete values.covers;
          }
          return expenseService
            .createExpenseReport(values)
            .then(handleErrors)
            .catch(() => {
              throw Error('Network Error');
            });
        },
      }),
    [reportDefaultType, toDate, fromDate]
  );

  const HandleTypeChange = () => {
    if (reportDefaultType == 'other') {
      toast.current.show([
        {
          severity: 'success',
          summary: 'Successful',
          detail: `Report Created Successfully`,
          life: 3000,
        },
        {
          severity: 'warn',
          summary: 'Load Failed',
          detail: 'Change the filter to view the added report',
          life: 6000,
        },
      ]);
    } else {
      toast.current.show({
        severity: 'success',
        summary: 'Successful',
        detail: `Report Created Successfully`,
        life: 3000,
      });
    }
  };

  const rowExpandedTemplate = ({ data }) => {
    return (
      <ExpenseDatatable
        reportId={data.data.expReportId}
        subGrid={subGrid}
        status={approveStatus.status}
        expenseReport={expenseReport}
        dataKey={data.data.id}
        expenseReportRef={dt}
        reportStatus={data.data.status}
      />
    );
  };

  const addNewRow = () => {
    const token = crypto.randomBytes(2).toString('hex');
    const _emptyData = {
      ...emptyData,
      description: `New Expense Report #${token}#`,
      createdFor: user.userId,
    };
    expenseReport.insert(_emptyData).then(() => {
      HandleTypeChange();
      dt.current.instance.refresh().then(() => {
        reportDefaultType != 'other' &&
          dt.current.instance?.editCell(0, 'description');
      });
    });
  };

  const addNewSubGridRow = (data) => {
    getParamConfigRate()
      .then((rate) => {
        const milageRate = rate.data[0].value;

        const _emptyData = {
          expStartDate: todaysDate,
          expEndDate: tomorrowsDate,
          expCategoryId: category.REC_ID,
          expCreatedForId: data.row.data.createdFor,
          expAmount: 0,
          expMiles: 0,
          expRate:
            isNaN(milageRate) || milageRate <= 0 ? 1 : parseFloat(milageRate),
          expReportId: data.row.data.expReportId,
          expReimbursable:
            expenseNonReimbursableParam.userParamStatus == 1 ? 0 : 1,
          expLinkedDocs: [],
          expDescription: '',
        };
        expenseService.saveExpenseDetails(_emptyData).then((response) => {
          if (response.status === 200) {
            toast.current.show({
              severity: 'success',
              summary: 'Successful',
              detail: `Expense Created Successfully`,
              life: 3000,
            });
          }
        });
        if (!dt.current.instance.isRowExpanded(data.row.key)) {
          dt.current.instance.expandRow(data.row.key);
        }
        dt.current.instance.refresh(true);
        // refresh dt grid and focus on subgrid on load success
        setTimeout(
          () =>
            subGrid.current.instance.refresh().then(() => {
              subGrid.current.instance.editCell(0, 'Description');
            }),
          500
        );
      })
      .catch((error) => console.log('ooops :(', error));
  };

  const CoverEditComp = (props) => {
    const [start, end] = props.data.value.split(' - ');
    const [startValue, setStartValue] = useState(
      dayjs.utc(start, 'MM-DD-YYYY').toDate()
    );
    const [endValue, setEndValue] = useState(
      dayjs.utc(end, 'MM-DD-YYYY').toDate()
    );
    const minChangeHandler = (e) => {
      setStartValue(e.value);
      props.data.setValue(
        `${dayjs(e.value).format('MM-DD-YYYY')} - ${dayjs(endValue).format(
          'MM-DD-YYYY'
        )}`
      );
    };
    const maxChangeHandler = (e) => {
      setEndValue(e.value);
      props.data.setValue(
        `${dayjs(startValue).format('MM-DD-YYYY')} - ${dayjs(e.value).format(
          'MM-DD-YYYY'
        )}`
      );
    };
    return (
      <>
        <DateBox
          value={startValue}
          onValueChanged={minChangeHandler}
          displayFormat="MM-dd-yyyy"
        />
        <DateBox
          value={endValue}
          min={startValue}
          onValueChanged={maxChangeHandler}
          displayFormat="MM-dd-yyyy"
        />
      </>
    );
  };

  // My Expenses option in the TypeList should be visible only for owner
  const typeListNew = typeList.filter((item) =>
    user.isOwner != 1 ? item.value != 'myExpenses' : item
  );

  const expandSingleRow = (e) => {
    e.component.collapseAll(-1);
  };
  const renderStatusColumn = (data) => {
    return (
      <DxButton
        type={statusArray.find((i) => i?.text === data?.value)?.type}
        className={statusArray.find((i) => i.text === data.value)?.color}
        // onClick={expenseHistory(data)}
        onClick={(e) => {
          {
            data.text !== 'OPEN' && expenseHistory(e, data);
          }
        }}
      >
        {data.value}
      </DxButton>
    );
  };
  const onSummaryDialogHide = useCallback(() => {
    setSummaryDialog(false);
  }, []);
  const onConfirmDialogHide = useCallback(() => {
    setConfirmDialog(false);
  }, []);
  const onConfirmDialogForPaymentHide = useCallback(() => {
    setConfirmDialogForPayment(false);
  }, []);
  const onApproverDialogHide = useCallback(() => {
    setApproverDialog(false);
  }, []);
  const onDefaultSelectionChanged = useCallback(async (e) => {
    onValueChange('reportDefaultType', e);
    await expenseService.createUpdateFilter({
      filterName: 'expenseReport',
      filterData: e,
    });
  }, []);
  const coverCellTemplate = (cellInfo) => {
    return `${dayjs(cellInfo.startDate, 'MM-DD-YYYY').format(
      'MM-DD-YYYY'
    )} - ${dayjs(cellInfo.endDate, 'MM-DD-YYYY').format('MM-DD-YYYY')}`;
  };
  const onRejectIconClick = useCallback((e) => {
    const status = 3;
    const header = 'Expense Rejection';
    const message = 'Please Confirm you wish to Reject the Expense ?';
    dt.current.instance
      .byKey(e.row.data.id)
      .done((d) => confirmApprove(d, status, header, message));
  }, []);
  const onApproveIconClick = useCallback((e) => {
    const status = 2;
    const header = 'Expense Approval';
    const message = 'Confirm you wish to Approve the Expense Report ?';
    dt.current.instance
      .byKey(e.row.data.id)
      .done((d) => confirmApprove(d, status, header, message));
  }, []);

  //for Waiting to be paid tab- pay button onclick
  const onPayIconClick = useCallback((e) => {
    const status = 4;
    const header = 'Expense Payment';
    const message = 'Confirm you wish to make a payment?';
    dt.current.instance
      .byKey(e.row.data.id)
      .done((d) => confirmPayment(d, status, header, message));
  }, []);

  const onValidateApproveIconClick = async (e) => {
    try {
      e.component?.collapseAll(-1);
      const { expReportId, id } = e.row.data;
      const { data: linkExpenseValidate } =
        await expenseService.linkExpenseReportValidation({ expReportId });
      if (linkExpenseValidate.isError) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: linkExpenseValidate.message,
          life: 10000,
        });
        return;
      }

      const validate = await expenseService
        .submitApprove(expReportId)
        .then((res) => res.data);

      const status = validate.isWarning ? 0 : 1;
      const header = validate.isWarning
        ? 'Validate Expense Report'
        : 'Submit for Approval';
      const message = validate.isWarning
        ? validate.message
        : 'Confirm you wish to Submit the Expense Report for Approval?';

      dt.current.instance
        .byKey(id)
        .done((d) => confirmApprove(d, status, header, message));
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Please try after sometime.',
        life: 10000,
      });
    }
  };

  const onSubmitApproveIconClick = useCallback((e, data) => {
    const status = 1;
    const header = 'Submit for Approval';
    const message =
      'Confirm you wish to Submit the Expense Report for Approval ?';
    dt.current.instance
      .byKey(data.id)
      .done((d) => confirmApprove(d, status, header, message));
  }, []);

  const onChangePrint = useCallback((e) => {
    setDisplayReceipt(e.value);
  }, []);

  const getExportRequestParam = async (reportParam) => {
    const param = {
      createType: 'all',
      expReportId: reportParam.expReportId,
      acjhId: 0,
      expId: 0,
      displayReceipts: displayReceipt ? 1 : 0,
    };
    return expenseService.postExpenseExport(param).then((response) => {
      if (response.status === 200) {
        return new Blob([response.data], { type: 'application/pdf' });
      }
    });
  };

  const exportReport = async (e) => {
    try {
      onAttachConfirmDialogHide();
      setPrintDisable(true);
      nProgress.start();
      dt.current?.instance.beginCustomLoading();
      for (const paramData of printData) {
        await getExportRequestParam(paramData)
          .then((blob) => {
            const fileURL = window.URL.createObjectURL(blob);
            const alink = document.createElement('a');
            alink.href = fileURL;
            alink.download = `${paramData.description.trim()}.pdf`;
            alink.click();
          })
          .catch(() => {
            toast.current.show({
              severity: 'error',
              summary: 'Print report failed',
              detail: 'Failed to export one or more reports. Please try again.',
              life: 10000,
            });
          });
      }
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: error.message || 'An unexpected error occurred.',
        life: 10000,
      });
    } finally {
      dt.current?.instance.deselectAll();
      dt.current?.instance.refresh();
      dt.current?.instance.endCustomLoading();
      setPrintData([])
      nProgress.done();
      setPrintDisable(false);
    }
  };

  const onExportBtnClick = () => {
    try {
      const selectedData = dataGrid?.getSelectedRowsData();
      if (selectedData?.length > 0) {
        setPrintData(selectedData);
        setDisplayReceipt(true);
        setAttachConfirmDialog(true);
      } else {
        toast.current.show({
          severity: 'warn',
          summary: 'No Selection',
          detail: 'Please select rows to print.',
          life: 5000,
        });
      }
    } catch (error) {
      console.error('Error during export:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Export Error',
        detail: 'An unexpected error occurred. Please try again.',
        life: 10000,
      });
    }
  };

  const onAttachConfirmDialogHide = useCallback(() => {
    setAttachConfirmDialog(false);
  }, []);

  const attachConfirmDialogTemplate = (
    <div className="p-dialog-footer popup text-right">
      <DxButton
        icon="pi pi-check"
        type="default"
        text="Print"
        className="bg-primary mr-2"
        onClick={exportReport}
      />
      <DxButton
        icon="pi pi-times"
        type="danger"
        text="Cancel"
        className="mr-2"
        onClick={onAttachConfirmDialogHide}
      />
    </div>
  );
  const onDeleteBtnClicked = (e) => {
    if (e.row) {
      setDelExpenseReport(e.row.data.expReportId);
      setDeleteDialog(true);
    }
  };
  const onDeleteDialogHide = useCallback(() => {
    setDeleteDialog(false);
    setDelExpenseReport(null);
  }, []);
  const deleteRecords = () =>
    expenseReport.remove(delExpenseReport).then(() => {
      setDeleteDialog(false);
      dt.current.instance.refresh();
    });
  const reportDialogTemplate = (
    <>
      <DxButton
        icon="pi pi-check"
        type="danger"
        text="Delete"
        className="bg-error mr-2"
        onClick={deleteRecords}
      />
      <DxButton
        icon="pi pi-times"
        type="default"
        text="Cancel"
        className="bg-yellow-600 mr-2"
        onClick={onDeleteDialogHide}
      />
    </>
  );

  const [exportDialog, setExportDialog] = useState(false);

  const exportExpense = () => {
    setExportDialog(true);
  };

  const onExportDialogHide = useCallback(() => {
    setExportDialog(false);
  }, []);
  const onViewIconClick = useCallback((e) => {
    setSelectedExpenseReport(e.row.data);
    setShowExpenseDetails(true);
  }, []);


  return (
    <div
      className={`datatable-rowexpansion-demo mt-1 mb-3 ${
        showExpenseDetails ? 'split-view' : ''
      }`}
      id="loadExpense"
    >
      <Toast ref={toast} baseZIndex="1600" position="top-right" />

        <DataGrid
        id="grid-container"
        dataSource={expenseReport}
        showBorders={true}
        ref={dt}
        allowColumnReordering={true}
        rowAlternationEnabled={true}
        remoteOperations={false}
        onInitNewRow={onInitNewRow}
        showSelectionControls={true}
        columnAutoWidth={true}
        noDataText="No Expense Report Found"
        onRowExpanding={expandSingleRow}
        onInitialized={onInitialized}
        repaintChangesOnly={true}
        onCellPrepared={onCellPrepared}
        >
          {approveStatus.status === 0 && (
            <Selection
              mode="multiple"
              width={300}
              showCheckBoxesMode="always"
              allowSelectAll={false}
            />
          )}

          <Editing
            mode="cell"
            useIcons={true}
            allowUpdating={isOpenExpense}
            allowDeleting={isOpenExpense}
            allowAdding={true}
          />
          <SearchPanel visible={true} width="200" />
          <FilterRow visible />
          <HeaderFilter visible />

          {approveStatus.status === 0 ? (
            <Column type="buttons" name="submit">
              <Button
                hint="Add Expense"
                name="add"
                onClick={addNewSubGridRow}
                disabled={isNotOpenExpense}
              />
              <Button
                hint="Send for Approval"
                name="submit"
                icon="todo"
                type="success"
                stylingMode="text"
                onClick={onValidateApproveIconClick}
              />
            </Column>
          ) : approveStatus.status === 1 ? (
            <Column type="buttons" name="approve">
              <Button
                hint="Approve"
                name="approve"
                icon="todo"
                type="success"
                stylingMode="text"
                onClick={onApproveIconClick}
                disabled={disableApprove}
              />
              <Button
                hint="Reject"
                name="reject"
                icon="close"
                type="danger"
                stylingMode="text"
                onClick={onRejectIconClick}
                disabled={disableApprove}
              />
              <Button
                hint="View Expense Report"
                icon="view-expense"
                type="default"
                stylingMode="text"
                elementAttr={{ class: 'aj-btn' }}
                onClick={onViewIconClick}
                disabled={disableApprove}
              />
            </Column>
          ) : approveStatus.status === 2 ? (
            <Column type="buttons" name="paid" width={50}>
              <Button
                hint="Pay"
                name="pay"
                icon="todo"
                type="success"
                stylingMode="text"
                onClick={onPayIconClick}
                disabled={disableApprove}
                height={'100px'}
                width={'150px'}
              />
            </Column>
          ) : null}
          <MasterDetail enabled={true} component={rowExpandedTemplate} />
          <Column
            dataField="description"
            caption="Name / Description"
            width={250}
          >
            <RequiredRule />
            <StringLengthRule
              max={200}
              message="Description maximum limit crossed"
            />
          </Column>
          <Column dataField="createdFor" caption="Created For">
            <Lookup
              dataSource={userList}
              valueExpr="USER_ID"
              displayExpr="USER_NAME"
            />
            <RequiredRule />
          </Column>
          <Column
            dataField="covers"
            caption="Covers"
            editCellComponent={CoverEditComp}
            calculateCellValue={coverCellTemplate}
          />
          <Column
            dataField="total"
            caption="Total Amount"
            allowEditing={false}
            format={amountFormat}
            alignment="right"
            dataType="number"
          />
          <Column
            dataField="totalTax"
            caption="Total Tax"
            allowEditing={false}
            format={amountFormat}
            alignment="right"
            dataType="number"
            max={*********}
            visible={taxColumnVisibible}
          />
          <Column
            dataField="status"
            cellRender={renderStatusColumn}
            allowEditing={false}
          />
          <Column dataField="createdBy" allowEditing={false} />
          <Column type="buttons" visible={approveStatus.status === 0}>
            <Button
              hint="Delete Expense Report"
              icon="trash"
              disabled={disableDelApprStatus}
              onClick={onDeleteBtnClicked}
            />
          </Column>

          <Column dataField="expReportId" visible={false} />
          <Toolbar>
          <Item location="before">
            <SelectBox
              width="130"
              items={typeListNew}
              displayExpr="label"
              valueExpr="value"
              value={reportDefaultType}
              onValueChanged={(e) => onDefaultSelectionChanged(e.value)}
              hint="Select Type"
            />
          </Item>

          {approveStatus.status === 0 && (
            <Item location="before">
              <DxButton
                icon="add"
                onClick={addNewRow}
                type="default"
                className="bg-primary"
                text="Add Row"
              />

              <DxButton
                icon="pdffile"
                type="default"
                className="bg-primary ml-2"
                onClick={onExportBtnClick}
                text="Print Report"
                disabled={printDisable}
              />
            </Item>
          )}

          <Item location="before">
            <div className="flex w-7">
              <DateBox
                value={fromDate}
                onValueChanged={minDateChangeHandler}
                displayFormat="MM-dd-yyyy"
                placeholder="From Date"
                className="mr-2"
              />
              <DateBox
                value={toDate}
                min={fromDate}
                onValueChanged={maxDateChangeHandler}
                displayFormat="MM-dd-yyyy"
                placeholder="To Date"
                className="mr-2"
              />
            </div>
          </Item>
          <Item location="after" name="searchPanel" />
          <Item location="after">
            <DxButton
              icon="clear"
              onClick={clearFilter}
              hint="Clear All Filters"
            />
          </Item>
          <Item location="after" className="text-right">
            <DxButton
              onClick={expenseSummary}
              // icon="trash"
              type="default"
              className="bg-primary"
              text="Expense Summary"
            />
          </Item>
        </Toolbar>
        </DataGrid>
        {showExpenseDetails && (
          <ExpenseDetails
            selectedExpenseReport={selectedExpenseReport}
            onClose={() => setShowExpenseDetails(false)}
            expenseReport={dt}
          />
        )}

      {approverDialog && (
        <AssignApprover
          approverDialog={approverDialog}
          onHide={onApproverDialogHide}
          approverMethod={approverMethod}
          createdFor={user.userName}
          createdForId={user.userId}
        />
      )}
      {confirmDialog && (
        <ConfirmDialog
          confirmMode={confirmMode}
          data={expenseRow}
          visible={confirmDialog}
          onHide={onConfirmDialogHide}
          onSave={
            // onConfirmClicked(confirmMode.status)
            confirmMode.status === 0 ? onSubmitApproveIconClick : approveExpense
          }
        />
      )}

      {confirmDialogForPayment && (
        <ConfirmDialog
          confirmMode={confirmMode}
          data={expenseRow}
          visible={confirmDialogForPayment}
          onHide={onConfirmDialogForPaymentHide}
          onSave={approvePayment}
        />
      )}

      {summaryDialog && (
        <SummaryDialogTemplate
          confirmMode={confirmMode}
          data={summary}
          visible={summaryDialog}
          onInputChange={onInputChange}
          onHide={onSummaryDialogHide}
          // footer={reportDialogTemplate}
        />
      )}
      {attachConfirmDialog && (
        <SmallModal
          title="Print Report"
          setOpen={setAttachConfirmDialog}
          footer={attachConfirmDialogTemplate}
        >
          <div className="formgrid grid m-3">
            <CheckBox
              onValueChanged={onChangePrint}
              text="Print with Receipts"
              value={displayReceipt}
              defaultValue={true}
            />
          </div>
        </SmallModal>
      )}
      {deleteDialog && (
        <DeleteRecordDialogTemplate
          header="Confirm delete"
          message="Please Confirm you wish to Delete Expense Report ?"
          data={delExpenseReport}
          visible={deleteDialog}
          onHide={onDeleteDialogHide}
          footer={reportDialogTemplate}
        />
      )}
      {approveStatus.status === 0 && (
        <>
          <div className="export-btn">
            <DxButton
              onClick={exportExpense}
              type="default"
              className="bg-primary"
              text="Export"
            />
          </div>
          {exportDialog && (
            <ExportDialog visible={exportDialog} onHide={onExportDialogHide} />
          )}
        </>
      )}
      <style global jsx>{`
     #grid-container > .dx-datagrid > .dx-datagrid-headers {
          position: -webkit-sticky;
          position: sticky;
          background-color: #fff;
          z-index: 1;
          top: 50px;
        }
        .master-detail-caption {
          padding: 0 0 5px 10px;
          font-size: 14px;
          font-weight: bold;
        }
        .dx-popup-content.dx-dialog-content {
          padding: 20px 0;
          margin: 0 20px;
          text-align: center;
        }
        .export-btn {
          position: absolute;
          top: 105px;
          left: 750px;
        }
        .export-btn .dx-button .dx-button-content {
          padding: 6px 20px;
        }
        .user-field .dx-placeholder {
          color: #333;
        }
      `}</style>
    </div>
  );
};

export default ExpenseReport;