import { useEffect, useState, useRef } from 'react';

const PreviewReceipt = ({ src, zoomLevel = 1 }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [pdfSource, setPdfSource] = useState('');
  const isPdf = src.includes('data:application/pdf;base64,');
  const scrollContainerRef = useRef(null);

  useEffect(() => {
    if (isPdf) {
      const zoom = Math.round(zoomLevel * 100);
      setPdfSource(`${src}#toolbar=0&zoom=${zoom}`);
    } else {
      setPdfSource(src);
    }
  }, [zoomLevel, src]);

  // Direct wheel handler - no smoothing to avoid lag
  const handleWheel = (event) => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop += event.deltaY;
      if (
        scrollContainerRef.current.scrollHeight >
        scrollContainerRef.current.clientHeight
      ) {
        event.preventDefault();
      }
    }
  };

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        position: 'relative',
        backgroundColor: '#f0f0f0',
        overflow: 'auto',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      {isLoading && (
        <div
          style={{
            position: 'absolute',
            inset: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f0f0f0',
            zIndex: 1,
          }}
        >
          <span>Loading...</span>
        </div>
      )}

      {isPdf ? (
        <embed
          key={pdfSource}
          src={pdfSource}
          type="application/pdf"
          width="100%"
          height="100%"
          onLoad={() => setIsLoading(false)}
          onError={() => {
            console.error('Error loading PDF');
            setIsLoading(false);
          }}
        />
      ) : (
        <div
          ref={scrollContainerRef}
          style={{
            width: '100%',
            height: '100%',
            overflow: 'auto',
            position: 'relative',
          }}
          onWheel={handleWheel}
        >
          <img
            src={pdfSource}
            alt="Receipt Preview"
            style={{
              display: 'block',
              width: `${100 * zoomLevel}%`,
              height: 'auto',
              pointerEvents: 'auto',
            }}
            onLoad={() => setIsLoading(false)}
            onError={() => {
              console.error('Error loading image');
              setIsLoading(false);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default PreviewReceipt;
