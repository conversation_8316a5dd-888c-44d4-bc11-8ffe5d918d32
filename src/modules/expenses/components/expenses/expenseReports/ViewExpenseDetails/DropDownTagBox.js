import { forwardRef, useCallback, useEffect, useState } from 'react';
import DropDownBox from 'devextreme-react/drop-down-box';
import DataGrid, {
  Selection,
  FilterRow,
  Scrolling,
  Paging,
} from 'devextreme-react/data-grid';

const dropdownOptions = { width: '450px', height: '480px' };

const DropDownTagBox = forwardRef(
  (
    {
      dataSource,
      paramId,
      paramValue,
      values,
      setValue,
      disabled,
      placeholder,
      onClosed,
      children,
    },
    ref
  ) => {
    const [gridBoxValue, setGridBoxValue] = useState(values);
    useEffect(() => {
      setGridBoxValue(values.map((a) => a[paramId]));
    }, [values]);
    const dataGridOnSelectionChanged = useCallback(
      (e) => {
        setGridBoxValue((e.selectedRowKeys.length && e.selectedRowKeys) || []);
        setValue((e.selectedRowsData.length && e.selectedRowsData) || []);
      },
      [setValue]
    );
    const dataGridRender = () => {
      return (
        <DataGrid
          dataSource={dataSource}
          height={440}
          hoverStateEnabled={true}
          selectedRowKeys={gridBoxValue}
          onSelectionChanged={dataGridOnSelectionChanged}
          keyExpr={paramId}
        >
          <Selection
            mode="multiple"
            showCheckBoxesMode="always"
            allowSelectAll={false}
          />
          <Scrolling
            rowRenderingMode="virtual"
            mode="virtual"
            showScrollbar="always"
          />
          <Paging enabled={false} />
          <FilterRow visible={true} />
          {children}
        </DataGrid>
      );
    };

    const syncDataGridSelection = useCallback((e) => {
      setGridBoxValue(e.value || []);
    }, []);

    return (
      <DropDownBox
        value={gridBoxValue}
        valueExpr={paramId}
        deferRendering={false}
        displayExpr={paramValue}
        placeholder={placeholder ?? 'Select a value...'}
        showClearButton={false}
        dataSource={dataSource}
        onValueChanged={syncDataGridSelection}
        contentRender={dataGridRender}
        ref={ref}
        dropDownOptions={dropdownOptions}
        onClosed={onClosed}
        disabled={disabled ?? false}
      />
    );
  }
);

export default DropDownTagBox;
