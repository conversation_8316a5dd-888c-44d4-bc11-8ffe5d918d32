import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Button as DxButton } from 'devextreme-react/button';
import PreviewReceipt from '../PreviewReceipt';
import { ExpenseService } from '@services/expenseService';
import { getExpenseReceipt } from '@services/api';
import {
  NumberBox,
  SelectBox,
  TextArea,
  TextBox,
  ScrollView,
  LoadPanel,
  Popup,
} from 'devextreme-react';
import { Calendar } from 'primereact/calendar';
import { AutoComplete } from 'primereact/autocomplete';
import { Column } from 'devextreme-react/data-grid';
import { useDefaults } from '@contexts/defaults';
import { getCustomValBasedOnLabel } from 'src/utils';
import MealDialogTemplate from './mealDialog';
import TransportationDialogTemplate from '../../dialogs/transportationDialog';
import MilageDialogTemplate from '../../dialogs/milageDialog';
import { useMenu } from '@contexts/menu';
import { useExpense } from '@contexts/expense';
import * as dayjs from 'dayjs';
import ConfirmDialog from '../../dialogs/confirmDialog';
import { Toast } from 'primereact/toast';
import DropDownTagBox from './DropDownTagBox';

const expenseService = new ExpenseService();
const nightsAttr = { id: 'nights' };
const handleErrors = (response) => {
  if (response.status !== 200) {
    throw Error(response.statusText);
  }
  return response.data;
};
const setFormatParams = (element) => {
  if (element.expMerchant && typeof element.expMerchant === 'object') {
    element.expMerchantId = element.expMerchant.id;
    element.expMerchant = element.expMerchant.merchant;
  } else if (element.expMerchant && typeof element.expMerchant === 'string') {
    element.expMerchantId = '';
    element.expMerchant = element.expMerchant.trim();
  }
  if (element.expContacts && element.expContacts.length > 0) {
    element.expContacts = element.expContacts.map((item) => {
      return {
        contactId: typeof item.contactId !== 'number' ? 0 : item.contactId,
        contactName: item.contactName,
      };
    });
  }
  if (element.expCompanies && element.expCompanies.length > 0) {
    element.expCompanies = element.expCompanies.map((item) => {
      return {
        companyId: item.companyId,
        companyName: item.companyName,
      };
    });
  } else {
    element.expCompanies = [];
  }
  return element;
};
const customCompany = (updateExpData) => {
  if (updateExpData.expCompanies?.length > 0) {
    return updateExpData.expCompanies.map((itemNew) => {
      return {
        companyId: itemNew.companyId,
        companyName: itemNew.companyName,
      };
    });
  } else {
    return [];
  }
};

const position = { of: '#viewContainer' };

const ExpenseDetails = ({ selectedExpenseReport, onClose, expenseReport }) => {
  const [currentExpenseReportIndex, setCurrentExpenseReportIndex] = useState(0);
  const [currentExpenseIndex, setCurrentExpenseIndex] = useState(0);
  const [currentReceiptIndex, setCurrentReceiptIndex] = useState(0);
  const [expenseReports, setExpenseReports] = useState([]);
  const [expenses, setExpenses] = useState([]);
  const [receipt, setReceipt] = useState([]);
  const [categoryList, setCategoryList] = useState([]);
  const [companyList, setCompanyList] = useState([]);
  const [userList, setUserList] = useState([]);
  const [merchantList, setMerchantList] = useState([]);
  const [filteredMerchant, setFilteredMerchant] = useState(null);
  const [isValidate, setIsValidate] = useState(false);
  const calendarRef = useRef(null);
  const { user } = useMenu();
  const [confirmDialog, setConfirmDialog] = useState(false);
  const [confirmMode, setConfirmMode] = useState([]);
  const { defaults } = useExpense();
  const [loading, setLoading] = useState(false);
  const pendingUpdateRef = useRef(false);
  const originalExpenseDataRef = useRef(null);
  const [expenseData, setExpenseData] = useState({
    expReportId: 0,
    expAmount: 0,
    expTax1: 0,
    expTax: 0,
    expCategoryId: {
      REC_ID: 1,
      CATEGORY_NAME: 'Hotel',
      CATEGORY_BASE_ID: 1,
    },
    expCreatedBy: user.userName,
    expCreatedForId: { USER_ID: 1, USER_NAME: 'Admin' },
    expDescription: '',
    expNights: 0,
    expFromPlace: '',
    expToPlace: '',
    expMiles: 0,
    expRate: isNaN(milageRate) || milageRate <= 0 ? 1 : parseFloat(milageRate),
    expTravelMode: '',
    expMerchantId: '',
    expMerchant: '',
    expReimbursable: expenseNonReimbursableParam?.userParamStatus == 1 ? 0 : 1,
    expContacts: [],
    expCompanies: [],
  });
  const attendeeRef = useRef(null);
  const toast = useRef(null);
  const currentExpenseReport = expenseReports[currentExpenseReportIndex];
  const currentReceipt = receipt[currentReceiptIndex];

  const { milageRate, taxColumnVisibible, expenseNonReimbursableParam } =
    defaults;
  const [zoomLevel, setZoomLevel] = useState(1);
  const zoomIn = () => {
    setZoomLevel((prevZoom) =>
      Math.min(parseFloat((prevZoom + 0.1).toFixed(2)), 2)
    );
  };

  const zoomOut = () => {
    setZoomLevel((prevZoom) =>
      Math.max(parseFloat((prevZoom - 0.1).toFixed(2)), 0.5)
    );
  };

  const disableApprove = !currentExpenseReport?.approver?.some(
    (approver) => approver.approverId === user.userId
  );
  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();

  const [customTax1, customTax, customState] = getCustomValBasedOnLabel(
    CUSTOM_LABELS,
    ['IDS_EXP_TAX1', 'IDS_EXP_TAX2', 'IDS_STATE']
  );
  useEffect(() => {
    const currentExpenseData = expenses[currentExpenseIndex];
    if (!currentExpenseData) {
      return;
    }
    setZoomLevel(1);

    const updatedExpense = {
      expReportId: currentExpenseData?.expReportId,
      expCategoryId: {
        REC_ID: currentExpenseData?.expCategoryId,
        CATEGORY_NAME: currentExpenseData?.expCategory,
        CATEGORY_BASE_ID: currentExpenseData?.expCategoryBaseId,
      },
      expAmount: parseFloat(currentExpenseData?.expAmount ?? 0),
      expDescription: currentExpenseData?.expDescription,
      expCreatedBy: currentExpenseData?.expCreatedBy,
      expCreatedForId: {
        USER_ID: currentExpenseData?.expCreatedForId,
        USER_NAME: currentExpenseData?.expCreatedFor,
      },
      expDate:
        currentExpenseData.expCategoryBaseId === 1
          ? [
              dayjs.utc(currentExpenseData.expStartDate, 'MM-DD-YYYY').toDate(),
              dayjs.utc(currentExpenseData.expEndDate, 'MM-DD-YYYY').toDate(),
            ]
          : dayjs.utc(currentExpenseData.expStartDate, 'MM-DD-YYYY').toDate(),
      expNights: currentExpenseData?.expNights,
      expFromPlace: currentExpenseData?.expFromPlace,
      expToPlace: currentExpenseData?.expToPlace,
      expMiles: parseFloat(currentExpenseData?.expMiles ?? 0),
      expRate: parseFloat(currentExpenseData?.expRate ?? 0),
      expTravelMode: currentExpenseData?.expTravelMode,
      expMerchantId: currentExpenseData?.expMerchantId,
      expMerchant: currentExpenseData?.expMerchant,
      expReimbursable: currentExpenseData?.expReimbursable,
      expContacts: currentExpenseData?.expContacts,
      expCompanies: currentExpenseData?.expCompanies,

      expId: currentExpenseData?.expId,
      id: currentExpenseData?.id,
      expTax1: currentExpenseData?.expTax1,
      expTax: currentExpenseData?.expTax,
    };
    originalExpenseDataRef.current = JSON.stringify(updatedExpense);
    setExpenseData((prevState) => {
      return { ...prevState, ...updatedExpense };
    });
  }, [expenses, currentExpenseIndex]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [categoryList, userList, merchantList, companyList] =
          await Promise.all([
            expenseService.getExpenseCategory().then(handleErrors),
            expenseService.getAllUserList().then(handleErrors),
            expenseService.getMerchants().then(handleErrors),
            expenseService.getDropdowns('companies').then(handleErrors),
          ]);

        setCategoryList(categoryList);
        setUserList(userList);
        setMerchantList(merchantList);
        setCompanyList(companyList);
      } catch (error) {
        console.error('Failed to fetch expense category:', error);
      }
    };
    fetchData();
  }, []);
  const hasChanges = useCallback(() => {
    if (!originalExpenseDataRef.current) return false;

    const currentData = JSON.stringify(expenseData);
    return currentData !== originalExpenseDataRef.current;
  }, [expenseData]);
  const updateExpense = async (data) => {
    pendingUpdateRef.current = true;
    try {
      if (
        !data.expDescription ||
        data.expDescription.toString().trim() === ''
      ) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Description is required',
          life: 3000,
        });
        return;
      }
      const dataToUpdate = JSON.parse(JSON.stringify(data));
      const params = [dataToUpdate].map((element) => {
        const newElement = setFormatParams(element);
        return setUpdateParams(newElement, dataToUpdate);
      });

      const response = await expenseService.updateExpenseDetails(params);
      return response;
    } catch (error) {
      console.error('Error saving expense:', error);
      throw error;
    } finally {
      pendingUpdateRef.current = false;
    }
  };

  const onApproveIconClick = () => {
    const status = 2;
    const header = 'Expense Approval';
    const message = 'Confirm you wish to Approve the Expense Report ?';
    setConfirmMode({
      header,
      message,
      status,
      comment: '',
    });
    setConfirmDialog(true);
  };
  const onRejectIconClick = () => {
    const status = 3;
    const header = 'Expense Rejection';
    const message = 'Confirm you wish to Reject the Expense Report ?';
    setConfirmMode({
      header,
      message,
      status,
      comment: '',
    });
    setConfirmDialog(true);
  };

  const onConfirmDialogHide = () => {
    setConfirmDialog(false);
  };
  const fetchExpenses = async (reportId, expId) => {
    try {
      // Cancel any pending updates before fetching
      if (pendingUpdateRef.current) {
        // Consider adding a mechanism to cancel pending requests
        await new Promise((resolve) => setTimeout(resolve, 300));
      }

      const response = await expenseService.getExpenseList(
        'all',
        reportId,
        '',
        ''
      );
      const data = handleErrors(response);
      setExpenses(data);

      const indexToSet = expId
        ? data.findIndex((exp) => exp.expId === expId)
        : 0;
      setCurrentExpenseIndex(Math.max(0, indexToSet));
    } catch (error) {
      console.error('Failed to fetch expenses:', error);
      setExpenses([]);
    }
  };

  const fetchExpenseReceipt = async (expId) => {
    try {
      const modifiedQuery = `${expId}?download=true`;
      const response = await getExpenseReceipt(modifiedQuery);
      const data = handleErrors(response);
      setReceipt(data);
      setCurrentReceiptIndex(0);
    } catch (error) {
      console.error('Failed to fetch expense receipt:', error);
      setReceipt([]);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      const allExpenses = expenseReport.current.instance
        .getDataSource()
        .items()
        .filter((eachExpense) =>
          eachExpense?.approver?.some(
            (approver) => approver.approverId === user.userId
          )
        );

      setExpenseReports(allExpenses);

      const selectedExpReportId = selectedExpenseReport.expReportId;
      for (let i = 0; i < allExpenses.length; i++) {
        if (allExpenses[i].expReportId === selectedExpReportId) {
          setCurrentExpenseReportIndex(i);
          await fetchExpenses(allExpenses[i].expReportId, allExpenses[i].expId);
          break;
        }
      }
    };
    loadData();
  }, [selectedExpenseReport, expenseReport]);

  useEffect(() => {
    if (expenses.length > 0) {
      fetchExpenseReceipt(expenses[currentExpenseIndex]?.expId);
    }
  }, [expenses, currentExpenseIndex]);

  const handlePrevExpense = useCallback(async () => {
    if (pendingUpdateRef.current) {
      await new Promise((resolve) => {
        const checkUpdate = () => {
          if (!pendingUpdateRef.current) {
            resolve();
          } else {
            setTimeout(checkUpdate, 100);
          }
        };
        checkUpdate();
      });
    }
    setZoomLevel(1);
    if (hasChanges()) {
      try {
        await updateExpense(expenseData);
        await fetchExpenses(
          expenseData.expReportId,
          expenses[currentExpenseIndex - 1]?.expId
        );
      } catch (error) {
        console.error('Failed to save expense before navigating:', error);
      }
    }

    if (currentExpenseIndex > 0) {
      const newIndex = currentExpenseIndex - 1;
      setCurrentExpenseIndex(newIndex);
      fetchExpenseReceipt(expenses[newIndex].expId);
    }
  }, [currentExpenseIndex, expenses, hasChanges, expenseData]);

  const handleNextExpense = useCallback(async () => {
    if (loading) return;

    setLoading(true);

    try {
      if (pendingUpdateRef.current) {
        await new Promise((resolve, reject) => {
          const startTime = Date.now();
          const timeoutLimit = 5000;

          const checkUpdate = () => {
            if (!pendingUpdateRef.current) {
              resolve();
            } else if (Date.now() - startTime > timeoutLimit) {
              reject(new Error('Timeout waiting for pending update.'));
            } else {
              setTimeout(checkUpdate, 100);
            }
          };
          checkUpdate();
        }).catch((err) => console.error(err));
      }

      setZoomLevel(1);
      if (hasChanges()) {
        try {
          await updateExpense(expenseData); // Ensure expense is saved
          await fetchExpenses(
            expenseData.expReportId,
            expenses[currentExpenseIndex + 1]?.expId
          );
        } catch (error) {
          console.error('Failed to save expense before navigating:', error);
        }
      }
      if (currentExpenseIndex < expenses.length - 1) {
        const newIndex = currentExpenseIndex + 1;
        setCurrentExpenseIndex(newIndex);
        fetchExpenseReceipt(expenses[newIndex].expId);
      }
    } finally {
      setLoading(false);
    }
  }, [currentExpenseIndex, expenses, hasChanges, expenseData, loading]);

  const handlePrevReceipt = useCallback(() => {
    setZoomLevel(1);
    if (currentReceiptIndex > 0) {
      setCurrentReceiptIndex(currentReceiptIndex - 1);
    }
  }, [currentReceiptIndex]);

  const handleNextReceipt = useCallback(() => {
    setZoomLevel(1);
    if (currentReceiptIndex < receipt.length - 1) {
      setCurrentReceiptIndex(currentReceiptIndex + 1);
    }
  }, [currentReceiptIndex, receipt]);

  const handlePrevReport = useCallback(async () => {
    if (loading) return; // Prevent multiple clicks while loading

    setLoading(true);

    try {
      if (pendingUpdateRef.current) {
        await new Promise((resolve, reject) => {
          const startTime = Date.now();
          const timeoutLimit = 5000; // Max wait time: 5 seconds

          const checkUpdate = () => {
            if (!pendingUpdateRef.current) {
              resolve();
            } else if (Date.now() - startTime > timeoutLimit) {
              reject(new Error('Timeout waiting for pending update.'));
            } else {
              setTimeout(checkUpdate, 100);
            }
          };
          checkUpdate();
        }).catch((err) => console.error(err));
      }

      if (hasChanges()) {
        try {
          await updateExpense(expenseData); // Ensure expense is saved
          await fetchExpenses(
            expenseReports[currentExpenseReportIndex - 1]?.expReportId,
            expenseReports[currentExpenseReportIndex - 1]?.expId
          );
        } catch (error) {
          console.error('Failed to save expense before navigating:', error);
        }
      }

      if (currentExpenseReportIndex > 0) {
        setCurrentExpenseReportIndex(currentExpenseReportIndex - 1);
        setCurrentExpenseIndex(0);
      }
    } finally {
      setLoading(false); // Ensure loading state resets after execution
    }
  }, [
    currentExpenseReportIndex,
    expenseReports,
    fetchExpenses,
    setCurrentExpenseReportIndex,
    setCurrentExpenseIndex,
    expenseData,
    hasChanges,
    loading,
  ]);

  const handleNextReport = useCallback(async () => {
    if (loading) return; // Prevent multiple clicks while loading

    setLoading(true);

    try {
      if (pendingUpdateRef.current) {
        await new Promise((resolve, reject) => {
          const startTime = Date.now();
          const timeoutLimit = 5000; // Max wait time: 5 seconds

          const checkUpdate = () => {
            if (!pendingUpdateRef.current) {
              resolve();
            } else if (Date.now() - startTime > timeoutLimit) {
              reject(new Error('Timeout waiting for pending update.'));
            } else {
              setTimeout(checkUpdate, 100);
            }
          };
          checkUpdate();
        }).catch((err) => console.error(err));
      }

      if (hasChanges()) {
        try {
          await updateExpense(expenseData); // Ensure expense is saved
          await fetchExpenses(
            expenseReports[currentExpenseReportIndex + 1]?.expReportId,
            expenseReports[currentExpenseReportIndex + 1]?.expId
          );
        } catch (error) {
          console.error('Failed to save expense before navigating:', error);
        }
      }

      if (currentExpenseReportIndex < expenseReports.length - 1) {
        setCurrentExpenseReportIndex(currentExpenseReportIndex + 1);
        setCurrentExpenseIndex(0);
      }
    } finally {
      setLoading(false); // Ensure loading state resets after execution
    }
  }, [
    loading,
    currentExpenseReportIndex,
    expenseReports,
    fetchExpenses,
    setCurrentExpenseReportIndex,
    setCurrentExpenseIndex,
    expenseData,
    hasChanges,
  ]);

  const setUpdateParams = (element, updateExpData) => {
    const expDateArray = Array.isArray(updateExpData.expDate)
      ? updateExpData.expDate
      : updateExpData.expDate
      ? [updateExpData.expDate]
      : []; // Ensure it's always an array
    return {
      id: element.id,
      expAmount: updateExpData.expAmount ?? 0,
      expCategoryId: element.expCategoryId.REC_ID,
      expCreatedForId: element.expCreatedForId.USER_ID,
      expMerchantId:
        element.expCategoryId.CATEGORY_BASE_ID === 5
          ? ''
          : element.expMerchantId ?? 0,
      expMerchant:
        element.expCategoryId.CATEGORY_BASE_ID === 5 ? '' : element.expMerchant,
      expContacts: element.expContacts,
      expCompanies: customCompany(updateExpData),
      expDescription:
        updateExpData.expDescription?.toString().trim() === ''
          ? element.expDescription?.toString().trim()
          : updateExpData.expDescription?.toString().trim(),
      expStartDate:
        expDateArray.length === 0
          ? element.expStartDate
          : dayjs(expDateArray[0]).format('MM-DD-YYYY'),
      expEndDate:
        expDateArray.length === 0
          ? element.expEndDate
          : dayjs(expDateArray[1] ?? expDateArray[0]).format('MM-DD-YYYY'),
      expNights: element.expNights,
      expFromPlace: element.expFromPlace?.toString().trim(),
      expToPlace: element.expToPlace?.toString().trim(),
      expMiles: element.expMiles,
      expRate: element.expRate,
      expTravelMode: element.expTravelMode?.toString().trim(),
      expReimbursable:
        updateExpData.expReimbursable === ''
          ? element.expReimbursable
          : updateExpData.expReimbursable,
      expId: element.expId,
      expTax1: updateExpData.expTax1 ?? 0,
      expTax: updateExpData.expTax ?? 0,
    };
  };
  const handleDownload = useCallback(async () => {
    if (!currentReceipt?.imageUrl) {
      console.error('No receipt URL available for download.');
      return;
    }

    try {
      const blobUrl = currentReceipt.imageUrl;

      if (!blobUrl) return;

      const contentType = blobUrl.split(':')[1]?.split(';')[0] || 'image/png'; // Extract MIME type from Blob URL
      const fileExtension = contentType.split('/')[1] || 'png'; // Get file extension

      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `receipt-${currentReceipt.id || 'file'}.${fileExtension}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading the file:', error);
    }
  }, [currentReceipt]);

  const approveExpense = (e, data, comment) => {
    setConfirmDialog(false);
    const row = currentExpenseReport;
    const params = {
      id: row.id,
      expReportId: row.expReportId,
      submittedDate:
        confirmMode.status === 1 ? dayjs().format('MM/DD/YYYY HH:mm') : '',
      approvedDate:
        confirmMode.status !== 1 ? dayjs().format('MM/DD/YYYY HH:mm') : '',
      approvedStatus: confirmMode.status,
      comments: comment?.toString().trim(),
      createdFor: row.createdFor,
    };
    let statusText = '';
    if (confirmMode.status == 1) {
      statusText = 'Expense Submitted Successfully';
    } else if (confirmMode.status == 2) {
      statusText = 'Expense Approved Successfully';
    } else {
      statusText = 'Expense Rejected Successfully';
    }

    expenseService
      .approve(params)
      .then((response) => {
        if (response.status === 200) {
          onClose?.();
          expenseReport.current.instance.refresh();
          toast.current.show({
            severity: 'success',
            summary: 'Successful',
            detail: statusText,
            life: 3000,
          });
        }
      })
      .catch((ex) => {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: ex.response?.data,
          life: 10000,
        });
      });
  };
  const onDateChange = (e) => {
    let expDate;
    if (e?.CATEGORY_BASE_ID) {
      if (e.CATEGORY_BASE_ID === 1) {
        if (Array.isArray(expenseData.expDate)) {
          expDate = [
            dayjs(expenseData.expDate[0]).toDate(),
            dayjs(expenseData.expDate[1]).toDate(),
          ];
        } else {
          expDate = [
            dayjs(expenseData.expDate).toDate(),
            dayjs(expenseData.expDate).add(1, 'day').toDate(),
          ];
        }
      } else {
        if (Array.isArray(expenseData.expDate)) {
          expDate = dayjs(expenseData.expDate[0]).toDate();
        } else {
          expDate = dayjs(expenseData.expDate).toDate();
        }
      }
    }
    return expDate;
  };

  const onInputChange = useCallback(
    (e, name) => {
      if (e == null || e == undefined || categoryList.length == 0) return;
      if (name === 'expCategoryId') {
        e = categoryList.find((item) => item.REC_ID === e);
        if (e?.CATEGORY_BASE_ID === 5) {
          const expAmount = expenseData.expMiles * expenseData.expRate;
          setExpenseData((_expenseData) => ({
            ..._expenseData,
            [name]: e,
            expAmount: expAmount,
          }));
        }
      } else if (name === 'expCreatedForId') {
        e = userList.find((item) => item.USER_ID === e);
      }
      // For Meal and Entertainment make 2 dates
      const expDate = onDateChange(e);
      if (expDate) {
        setExpenseData((_expenseData) => ({
          ..._expenseData,
          [name]: e,
          expDate: expDate,
        }));
      } else if (name == 'expMiles' && expenseData.expRate) {
        const expAmount = e * expenseData.expRate;
        setExpenseData((_expenseDataNew) => ({
          ..._expenseDataNew,
          [name]: e,
          expAmount: expAmount,
        }));
      } else {
        setExpenseData((_expenseData) => ({
          ..._expenseData,
          [name]: e,
        }));
      }
    },
    [expenseData, categoryList, userList]
  );
  const searchMerchant = useCallback(
    (event) => {
      setTimeout(() => {
        const results = merchantList.filter((merch) =>
          merch.merchant.toLowerCase().startsWith(event.query.toLowerCase())
        );
        setFilteredMerchant(results);
      }, 250);
    },
    [merchantList, setFilteredMerchant]
  );

  const handleLeftReceiptDisable =
    currentReceiptIndex === 0 || receipt.length === 0;
  const handleRightReciptDisable =
    currentReceiptIndex === receipt.length - 1 || receipt.length === 0;
  const handleRightExpenseDisable =
    currentExpenseIndex === expenses.length - 1 || expenses.length === 0;
  const handleLeftExpenseDisable =
    currentExpenseIndex === 0 || expenses.length === 0;
  const handleRightExpReportDisable =
    currentExpenseReportIndex === expenseReports.length - 1;
  const handleLeftExpReportDisable = currentExpenseReportIndex === 0;

  const setAjValue = useCallback(
    (selectedCompanyIds) => {
      onInputChange(selectedCompanyIds, 'expCompanies');
    },
    [onInputChange]
  );
  const changeCategory = useCallback(
    (e) => {
      onInputChange(e.value, 'expCategoryId');
    },
    [onInputChange]
  );
  const changeNights = useCallback(
    (e) => {
      onInputChange(e.value, 'expNights');
    },
    [onInputChange]
  );
  const renderCategoryItems = () => {
    let filteredCategoryList = categoryList;

    const selectedCategoryId = expenseData.expCategoryId.REC_ID;

    filteredCategoryList = categoryList.filter((item) => {
      if (item.REC_ID === selectedCategoryId && item.CATEGORY_HIDE_FLAG === 1) {
        return true;
      }

      return item.CATEGORY_HIDE_FLAG !== 1;
    });

    return (
      <>
        <div className="formgrid grid m-0">
          <div className="field col">
            <label htmlFor="expCategoryId">Category</label>
            <SelectBox
              dataSource={filteredCategoryList}
              displayExpr="CATEGORY_NAME"
              valueExpr="REC_ID"
              value={expenseData?.expCategoryId?.REC_ID}
              onValueChanged={changeCategory}
              autoFocus

              // ref={categoryDropdownRef}
            />
          </div>
        </div>
        <div className="formgrid grid m-0">
          <div className="field col">
            <label htmlFor="expCreatedBy">Created By</label>

            <TextBox value={expenseData.expCreatedBy} readOnly={true} />
          </div>
          <div className="field col">
            <label htmlFor="expCreatedForId">Created For *</label>

            <SelectBox
              dataSource={userList}
              displayExpr="USER_NAME"
              valueExpr="USER_ID"
              value={expenseData.expCreatedForId?.USER_ID}
              onValueChanged={(e) => onInputChange(e.value, 'expCreatedForId')}
              placeholder="Select User"
              searchEnabled={true}
              searchMode="contains"
              searchExpr="USER_NAME"
              searchTimeout={200}
              minSearchLength={0}
              showDataBeforeSearch={true}
              focusStateEnabled={true}
              // ref={createdForDropdownRef}
              disabled={expenseData.expReportId !== 0}
            />
          </div>
        </div>

        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID !== 5 && (
            <div className="formgrid grid m-0">
              <div className="field col">
                <label htmlFor="expMerchant">Merchant</label>
                <br />
                <AutoComplete
                  value={expenseData.expMerchant}
                  suggestions={filteredMerchant}
                  completeMethod={searchMerchant}
                  field="merchant"
                  dropdown
                  placeholder="Select Merchant"
                  onChange={(e) => onInputChange(e.value, 'expMerchant')}
                  aria-label="User List"

                  // ref={merchantRef}
                />
              </div>
            </div>
          )}
        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID === 1 && (
            <div className="formgrid grid m-0">
              <div className="field col">
                <label htmlFor="expNights">Nights</label>
                <NumberBox
                  min={0}
                  max={99}
                  value={parseInt(expenseData.expNights)}
                  onValueChanged={changeNights}
                  elementAttr={nightsAttr}
                />
              </div>
            </div>
          )}
        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID === 2 && (
            <MealDialogTemplate
              data={expenseData}
              onChange={onInputChange}
              attendeeRef={attendeeRef}
              onAttendeesOpen={() => {}}
            />
          )}
        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID === 3 && (
            <MealDialogTemplate
              data={expenseData}
              onChange={onInputChange}
              attendeeRef={attendeeRef}
              onAttendeesOpen={() => {}}
            />
          )}
        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID === 4 && (
            <TransportationDialogTemplate
              data={expenseData}
              onChange={onInputChange}
            />
          )}
        {expenseData.expCategoryId &&
          expenseData.expCategoryId.CATEGORY_BASE_ID === 5 && (
            <MilageDialogTemplate data={expenseData} onChange={onInputChange} />
          )}
      </>
    );
  };
  const handleClose = async () => {
    if (pendingUpdateRef.current) {
      await new Promise((resolve) => {
        const checkUpdate = () => {
          if (!pendingUpdateRef.current) {
            resolve();
          } else {
            setTimeout(checkUpdate, 100);
          }
        };
        checkUpdate();
      });
    }
    if (
      !expenseData.expDescription ||
      expenseData.expDescription.trim() === ''
    ) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Description is required',
        life: 3000,
      });
      return;
    }
    if (hasChanges()) {
      try {
        await updateExpense(expenseData);
      } catch (error) {
        console.error('Failed to save expense before closing:', error);
      }
    }

    onClose?.();
  };
  const handleDateChange = (e) => onInputChange(e.value, 'expDate');

  const handleAmountChange = (e) => onInputChange(e.value, 'expAmount');

  const handleTax1Change = (e) => onInputChange(e.value, 'expTax1');

  const handleTaxChange = (e) => onInputChange(e.value, 'expTax');

  const handleCompaniesChange = (value) => setAjValue(value);

  const handleDescriptionChange = (e) => {
    onInputChange(e.value, 'expDescription');
  };

  const handleReimbursableChange = (e) =>
    onInputChange(e.value, 'expReimbursable');

  const handleCalendarSelect = () => {
    if (expenseData.expDate[1] === null) {
      calendarRef.current.hide(2);
    }
  };
  const renderCommonItems = () => {
    return (
      <>
        <div className="formgrid grid m-0">
          <div className="field col-12">
            <label htmlFor="date">Date</label>
            <Calendar
              id="range"
              ref={calendarRef}
              value={expenseData.expDate}
              onChange={handleDateChange}
              selectionMode={
                expenseData?.expCategoryId?.CATEGORY_BASE_ID === 1
                  ? 'range'
                  : 'single'
              }
              placeholder="MM/DD/YYYY"
              showIcon
              showOnFocus={false}
              panelClassName="text-sm"
              todayButtonClassName="m-0 "
              clearButtonClassName="m-0 p-button-danger "
              onSelect={handleCalendarSelect}
              readOnlyInput={true}
            />
          </div>
          <div className={`field ${taxColumnVisibible ? 'col-6' : 'col'}`}>
            <label htmlFor="expAmount">Amount</label>

            <NumberBox
              value={parseFloat(expenseData.expAmount)}
              onValueChanged={handleAmountChange}
              format="$ #,##0.00"
              min={0}
              max={9999999999}
              readOnly={expenseData.expCategoryId?.CATEGORY_BASE_ID === 5}
            />
          </div>
          <div className={`field ${taxColumnVisibible ? 'col-3' : 'hidden'}`}>
            <label htmlFor="expTax1"> {customTax1} </label>

            <NumberBox
              value={parseFloat(expenseData.expTax1)}
              onValueChanged={handleTax1Change}
              format="$ #,##0.00"
              min={0}
              max={9999999999}
            />
          </div>
          <div className={`field ${taxColumnVisibible ? 'col-3' : 'hidden'}`}>
            <label htmlFor="expTax">{customTax} </label>

            <NumberBox
              value={parseFloat(expenseData.expTax)}
              onValueChanged={handleTaxChange}
              format="$ #,##0.00"
              min={0}
              max={9999999999}
            />
          </div>
        </div>
        <div className="formgrid grid m-0">
          <div className="field col">
            <label htmlFor="expCompanies">Companies</label>
            <br />
            <DropDownTagBox
              dataSource={companyList}
              paramId="companyId"
              paramValue="companyName"
              values={expenseData.expCompanies}
              setValue={setAjValue}
              placeholder="Select Companies"
            >
              <Column dataField="companyName" caption="Name" />
              <Column dataField="companyAddress" caption="Address" />
              <Column dataField="companyCity" caption={customState} />
            </DropDownTagBox>
          </div>
        </div>
        <div className="formgrid grid m-0">
          <div className="field col">
            <label htmlFor="expDescription">Description *</label>
            <br />

            <TextArea
              value={expenseData.expDescription}
              // ref={textBox}
              onValueChanged={handleDescriptionChange}
              onKeyUp={() => {
                setIsValidate(false);
              }}
              maxLength={200}
              placeholder="Enter Description"
              required={true}
              minHeight="60px"
              autoResizeEnabled={true}
              className={isValidate ? 'border-red-500' : ''}
              name="expDescription"
              valueChangeEvent="keyup"
            />
          </div>
        </div>
        <div className="formgrid grid m-0">
          <div className="field col">
            <label htmlFor="expReimbursable">Reimbursable</label>
            <SelectBox
              dataSource={[
                {
                  label: 'Yes',
                  value: 1,
                },
                {
                  label: 'No',
                  value: 0,
                },
              ]}
              displayExpr="label"
              valueExpr="value"
              value={expenseData.expReimbursable}
              onValueChanged={handleReimbursableChange}
              placeholder="Select Option"

              // ref={reimbursableRef}
            />
          </div>
        </div>
      </>
    );
  };

  const renderPopup = () => {
    return (
      <div className="details-container ">
        <div className="flex surface-100 p-2 justify-content-between w-full">
          <div className="text-xl font-bold">
            Expense Report - {currentExpenseReport?.description}
          </div>
          <div>
            <DxButton
              hint="Approve"
              icon="todo"
              type="success"
              className="ml-2 approveBtn"
              onClick={onApproveIconClick}
              disabled={disableApprove}
            />
            <DxButton
              hint="Reject"
              icon="close"
              type="danger"
              className="mr-2 ml-2 rejectBtn"
              onClick={onRejectIconClick}
              disabled={disableApprove}
            />

            <DxButton
              hint="Previous Report"
              icon="chevronprev"
              onClick={handlePrevReport}
              className="border-none surface-100 ml-2"
              disabled={handleLeftExpReportDisable}
            />
            <DxButton
              hint="Next Report"
              icon="chevronnext"
              onClick={handleNextReport}
              className="border-none surface-100 ml-2"
              disabled={handleRightExpReportDisable}
            />
            <DxButton
              hint="Close"
              icon="close"
              onClick={handleClose}
              className="border-none surface-100 ml-2"
            />
          </div>
        </div>
        <div
          className="flex flex-column justify-center sticky "
          id="viewContainer"
          style={{ top: '50px' }}
        >
          <LoadPanel
            shadingColor="rgba(0,0,0,0.4)"
            position={position}
            visible={loading}
            showIndicator={true}
            shading={true}
            showPane={true}
            hideOnOutsideClick={false}
          />

          <div
            className="details-content bg-white flex"
            style={{ height: '70vh' }}
          >
            <div
              className="details-half flex flex-column"
              style={{ flex: 1, marginRight: '1rem' }}
            >
              <div className="flex justify-content-between p-2">
                <div className="text-lg">
                  Receipt{' '}
                  <span>
                    {receipt.length > 0
                      ? `${currentReceiptIndex + 1}/${receipt.length}`
                      : '0'}
                  </span>
                </div>
                <div className="flex items-center">
                  <DxButton
                    icon="download"
                    onClick={handleDownload}
                    className="border-none mr-2"
                    disabled={!currentReceipt?.imageUrl}
                  />
                  <DxButton
                    onClick={zoomOut}
                    className="dx-icon-zoom-out border-none mr-2"
                    disabled={!currentReceipt?.imageUrl}
                  />
                  <DxButton
                    onClick={zoomIn}
                    className="dx-icon-zoom-in border-none mr-2"
                    disabled={!currentReceipt?.imageUrl}
                  />
                  <DxButton
                    icon="chevronprev"
                    onClick={handlePrevReceipt}
                    className="border-none mr-2"
                    disabled={handleLeftReceiptDisable}
                  />
                  <DxButton
                    icon="chevronnext"
                    onClick={handleNextReceipt}
                    className="border-none"
                    disabled={handleRightReciptDisable}
                  />
                </div>
              </div>
              <div className="flex flex-1 overflow-auto h-full justify-content-center align-items-center">
                {currentReceipt?.imageUrl ? (
                  <PreviewReceipt
                    src={`${currentReceipt?.imageUrl}`}
                    zoomLevel={zoomLevel}
                  />
                ) : (
                  <div
                    className="no-receipt flex align-items-center justify-content-center"
                    style={{ height: '100%' }}
                  >
                    No receipt available
                  </div>
                )}
              </div>
            </div>
            {expenses && expenses.length > 0 && (
              <div
                style={{
                  flex: 1,
                  padding: '1rem',
                  borderLeft: '1px solid #ccc',
                }}
              >
                <div className="flex justify-content-between ">
                  <div className="text-lg">
                    Expense{' '}
                    <span>
                      {currentExpenseIndex + 1}
                      {expenses.length > 0 && `/${expenses.length}`}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <DxButton
                      icon="chevronprev"
                      onClick={handlePrevExpense}
                      className="border-none mr-2"
                      disabled={handleLeftExpenseDisable}
                    />
                    <DxButton
                      icon="chevronnext"
                      onClick={handleNextExpense}
                      className="border-none"
                      disabled={handleRightExpenseDisable}
                    />
                  </div>
                </div>
                <ScrollView
                  width="100%"
                  height="100%"
                  className={`pointer-events-${
                    disableApprove ? 'none' : 'auto'
                  }`}
                  style={{
                    cursor: disableApprove ? 'not-allowed' : 'auto',
                    opacity: disableApprove ? 0.5 : 1,
                  }}
                >
                  <div className="mt-2  pr-3 ">
                    {renderCategoryItems()}
                    {renderCommonItems()}
                  </div>
                </ScrollView>
              </div>
            )}
          </div>
        </div>
        {confirmDialog && (
          <ConfirmDialog
            confirmMode={confirmMode}
            data={currentExpenseReport}
            visible={confirmDialog}
            onHide={onConfirmDialogHide}
            onSave={approveExpense}
          />
        )}
      </div>
    );
  };
  return (
    <>
      <Toast ref={toast} baseZIndex={1600} />
      <Popup
        width="80vw"
        height="80vh"
        // height="auto"
        maxHeight="100%"
        showTitle={false}
        // titleComponent={renderTitle}
        // title="Edit Expense"
        dragEnabled={false}
        hideOnOutsideClick={false}
        visible={true}
        onHiding={onClose}
        shadingColor="#00000090"
        contentRender={renderPopup}
        wrapperAttr={{
          id: 'expenseDetails-popup',
        }}
        // style={{padding:0}}
      />
      <style global jsx>{`
        #expenseDetails-popup .dx-popup-content {
          padding: 0 !important;
        }
      `}</style>
    </>
  );
};

export default ExpenseDetails;
