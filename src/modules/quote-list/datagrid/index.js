import { DataGrid } from 'devextreme-react';
import { Column } from 'devextreme-react/data-grid';

const onCellPrepared = (e) => {
  if (e.rowType == 'group') {
    // add class called bg-primary
    e.cellElement.classList.add('bg-group');
    if (e.columnIndex != 0) {
      // Change the text of the "groupTitle" cell.
      e.cellElement.innerText = e.value;
      // Center the "Title" cell.
      e.cellElement.style.textAlign = 'center';
    }
  } else if (e.rowType == 'header') {
    e.cellElement.style.textAlign = 'center';
  }
};
const QuoteListGrid = ({ gridColumns, gridData, filterData }) => {
  return (
    <>
      <div className="w-full">
        <DataGrid
          dataSource={gridData.gridData}
          noDataText="No reports found"
          showBorders
          onCellPrepared={onCellPrepared}
        >
          {/* <GroupPanel visible={true} />
        <SearchPanel visible={true} />
        <Grouping autoExpandAll={false} /> */}
          {gridColumns.map((column, index) => (
            <Column
              key={column.dataField}
              {...column}
              cssClass="might-overflow"
            />
          ))}
        </DataGrid>
      </div>

      <style global jsx>{`
        .dx-datagrid-header-panel .dx-toolbar {
          margin: 0;
        }
        .dx-datagrid .dx-toolbar .dx-toolbar-items-container {
          background: #dede;
          color: #fff;
          background: #0078d7 -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.8)), to(rgba(255, 255, 255, 0)));
          color: #fff;
        }
        .dx-toolbar .dx-toolbar-before {
          width: 100%;
        }
        .dx-toolbar .dx-toolbar-item:last-child {
          display: flex;
          justify-content: center;
        }
        .bg-group {
          background: #0078d7 -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.8)), to(rgba(255, 255, 255, 0)));
          color: #fff;
        }
        .dx-datagrid-group-closed,
        .dx-datagrid-group-opened {
          color: #fff;
        }
        .dx-datagrid-headers .dx-header-row {
          background: #dedede !important;
          color: #555 !important;
        }
        .might-overflow:hover {
          text-overflow: clip;
          white-space: normal;
          word-break: break-all;
        }
      `}</style>
    </>
  );
};

export default QuoteListGrid;
