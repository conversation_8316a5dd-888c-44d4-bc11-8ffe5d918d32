import { forwardRef, useEffect, useState } from 'react';
import { Button, DateBox, RadioGroup, CheckBox } from 'devextreme-react';
import { useDefaults } from '@contexts/defaults';
import { DATE_FILTER_FORMAT } from 'src/constants';
import GridBox from '@components/dropdownTagBox/GridBox';
import { getCustomValBasedOnLabel } from 'src/utils';
import '../../../modules/quote-list/filters/index';

const Filter = forwardRef((props, ref) => {
  const {
    groupByItems,
    onViewBtnClick,
    onFilterBtnClick,
    filterData,
    paramData,
    params,
    setParams,
    onFilterDataChanged,
    showOnlyParentQuotes,
    setShowOnlyParentQuotes,
  } = props;
  const {
    defaults: { DATE_FORMAT, CUSTOM_LABELS },
  } = useDefaults();

  const [principalValue, setPrincipalValue] = useState(params.principals);
  const [customerValue, setCustomerValue] = useState(params.customers);
  const principalAttr = { id: 'principal-input' };
  const customerAttr = { id: 'customer-input' };

  const labelId = [
    'IDS_PRINCI',
    'IDS_CUSTOMER',
    'IDS_REGION',
    'IDS_STATE',
    'IDS_QUOTES',
  ];
  const [
    customPrincipal,
    customCustomer,
    customRegion,
    customState,
    customQuote,
  ] = getCustomValBasedOnLabel(CUSTOM_LABELS, labelId);
  console.log(customState);

  const cellSpanTemplate = (cellElement, cellInfo) => {
    cellElement.innerHTML = `<span title="${cellInfo.value}">${cellInfo.value}</span>`;
  };

  const companyColumns = [
    {
      dataField: 'compName',
      caption: 'Name',
      width: 450,
      filterOperations: ['contains'],
      cellTemplate: (cellElement, cellInfo) => {
        cellSpanTemplate(cellElement, cellInfo);
      },
    },

    {
      dataField: 'street',
      caption: 'Street',
      width: 200,
      filterOperations: ['contains'],
      cellTemplate: (cellElement, cellInfo) => {
        cellSpanTemplate(cellElement, cellInfo);
      },
    },
    {
      dataField: 'city',
      caption: 'City',
      width: 200,
      filterOperations: ['contains'],
      cellTemplate: (cellElement, cellInfo) => {
        cellSpanTemplate(cellElement, cellInfo);
      },
    },
  ];

  useEffect(() => {
    setParams((prev) => ({
      ...prev,
      principals: principalValue,
      customers: customerValue,
    }));
  }, [principalValue, customerValue, setParams]);

  return (
    <div className="p-0">
      <div className="grid h-full">
        <div className="col">
          <div className="w-full">
            <div className="w-full mb-3">
              <p className="mb-2">Group By</p>
              <RadioGroup
                ref={ref}
                defaultValue={groupByItems[0].value}
                items={groupByItems}
                valueExpr="value"
                name="priority"
                layout="horizontal"
                onValueChanged={(e) => onFilterDataChanged(e.value, 'priority')}
              />
            </div>
            <div className="flex items-center gap-4 mt-2 mb-3">
              <CheckBox
                text={`Show only Parent ${customQuote}`}
                type="checkbox"
                id="showOnlyParentQuotes"
                value={showOnlyParentQuotes}
                onValueChanged={(e) => setShowOnlyParentQuotes(e.value)}
              />
            </div>

            <div className="flex align-items-center mb-3">
              <div className="">
                <div className="mb-1">From</div>
                <div>
                  <DateBox
                    value={filterData.fromDate}
                    name="fromDate"
                    onValueChanged={(e) =>
                      onFilterDataChanged(e.value, 'fromDate')
                    }
                    displayFormat={DATE_FILTER_FORMAT[DATE_FORMAT]}
                    useMaskBehavior={true}
                  />
                </div>
              </div>
              <div className="ml-2">
                <div className="mb-1">To</div>
                <div>
                  <DateBox
                    value={filterData.toDate}
                    onValueChanged={(e) =>
                      onFilterDataChanged(e.value, 'toDate')
                    }
                    displayFormat={DATE_FILTER_FORMAT[DATE_FORMAT]}
                    name="toDate"
                    useMaskBehavior={true}
                  />
                </div>
              </div>
            </div>
            <div className="flex  mb-3">
              <div className="dx-field w-full gridBox mt-1 mb-0 ">
                <GridBox
                  gridDataSource={paramData.principalData}
                  gridColumns={companyColumns}
                  customState={customState}
                  boxName={customPrincipal}
                  gridBoxValue={principalValue}
                  setGridBoxValue={setPrincipalValue}
                  dataKey="recId"
                  dataText="compName"
                  elementAttr={principalAttr}
                  height={50}
                  width={1000}
                ></GridBox>
              </div>
              <div className="dx-field w-full gridBox mt-1 mb-0 ml-2">
                <GridBox
                  gridDataSource={paramData.customerData}
                  gridColumns={companyColumns}
                  customState={customState}
                  boxName={customCustomer}
                  gridBoxValue={customerValue}
                  setGridBoxValue={setCustomerValue}
                  dataKey="recId"
                  dataText="compName"
                  elementAttr={customerAttr}
                  height={50}
                  width={1000}
                ></GridBox>
              </div>
            </div>
            <div className="flex align-items-end ">
              <Button
                className="bg-primary"
                text="View Report"
                onClick={onViewBtnClick}
              />
              <Button
                className="bg-primary"
                text="More Filters"
                onClick={onFilterBtnClick}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

export default Filter;
