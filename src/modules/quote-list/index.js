import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Toast } from 'primereact/toast';
import QuoteListDatagrid from './datagrid';
import QuoteListFilter from './filters';
import { useDefaults } from '@contexts/defaults';
import {
  companyType,
  divisionCustomers,
  divisionPrincipals,
  divisionRegions,
  getQuoteList,
  principalCustomer,
  quoteStatusList,
} from '@services/api';
import { amountFormat, DATE_FILTER_FORMAT } from 'src/constants';
import {
  getCustomValBasedOnLabel,
  convertDateFormat,
  getTodaysDate,
  convertDayjsToJSObject,
} from 'src/utils';
import Rollups from './rollups';
import FilterDialog from './filterDialog';
const labelId = ['IDS_PRINCI', 'IDS_CUSTOMER', 'IDS_DISTRI', 'IDS_REGION'];

const quoteCellTemplate = (cellData) => {
  return (
    <a
      href={`/RepfabricCRM/opploop/quotes/NewQuote.xhtml?recId=${cellData.data.id}`}
      className="text-blue-600"
      target="_blank"
      title={cellData?.value}
    >
      {cellData?.value}
    </a>
  );
};

const QuoteList = () => {
  const {
    defaults: { CUSTOM_LABELS, DATE_FORMAT },
  } = useDefaults();
  const [customPrincipal, customCustomer, customDistri, customRegion] =
    getCustomValBasedOnLabel(CUSTOM_LABELS, labelId);

  const priorityEntities = useMemo(
    () => [
      { text: customPrincipal, value: 'princiName' },
      { text: customCustomer, value: 'custName' },
    ],
    [customCustomer, customPrincipal]
  );

  const [filterData, setFilterData] = useState({
    fromDate: convertDayjsToJSObject(getTodaysDate(), 'MM-dd-YYYY'),
    toDate: convertDayjsToJSObject(getTodaysDate(), 'MM-DD-YYYY'),
    priority: priorityEntities[0].value,
  });
  const [groupBy, setGroupBy] = useState(priorityEntities[0].value);
  const [gridData, setGridData] = useState([]);
  const [paramData, setParamData] = useState([]);
  const [params, setParams] = useState({
    principals: [],
    customers: [],
    regions: [],
    principalCustomers: [],
    companyTypes: [],
    quoteStatus: [],
  });
  const [filterDialog, setFilterDialog] = useState(false);
  const [showOnlyParentQuotes, setShowOnlyParentQuotes] = useState(true);

  const radioRef = useRef(null);
  const toast = useRef(null);

  const gridColumns = [
    {
      dataField: 'quoteNumber',
      caption: 'Quote Number',
      cellRender: quoteCellTemplate,
    },
    {
      dataField: 'quoteDate',
      caption: 'Quote Date',
      dataType: 'date',
      format: DATE_FILTER_FORMAT[DATE_FORMAT],
    },
    {
      dataField: 'custName',
      caption: customCustomer,
      groupIndex: groupBy == 'custName' ? 0 : undefined,
      sortOrder: groupBy == 'princiName' ? 'asc' : 'none',
    },
    {
      dataField: 'princiName',
      caption: customPrincipal,
      groupIndex: groupBy == 'princiName' ? 0 : undefined,
      sortOrder: groupBy == 'custName' ? 'asc' : 'none',
    },
    {
      dataField: 'distriName',
      caption: customDistri,
    },
    {
      dataField: 'custRegionName',
      caption: customRegion,
    },
    {
      dataField: 'insUserName',
      caption: 'Quote Creator',
    },
    {
      dataField: 'quoteValue',
      caption: 'Quote Value',
      format: amountFormat,
      dataType: 'number',
    },
    {
      dataField: 'custParentName',
      caption: 'Parent Company Customer',
    },
    {
      dataField: 'quoteBuyingGroup',
      caption: 'Buying Group',
    },
    {
      dataField: 'quoteNote',
      caption: 'Notes',
    },
    {
      dataField: 'quoteOpenStatusName',
      caption: 'Open Status',
    },
    {
      dataField: 'quoteDelivStatusName',
      caption: 'Quote Status',
    },
  ];

  const reportGenerateFunction = async () => {
    setFilterDialog(false);
    try {
      if (filterData.fromDate == null || filterData.toDate == null) {
        toast.current?.show({
          detail: 'Date should not be empty',
          severity: 'error',
          summary: 'Error',
        });
        return;
      }
      if (filterData.fromDate > filterData.toDate) {
        toast.current?.show({
          detail: 'From Date should be less than To Date',
          severity: 'error',
          summary: 'Error',
        });
        return;
      }

      setGroupBy(radioRef.current?.instance.option('value'));
      const res = await (
        await getQuoteList({
          fromDate: convertDateFormat(filterData.fromDate, 'YYYY/MM/DD'),
          toDate: convertDateFormat(filterData.toDate, 'YYYY/MM/DD'),
          principals: params.principals.map((item) => item.recId).toString(),
          customers: params.customers.map((item) => item.recId).toString(),
          parentCompany: params.principalCustomers
            .map((item) => item.recId)
            .toString(),
          custCompType: params.companyTypes
            .map((item) => item.compTypeId)
            .toString(),
          quoteStatus: params.quoteStatus.map((item) => item.REC_ID).toString(),
          region: params.regions.map((item) => item.id).toString(),
          groupByPrinci: filterData.priority == 'princiName' ? 1 : 0,
          showOnlyParentQuotes: showOnlyParentQuotes ? true : false,
        })
      ).data;
      setGridData(res);
    } catch (error) {
      console.log('Some error occured');
    }
  };

  const onFilterBtnClick = async () => {
    setFilterDialog(true);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const principalData = await (await divisionPrincipals()).data;
        const customerData = await (await divisionCustomers()).data;
        const principalCustomerData = await (await principalCustomer()).data;
        const companyTypeData = await (await companyType()).data;
        const quoteStatusData = await (await quoteStatusList()).data;
        const regionData = await (await divisionRegions()).data;


        setParamData({
          ...paramData,
          principalData: principalData,
          customerData: customerData,
          principalCustomerData: principalCustomerData,
          companyTypeData: companyTypeData,
          quoteStatusData: quoteStatusData,
          regionData: regionData,
          
          
        });
        reportGenerateFunction();
      } catch (error) {
        console.log('Some error occured');
      }
    };
    fetchData();
  }, []);

  const onFilterDataChanged = useCallback(
    (e, name) => {
      setFilterData((_filterData) => ({
        ..._filterData,
        [name]: e,
      }));
    },

    []
  );

  return (
    <>
      <Toast ref={toast} />
      <d className="flex justify-content-around my-4">
        <QuoteListFilter
          ref={radioRef}
          groupByItems={priorityEntities}
          filterData={filterData}
          paramData={paramData}
          params={params}
          setParams={setParams}
          onFilterDataChanged={onFilterDataChanged}
          onViewBtnClick={reportGenerateFunction}
          onFilterBtnClick={onFilterBtnClick}
          showOnlyParentQuotes={showOnlyParentQuotes}
          setShowOnlyParentQuotes={setShowOnlyParentQuotes}
        />
        <Rollups gridData={gridData} />
      </d>
      <QuoteListDatagrid
        gridColumns={gridColumns}
        gridData={gridData}
        filterData={filterData}
      />
      {filterDialog && (
        <FilterDialog
          onHide={() => {
            setFilterDialog(false);
          }}
          paramData={paramData}
          params={params}
          setParams={setParams}
          onViewBtnClick={reportGenerateFunction}
        />
      )}
      <style global jsx>
        {`
          .gridBox .dx-dropdowneditor-input-wrapper .dx-placeholder {
            font-weight: 400;
            color: #333;
          }
        `}
      </style>
    </>
  );
};

export default QuoteList;
