import { useEffect, useRef, useState } from 'react';
import { Toast } from 'primereact/toast';
import { Popup, Button } from 'devextreme-react';
import ScrollView from 'devextreme-react/scroll-view';
import { useDefaults } from '@contexts/defaults';
import GridBox from '@components/dropdownTagBox/GridBox';
import { getCustomValBasedOnLabel } from 'src/utils';

const updateBtnAttr = { id: 'update-btn' };
const cancelBtnAttr = { id: 'cancel-btn' };
const clearBtnAttr = { id: 'clear-btn' };
const principalAttr = { id: 'principal-option' };
const customerAttr = { id: 'customer-option' };
const regionAttr = { id: 'region-option' };
const statusAttr = { id: 'status-option' };

const cellSpanTemplate = (cellElement, cellInfo) => {
  cellElement.innerHTML = `<span title="${cellInfo.value}">${cellInfo.value}</span>`;
};

const companyColumns = [
  {
    dataField: 'compName',
    caption: 'Name',
    filterOperations: ['contains'],
    cellTemplate: (cellElement, cellInfo) => {
      cellSpanTemplate(cellElement, cellInfo);
    },
  },

  {
    dataField: 'street',
    caption: 'Street',
    filterOperations: ['contains'],
    cellTemplate: (cellElement, cellInfo) => {
      cellSpanTemplate(cellElement, cellInfo);
    },
  },
  {
    dataField: 'city',
    caption: 'City',
    filterOperations: ['contains'],
    cellTemplate: (cellElement, cellInfo) => {
      cellSpanTemplate(cellElement, cellInfo);
    },
  },
];

const regionColumns = [
  {
    dataField: 'region',
    caption: 'Name',
    filterOperations: ['contains'],
  },
];

const statusColumns = [
  {
    dataField: 'OPT_OPTION',
    caption: 'Status',
    filterOperations: ['contains'],
  },
];

const compTypeColumns = [
  {
    dataField: 'compTypeName',
    caption: 'Name',
    filterOperations: ['contains'],
  },
];

const FilterDialog = ({
  onHide,
  paramData,
  params,
  setParams,
  onViewBtnClick,
}) => {
  const toast = useRef(null);

  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();

  const [customRegion] = getCustomValBasedOnLabel(CUSTOM_LABELS, [
    'IDS_REGION',
  ]);

  const [principalCustomerValue, setPrincipalCustomerValue] = useState(
    params.principalCustomers
  );
  const [companyTypeValue, setCompanyTypeValue] = useState(params.companyTypes);
  const [quoteStatusValue, setQuoteStatusValue] = useState(params.quoteStatus);
  const [regionValue, setRegionValue] = useState(params.regions);

  useEffect(() => {
    setParams((prev) => ({
      ...prev,
      principalCustomers: principalCustomerValue,
      companyTypes: companyTypeValue,
      quoteStatus: quoteStatusValue,
      regions: regionValue,
    }));
  }, [
    principalCustomerValue,
    companyTypeValue,
    quoteStatusValue,
    regionValue,
    setParams,
  ]);

  const filterArrayFormat = (array, param) => {
    return array.length
      ? array
          .map((obj) => [param, '=', obj[param]])
          .reduce((acc, curr, currentIndex) => {
            if (currentIndex == 0) return acc.concat([curr]);
            return acc.concat(['or', curr]);
          }, [])
      : [];
  };

  const principalCustFilter = filterArrayFormat(
    principalCustomerValue,
    'recId'
  );

  const compTypeFilter = filterArrayFormat(companyTypeValue, 'compTypeId');

  const quoteStatusFilter = filterArrayFormat(quoteStatusValue, 'REC_ID');

  const regionFilter = filterArrayFormat(regionValue, 'id');

  const onClearBtnClick = () => {
    setPrincipalCustomerValue([]);
    setCompanyTypeValue([]);
    setQuoteStatusValue([]);
    setRegionValue([]);
  };

  const createDialogFooter = (
    <>
      <Button
        type="default"
        text="Clear Filter"
        icon="pi pi-refresh"
        onClick={onClearBtnClick}
        className="bg-primary mr-2"
        elementAttr={clearBtnAttr}
      />
      <Button
        type="default"
        text="View Report"
        icon="pi pi-check"
        onClick={onViewBtnClick}
        className="bg-primary mr-2"
        elementAttr={updateBtnAttr}
      />
      <Button
        type="danger"
        text="Cancel"
        icon="pi pi-times"
        className="bg-error mr-2"
        onClick={onHide}
        elementAttr={cancelBtnAttr}
      />
    </>
  );

  const renderPopup = () => {
    return (
      <>
        <Toast ref={toast} />
        <ScrollView width="100%" height="100%">
          <div className="mt-2 mb-7 pr-3">
            <div className="formgrid grid my-2">
              <div className="field col">
                <div className="dx-field w-full gridBox mt-1 mb-0 ">
                  <GridBox
                    gridDataSource={paramData.principalCustomerData}
                    gridColumns={companyColumns}
                    boxName="Parent Company"
                    gridBoxValue={principalCustomerValue}
                    setGridBoxValue={setPrincipalCustomerValue}
                    dataKey="recId"
                    dataText="compName"
                    defaultSelectionFilter={principalCustFilter}
                    elementAttr={principalAttr}
                    height={50}
                    width={500}
                  ></GridBox>
                </div>
              </div>

              <div className="field col">
                <div className="dx-field w-full gridBox mt-1 mb-0 ">
                  <GridBox
                    gridDataSource={paramData.companyTypeData}
                    gridColumns={compTypeColumns}
                    boxName="Customer Company Type"
                    gridBoxValue={companyTypeValue}
                    setGridBoxValue={setCompanyTypeValue}
                    dataKey="compTypeId"
                    dataText="compTypeName"
                    defaultSelectionFilter={compTypeFilter}
                    elementAttr={customerAttr}
                    height={50}
                    width={400}
                  ></GridBox>
                </div>
              </div>
            </div>

            <div className="formgrid grid my-2">
              <div className="field col">
                <div className="dx-field w-full gridBox mt-1 mb-0 ">
                  <GridBox
                    gridDataSource={paramData.quoteStatusData}
                    gridColumns={statusColumns}
                    boxName="Quote Status"
                    gridBoxValue={quoteStatusValue}
                    setGridBoxValue={setQuoteStatusValue}
                    fieldName="region"
                    dataKey="REC_ID"
                    dataText="OPT_OPTION"
                    defaultSelectionFilter={quoteStatusFilter}
                    elementAttr={statusAttr}
                    height={50}
                    width={400}
                  ></GridBox>
                </div>
              </div>

              <div className="field col">
                <div className="dx-field w-full gridBox mt-1 mb-0 ">
                  <GridBox
                    gridDataSource={paramData.regionData}
                    gridColumns={regionColumns}
                    boxName={customRegion}
                    gridBoxValue={regionValue}
                    setGridBoxValue={setRegionValue}
                    fieldName="region"
                    dataKey="id"
                    dataText="region"
                    defaultSelectionFilter={regionFilter}
                    elementAttr={regionAttr}
                    height={50}
                    width={400}
                  ></GridBox>
                </div>
              </div>
            </div>
          </div>
        </ScrollView>
        <div className="p-dialog-footer popup text-right">
          {createDialogFooter}
        </div>
      </>
    );
  };

  return (
    <Popup
      width={500}
      height="auto"
      maxHeight="100%"
      showTitle={true}
      title="More Filters"
      dragEnabled={false}
      hideOnOutsideClick={false}
      visible={true}
      onHiding={onHide}
      shadingColor="#00000090"
      contentRender={renderPopup}
    />
  );
};

export default FilterDialog;
