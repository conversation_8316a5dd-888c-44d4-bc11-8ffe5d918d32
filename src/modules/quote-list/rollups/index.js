import { DataGrid } from 'devextreme-react';
import { Column, Item, Toolbar } from 'devextreme-react/data-grid';
import { amountFormat } from 'src/constants';
const loadPanelAttr = { enabled: false };
const onCellPrepared = (e) => {
  // If the cell is a header cell and align cell text to center
  if (e.rowType == 'header') {
    e.cellElement.style.textAlign = 'center';
  }
};
const Rollups = ({ gridData }) => {
  return (
    <DataGrid
      dataSource={gridData.rollups}
      noDataText="No reports found"
      showBorders
      showRowLines
      width={500}
      loadPanel={loadPanelAttr}
      onCellPrepared={onCellPrepared}
    >
      <Column dataField="status" caption="Status" alignment="left" />
      <Column
        dataField="value"
        caption="Amount"
        alignment="right"
        format={amountFormat}
        dataType="number"
      />
      <Column dataField="count" caption="Count" alignment="right" />
      <Toolbar>
        <Item location="before">
          <h5>Quotation Rollup Report</h5>
        </Item>
      </Toolbar>
    </DataGrid>
  );
};

export default Rollups;
