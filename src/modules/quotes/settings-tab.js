import { useState, useEffect, useRef } from 'react';
import {
  Switch,
  SelectBox,
  Button,
  TextBox,
  RadioGroup,
  NumberBox,
} from 'devextreme-react';
import List, { ItemDragging } from 'devextreme-react/list';
import { getMedMenuEnabled } from '@services/api';
const quoteType = [
  { id: false, text: 'Regular Quote' },
  { id: true, text: 'Super Quote' },
];
const templateTypeList = [
  { id: 'quote', text: ' Quote' },
  { id: 'salesOrder', text: 'Sales Order' },
  { id: 'medical', text: 'Medical' },
];
const addNewBtnAttr = { id: 'add-new-btn' };
const saveBtnAttr = { id: 'save-btn' };
const saveAsBtnAttr = { id: 'save-as-btn' };
const previewBtnAttr = { id: 'preview-btn' };
const deleteBtnAttr = { id: 'delete-btn' };
const selectTemplateAttr = { id: 'select-template' };
const templateNameAttr = { id: 'template-name' };
const quoteTypeAttr = { id: 'quote-type' };
const templateTypeAttr = { id: 'sales-order' };
const nameAttr = { id: 'include-name' };
const signatureAttr = { id: 'include-singature' };
const defTemplateAttr = { id: 'def-template' };
const topicVisibleAttr = { id: 'topic_visible' };
const customAddressAttr = { id: 'custom-address' };
const secCustomerAttr = { id: 'sec-customer' };
const groupByManAttr = { id: 'group-by-man' };
const descAttr = { id: 'desc' };
const descWrapAttr = { id: 'desc-wrap' };
const noteVisibleAttr = { id: 'note-visible' };
const noteFooterVisibleAttr = { id: 'note-footer-visible' };
const manufacturerFooterNoteVisibleAttr = { id: 'man-footer-note-visible' };
const preparedByAttr = { id: 'prepared-by' };
const salesTeamAttr = { id: 'sales-team' };
const totalWeightAttr = { id: 'total-weight' };
const titleManAttr = { id: 'title-man' };
const alignCompAttr = { id: 'align-comp' };
const hideLineAttr = { id: 'hide-line' };
const displayFooterAttr = { id: 'footer-display' };
const chooseBtnAttr = { id: 'choose-btn' };
const partLinkToSpecAttr = { id: 'link-btn' };
const totalLabelForCustomerAttr = { id: 'total-label-customer' };
const totalLabelForDistributorAttr = { id: 'total-label-distributor' };
const unitPriceDecimalAttr = { id: 'unit-price-decimal' };

const onDecimalDigitKeyDown = (e) => {
  const { event } = e;
  const str = event.key;
  if (/^[.,e]$/.test(str)) {
    event.preventDefault();
  }
  if (event.key === 'Backspace' || event.key === 'Delete') {
    const inputElement = event.target;
    setTimeout(() => {
      if (inputElement.value === '') {
        inputElement.value = 2;
        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
      }
    }, 0);
  }
};
const SettingsTab = ({
  allColumnList,
  quoteLabels,
  onPreviewBtnClicked,
  onSaveBtnClicked,
  onSaveAsBtnClicked,
  onUpdateBtnClicked,
  onDeleteBtnClicked,
  onDragStart,
  onAdd,
  onRemove,
  onReorder,
  selectedColumns,
  disableDescWrapSwitch,
  pdfTemplate,
  visible,
  setVisible,
  onInputChange,
  selectedTemplate,
  setSelectedTemplate,
  onTemplateSelChanged,
  isValidate,
  setIsValidate,
  validationClicked,
  columns,
  setColumns,
  onSwitchChange,
  onClickList,
  previewLogo,
  defaultTemplateShow,
  customPrinci,
  customUnitPrice,
}) => {
  const textBox = useRef(null);
  const [enableTextBox, setEnableTextBox] = useState(false);
  const [medEnable, setMedEnable] = useState(0);

  useEffect(() => {
    textBox.current?.instance.focus();
  }, [selectedTemplate, enableTextBox]);

  useEffect(() => {
    const medMenuEnable = async () => {
      const result = await (await getMedMenuEnabled()).data;
      setMedEnable(result);
    };
    medMenuEnable();
  }, []);

  const onAddBtnClicked = () => {
    setIsValidate(false);
    setEnableTextBox(true);
    setVisible({
      topicVisible: true,
      partLinkToSpec: false,
      desc: false,
      descWrap: true,
      noteVisible: false,
      noteFooterVisible: false,
      manufacturerFooterNoteVisible: false,
      templateName: '',
      logo: '',
      previewURL: previewLogo,
      customAddress: false,
      companyName: '',
      companyAddress: '',
      superQuote: false,
      defaultTemplate: false,
      templateType: 'quote',
      includeName: false,
      includeSignature: false,
      groupByMan: false,
      showSecCustomer: false,
      compInfoAlignRight: false,
      titleShowManufacturer: false,
      totalLabelForCustomer: '',
      totalLabelForDistributor: '',
      unitPriceDecimalDigit: 2,
    });
    setColumns({
      allColumns: quoteLabels,
      selColumns: [],
    });
    setSelectedTemplate('');
  };

  const disableSettingSection = !(selectedTemplate || enableTextBox);

  const onClickSubmit = () => {
    if (visible.templateName?.toString().trim() == '') {
      validationClicked();
    } else if (selectedTemplate) {
      onUpdateBtnClicked();
    } else {
      onSaveBtnClicked();
    }
  };

  const handleUnitPriceDecimalDigitChange = (e) => {
    // check whether e.value is null
    if (e.value === null) {
      onInputChange(0, 'unitPriceDecimalDigit');
    } else {
      onInputChange(e.value, 'unitPriceDecimalDigit');
    }
  };

  return (
    <aside className="quote-sidebar  form">
      <div className="dx-fieldset m-3 ">
        <div className="dx-field">
          <div className="dx-field-label w-75">
            {customPrinci} Default Template
          </div>
          <div className="dx-field-value w-25">
            <Button
              text="Choose"
              type="default"
              onClick={defaultTemplateShow}
              height="35"
              name="choose"
              className="choose-btn"
              elementAttr={chooseBtnAttr}
            />
          </div>
          <div className="dx-field-label quote-note-visibility-label ">
            <SelectBox
              placeholder="Select PDF Template"
              className="mt-1"
              dataSource={pdfTemplate}
              displayExpr="templateName"
              value={selectedTemplate}
              valueExpr="templateId"
              searchEnabled={true}
              searchMode="contains"
              searchExpr="templateName"
              searchTimeout={200}
              minSearchLength={0}
              showDataBeforeSearch={true}
              onValueChanged={onTemplateSelChanged}
              focusStateEnabled={false}
              name="pdfTemplate"
              elementAttr={selectTemplateAttr}
            />
          </div>

          <div className="dx-field-label  quote-settings-value mt-1">
            <Button
              hint="New Template"
              type="default"
              stylingMode="contained"
              onClick={onAddBtnClicked}
              width="40"
              height="35"
              className="text-xs"
              icon="add"
              name="addNew"
              elementAttr={addNewBtnAttr}
            />
          </div>
        </div>
      </div>

      <div className="dx-fieldset">
        <div className="dx-field">
          {(enableTextBox || selectedTemplate) && (
            <>
              <div className="dx-field-label quote-note-visibility-label ">
                <TextBox
                  value={visible.templateName}
                  onValueChange={(e) => {
                    onInputChange(e, 'templateName');
                  }}
                  onKeyUp={() => {
                    setIsValidate(false);
                  }}
                  ref={textBox}
                  focusStateEnabled
                  placeholder="Template Name"
                  className={isValidate ? 'border-red-500' : ''}
                  name="templateName"
                  elementAttr={templateNameAttr}
                />
                {isValidate && (
                  <div className="dx-item dx-validationsummary-item mt-2">
                    <div className="dx-item-content dx-validationsummary-item-content">
                      Please enter Template Name
                    </div>
                  </div>
                )}
              </div>
              <div className="dx-field-label  quote-settings-value">
                <Button
                  hint={selectedTemplate ? 'Save Updates' : 'Save'}
                  type="success"
                  stylingMode="contained"
                  useSubmitBehavior={true}
                  onClick={onClickSubmit}
                  width="40"
                  height="35"
                  className="text-xs"
                  icon="save"
                  disabled={!selectedColumns.length}
                  name="updateTemplate"
                  elementAttr={saveBtnAttr}
                />
              </div>

              <div className="dx-field-label">Quote type</div>
              <div className="dx-field-value w-full">
                <RadioGroup
                  items={quoteType}
                  // defaultValue={quoteType[0]}
                  layout="horizontal"
                  value={visible.superQuote}
                  onValueChanged={(e) => {
                    onInputChange(e.value, 'superQuote');
                  }}
                  valueExpr="id"
                  displayExpr="text"
                  disabled={!!selectedTemplate}
                  elementAttr={quoteTypeAttr}
                />
                {/* Message update for Client on Quote template selection */}
                <div className="note-section">
                  <p>
                    Note : While generating Quote PDF, only the applicable
                    templates will be listed based on the Quote type.
                  </p>
                </div>
              </div>

              <div className="dx-field-label quote-note-visibility-label white-space-normal">
                Default Template
              </div>

              <div className="dx-field-label  quote-settings-value">
                <Switch
                  value={visible.defaultTemplate}
                  onValueChanged={() => {
                    onSwitchChange(visible.defaultTemplate, 'defaultTemplate');
                  }}
                  switchedOffText="NO"
                  switchedOnText="YES"
                  className="text-sm"
                  elementAttr={defTemplateAttr}
                />
              </div>
            </>
          )}
        </div>
      </div>
      <div className="quote-setting-content" disabled={disableSettingSection}>
        <h3 className="sub-head">Settings</h3>
        <div className="dx-fieldset flex justify-content-between mx-6">
          {selectedTemplate && (
            <>
              <Button
                hint="Save As"
                type="default"
                stylingMode="contained"
                onClick={onSaveAsBtnClicked}
                width="40"
                height="35"
                className="text-xs save-as-btn"
                icon="save"
                disabled={!selectedColumns.length}
                elementAttr={saveAsBtnAttr}
              />
              <Button
                hint="Preview"
                type="default"
                stylingMode="contained"
                onClick={onPreviewBtnClicked}
                width="40"
                height="35"
                className="text-xs preview-btn"
                icon="pdffile"
                disabled={!selectedColumns.length}
                elementAttr={previewBtnAttr}
              />

              <Button
                hint="Delete"
                type="danger"
                stylingMode="contained"
                onClick={onDeleteBtnClicked}
                width="40"
                height="35"
                className="text-xs"
                icon="close"
                disabled={!selectedColumns.length}
                elementAttr={deleteBtnAttr}
              />
            </>
          )}
        </div>
        <div className="dx-fieldset ">
          {medEnable === 1 && (
            <div className="dx-field">
              <RadioGroup
                items={templateTypeList}
                layout="horizontal"
                value={visible.templateType}
                onValueChanged={(e) => {
                  onInputChange(e.value, 'templateType');
                }}
                valueExpr="id"
                displayExpr="text"
                className="my-2"
                elementAttr={templateTypeAttr}
              />
              {(visible.templateType == 'salesOrder' ||
                visible.templateType == 'medical') && (
                <div className="dx-field">
                  <div className="dx-field-label quote-note-visibility-label white-space-normal">
                    Include Name
                  </div>

                  <div className="dx-field-label  quote-settings-value">
                    <Switch
                      value={visible.includeName}
                      onValueChanged={() => {
                        onSwitchChange(visible.includeName, 'includeName');
                      }}
                      switchedOffText="NO"
                      switchedOnText="YES"
                      className="text-sm"
                      elementAttr={nameAttr}
                    />
                  </div>
                  <div className="dx-field-label quote-note-visibility-label white-space-normal">
                    Include Signature
                  </div>

                  <div className="dx-field-label  quote-settings-value">
                    <Switch
                      value={visible.includeSignature}
                      onValueChanged={() => {
                        onSwitchChange(
                          visible.includeSignature,
                          'includeSignature'
                        );
                      }}
                      switchedOffText="NO"
                      switchedOnText="YES"
                      className="text-sm"
                      elementAttr={signatureAttr}
                    />
                  </div>
                </div>
              )}
            </div>
          )}
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Quote Topic
            </div>
            <div className="dx-field-label  quote-settings-value">
              <Switch
                value={visible.topicVisible}
                onValueChanged={() => {
                  onSwitchChange(visible.topicVisible, 'topicVisible');
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                className="text-sm"
                elementAttr={topicVisibleAttr}
              />
            </div>
          </div>
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Modify Client Address
            </div>
            <div className="dx-field-label  quote-settings-value">
              <Switch
                value={visible.customAddress}
                onValueChanged={() => {
                  onSwitchChange(visible.customAddress, 'customAddress');
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                elementAttr={customAddressAttr}
              />
            </div>
          </div>
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Show Secondary Customer
            </div>
            <div className="dx-field-label  quote-settings-value">
              <Switch
                value={visible.showSecCustomer}
                onValueChanged={() => {
                  onSwitchChange(visible.showSecCustomer, 'showSecCustomer');
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                className="text-sm"
                elementAttr={secCustomerAttr}
              />
            </div>
          </div>
          {visible.superQuote && (
            <div className="dx-field">
              <div className="dx-field-label quote-note-visibility-label white-space-normal">
                Group by Manufacturers
              </div>
              <div className="dx-field-label  quote-settings-value">
                <Switch
                  value={visible.groupByMan}
                  onValueChanged={() => {
                    onSwitchChange(visible.groupByMan, 'groupByMan');
                  }}
                  switchedOffText="NO"
                  switchedOnText="YES"
                  className="text-sm"
                  disabled={disableDescWrapSwitch}
                  elementAttr={groupByManAttr}
                />
              </div>
            </div>
          )}
        </div>
        <div className="dx-fieldset">
          <div className="dx-fieldset-header text-lg">Choose Columns</div>
          <div className="dx-field quote-column-chooser-tab">
            <div className="dx-field-label quote-column-chooser">
              <p className="quote-list-title text-sm">All Columns</p>
              <List
                dataSource={allColumnList}
                keyExpr="id"
                displayExpr="name"
                repaintChangesOnly={true}
                indicateLoading={true}
                noDataText="No columns"
                height="250"
                scrollingEnabled={true}
                onItemClick={(e) => {
                  e.fromData = 'allColumns';
                  e.toData = 'selColumns';
                  e.toIndex = columns.selColumns.length;
                  onClickList(e);
                }}
                // showScrollbar="always"
              >
                <ItemDragging
                  allowReordering={true}
                  group="tasks"
                  data="allColumns"
                  onDragStart={onDragStart}
                  onAdd={onAdd}
                  onRemove={onRemove}
                  onReorder={onReorder}
                />
              </List>
            </div>
            <div className="dx-field-label quote-column-chooser">
              <p className="quote-list-title text-sm">Selected Columns</p>
              <List
                dataSource={selectedColumns}
                keyExpr="id"
                displayExpr="name"
                repaintChangesOnly={true}
                noDataText="none selected"
                height="250"
                scrollingEnabled={true}
                // showScrollbar="always"
                onItemClick={(e) => {
                  e.fromData = 'selColumns';
                  e.toData = 'allColumns';
                  e.toIndex = columns.allColumns.length;
                  onClickList(e);
                }}
              >
                <ItemDragging
                  allowReordering={true}
                  group="tasks"
                  data="selColumns"
                  onDragStart={onDragStart}
                  onAdd={onAdd}
                  onRemove={onRemove}
                  onReorder={onReorder}
                />
              </List>
            </div>
          </div>
        </div>
        <div className="dx-fieldset ">
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Part # Link to spec
            </div>
            <div className="dx-field-label  quote-settings-value">
              <Switch
                value={visible.partLinkToSpec}
                onValueChanged={() => {
                  onSwitchChange(visible.partLinkToSpec, 'partLinkToSpec');
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                disabled={disableDescWrapSwitch}
                elementAttr={partLinkToSpecAttr}
              />
            </div>
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Description below Part Number
            </div>
            <div className="dx-field-label  quote-settings-value">
              <Switch
                value={visible.desc}
                onValueChanged={() => {
                  onSwitchChange(visible.desc, 'desc');
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                disabled={disableDescWrapSwitch}
                elementAttr={descAttr}
              />
            </div>
            {visible.desc && (
              <>
                <div className="dx-field-label quote-note-visibility-label white-space-normal">
                  Description Wrap
                </div>
                <div className="dx-field-label  quote-settings-value">
                  <Switch
                    value={visible.descWrap}
                    onValueChanged={() => {
                      onSwitchChange(visible.descWrap, 'descWrap');
                    }}
                    switchedOffText="NO"
                    switchedOnText="YES"
                    disabled={disableDescWrapSwitch}
                    elementAttr={descWrapAttr}
                  />
                </div>
              </>
            )}
          </div>
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Line Item Note
            </div>
            <div className="dx-field-label  quote-settings-value">
              <Switch
                value={visible.noteVisible}
                onValueChanged={() => {
                  onSwitchChange(visible.noteVisible, 'noteVisible');
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                disabled={!selectedColumns.length}
                elementAttr={noteVisibleAttr}
              />
            </div>
          </div>
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Extended Price - Custom label
            </div>
            <div className="dx-field w-12 pl-2">
              <div className="dx-field-label w-6 text-center white-space-normal">
                For Customers:
              </div>
              <div className="dx-field-label p-0 w-6  quote-settings-value">
                <TextBox
                  value={visible.totalLabelForCustomer}
                  onValueChange={(e) => {
                    onInputChange(e, 'totalLabelForCustomer');
                  }}
                  name="totalLabelForCustomer"
                  placeholder=""
                  elementAttr={totalLabelForCustomerAttr}
                />
              </div>
            </div>
            <div className="dx-field w-12 pl-2">
              <div className="dx-field-label w-6 text-center white-space-normal">
                For Distributors:
              </div>
              <div className="dx-field-label p-0 w-6  quote-settings-value">
                <TextBox
                  value={visible.totalLabelForDistributor}
                  onValueChange={(e) => {
                    onInputChange(e, 'totalLabelForDistributor');
                  }}
                  name="totalLabelForDistributor"
                  placeholder=""
                  elementAttr={totalLabelForDistributorAttr}
                />
              </div>
            </div>
          </div>
          <div className="dx-field w-12 flex align-items-center">
            <div className="dx-field-label w-6 mr-2  white-space-normal">
              Decimal digits for {customUnitPrice}
            </div>
            <div className="dx-field-label p-0 w-6  quote-settings-value">
              <NumberBox
                defaultValue={2}
                min={2}
                max={6}
                showSpinButtons={true}
                value={visible.unitPriceDecimalDigit}
                onKeyDown={onDecimalDigitKeyDown}
                onValueChanged={handleUnitPriceDecimalDigitChange}
                inputAttr={unitPriceDecimalAttr}
              />
            </div>
          </div>
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Quote Comments
            </div>
            <div className="dx-field-label quote-settings-value">
              <Switch
                value={visible.noteFooterVisible}
                onValueChanged={() => {
                  onSwitchChange(
                    visible.noteFooterVisible,
                    'noteFooterVisible'
                  );
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                disabled={!selectedColumns.length}
                elementAttr={noteFooterVisibleAttr}
              />
            </div>
          </div>
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Footer note (Mfg.)
            </div>
            <div className="dx-field-label quote-settings-value">
              <Switch
                value={visible.manufacturerFooterNoteVisible}
                onValueChanged={() => {
                  onSwitchChange(
                    visible.manufacturerFooterNoteVisible,
                    'manufacturerFooterNoteVisible'
                  );
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                disabled={!selectedColumns.length}
                elementAttr={manufacturerFooterNoteVisibleAttr}
              />
            </div>
          </div>
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Prepared By
            </div>
            <div className="dx-field-label quote-settings-value">
              <Switch
                value={visible.showPreparedBy}
                onValueChanged={() => {
                  onSwitchChange(visible.showPreparedBy, 'showPreparedBy');
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                disabled={!selectedColumns.length}
                elementAttr={preparedByAttr}
              />
            </div>
          </div>
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Show Sales Team (Acc.Manager)
            </div>
            <div className="dx-field-label quote-settings-value">
              <Switch
                value={visible.showSalesTeam}
                onValueChanged={() => {
                  onSwitchChange(visible.showSalesTeam, 'showSalesTeam');
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                disabled={!selectedColumns.length}
                elementAttr={salesTeamAttr}
              />
            </div>
          </div>
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Show Total Weight
            </div>
            <div className="dx-field-label quote-settings-value">
              <Switch
                value={visible.showTotalWeight}
                onValueChanged={() => {
                  onSwitchChange(visible.showTotalWeight, 'showTotalWeight');
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                disabled={!selectedColumns.length}
                elementAttr={totalWeightAttr}
              />
            </div>
          </div>
          {!visible.superQuote && (
            <div className="dx-field">
              <div className="dx-field-label quote-note-visibility-label white-space-normal">
                Show Manufacturer in title
              </div>
              <div className="dx-field-label quote-settings-value">
                <Switch
                  value={visible.titleShowManufacturer}
                  onValueChanged={() => {
                    onSwitchChange(
                      visible.titleShowManufacturer,
                      'titleShowManufacturer'
                    );
                  }}
                  switchedOffText="NO"
                  switchedOnText="YES"
                  disabled={!selectedColumns.length}
                  elementAttr={titleManAttr}
                />
              </div>
            </div>
          )}
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Right align company details
            </div>
            <div className="dx-field-label quote-settings-value">
              <Switch
                value={visible.compInfoAlignRight}
                onValueChanged={() => {
                  onSwitchChange(
                    visible.compInfoAlignRight,
                    'compInfoAlignRight'
                  );
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                disabled={!selectedColumns.length}
                elementAttr={alignCompAttr}
              />
            </div>
          </div>
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Hide Line
            </div>
            <div className="dx-field-label quote-settings-value">
              <Switch
                value={visible.hideLine}
                onValueChanged={() => {
                  onSwitchChange(visible.hideLine, 'hideLine');
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                disabled={!selectedColumns.length}
                elementAttr={hideLineAttr}
              />
            </div>
          </div>
          <div className="dx-field">
            <div className="dx-field-label quote-note-visibility-label white-space-normal">
              Display footer on every page
            </div>
            <div className="dx-field-label quote-settings-value">
              <Switch
                value={visible.displayFooter}
                onValueChanged={() => {
                  onSwitchChange(visible.displayFooter, 'displayFooter');
                }}
                switchedOffText="NO"
                switchedOnText="YES"
                disabled={!selectedColumns.length}
                elementAttr={displayFooterAttr}
              />
            </div>
          </div>
        </div>
      </div>
      <style jsx>{`
        .quote-setting-content[disabled] {
          pointer-events: none;
          opacity: 0.5;
          background: #ccc;
        }
        .sub-head {
          background: #c8d6f9;
          height: 40px;
          font-size: 18px;
          text-align: center;
          line-height: 40px;
          margin: 0;
        }

        .note-section {
          margin: 10px 0;
          padding: 10px 10px;
          font-size: 13px;
          background: #fff;
          font-style: italic;
        }
        .w-75 {
          width: 75%;
        }
        .w-25 {
          width: 25% !important;
        }
      `}</style>
    </aside>
  );
};

export default SettingsTab;
