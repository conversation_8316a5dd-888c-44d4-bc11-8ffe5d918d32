import Popup from 'devextreme-react/popup';

const QuoteDialog = ({
  header,
  body,
  footer,
  visible,
  onHide,
  dimension,
  onShown,
}) => {
  const renderDialog = () => {
    return (
      <>
        <div>{body}</div>
        <div className="p-dialog-footer popup text-right">{footer}</div>
      </>
    );
  };
  return (
    <Popup
      width={dimension.width}
      height={dimension.height}
      showTitle={true}
      title={header}
      dragEnabled={false}
      hideOnOutsideClick={true}
      visible={visible}
      onHiding={onHide}
      shadingColor="#00000090"
      contentRender={renderDialog}
      onShown={onShown}
    />
  );
};

QuoteDialog.defaultProps = {
  header: 'Default Popup',
  body: <></>,
  footer: <></>,
  dimension: { width: '500', height: '200' },
  isExpandCollapse: false,
};
export default QuoteDialog;
