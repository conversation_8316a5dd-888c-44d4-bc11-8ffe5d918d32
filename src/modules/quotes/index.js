import { useState, useRef, useEffect, useLayoutEffect } from 'react';
import { Toast } from 'primereact/toast';
import DataTable from '@ui/datatable/data-table.js';
import { Button, TextBox } from 'devextreme-react';
import { quoteData } from './data.js';
import SettingsTab from './settings-tab.js';
import PreviewPdf from '@components/preview-pdf/index.js';
import DeleteRecordDialogTemplate from '../expenses/components/expenses/dialogs/deleteRecordDialog';
import QuoteDialogTemplate from './quote-dialog.js';
import parse from 'html-react-parser';
import nProgress from 'nprogress';
import {
  getQuoteLabels,
  postQuotePDF,
  getPdfTemplate,
  postPdfConfig,
  deletePdfConfig,
  updatePdfConfig,
  getPdfConfig,
  uploadQuoteLogo,
} from '@services/api/index.js';
import Modal from '@components/modal/index.js';
import { basePath } from 'next.config';
import { LOGO } from 'src/constants/index.js';
const CRM_HEADER_LOGO =
  process.env.NODE_ENV == 'development' ? `${basePath}/logo.png` : LOGO;
import {
  convertNumToCurrency,
  getCustomValBasedOnLabel,
} from 'src/utils/index.js';
import SOReportTemplate from '@components/quote-report-template/so-report-template';
import QuoteReportTemplate from '@components/quote-report-template/quote-report-template';
import MedicalReportTemplate from '@components/quote-report-template/medical-report-template';
import DefaultTemplateDialog from './defaultTemplateDialog.js';

import { useDefaults } from '@contexts/defaults.js';

const VALID_FILE_TYPES = ['.jpg', '.jpeg', '.bmp', '.png', '.gif'];
const saveConfirmBtnAttr = { id: 'save-confirm-btn' };
const deleteConfirmBtnAttr = { id: 'delete-confirm-btn' };
const cancelBtnAttr = { id: 'cancel-btn' };

const groupData = {
  Intp10092019: [
    {
      ID: 1,
      QUOT_ITEM_PART_MANF: 'ENG 10 09 2019',
      QUOT_ITEM_PART_CUST: '7657849',
      QUOT_ITEM_PART_DESC: 'Axis Fixation Beam, 7.5 x 155 mm',
      GRID_DESC: 'Axis Fixation Beam, 7.5 x 155 mm',
      QUOT_ITEM_QNTY: '10',
      QUOT_UOM: 'EA',
      QUOT_RESALE: '26999.999000',
      QUOT_EXT_PRICE: '269999.99',
      QUOT_NOTES:
        'Et reiciendis similique sed quam aliquam et deleniti unde hic autem consequatur eos libero dignissimos. Ut sint animi aut voluptatem quia 33 natus quia.',
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '9.34',
      QUOT_PRINCIPAL: 'Intp10092019',
      QUOT_LOCATION: 'wl-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=1" alt="logo" />
      ),
    },
    {
      ID: 2,
      QUOT_ITEM_PART_MANF: '130-75155',
      QUOT_ITEM_PART_CUST: '258369147',
      QUOT_ITEM_PART_DESC: '7 SS fan blade M8 threading',
      GRID_DESC: '7 SS fan blade M8 threading',
      QUOT_ITEM_QNTY: '5',
      QUOT_UOM: '',
      QUOT_RESALE: '125000.999000',
      QUOT_EXT_PRICE: '135000.00',
      QUOT_NOTES:
        'Sed quasi velit aut quia tenetur est quia modi est optio repudiandae sed ipsum exercitationem. Cum eveniet dolor qui autem corrupti est voluptatem quae a veniam aperiam ut velit deleniti.',
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '12.90',
      QUOT_PRINCIPAL: 'Intp10092019',
      QUOT_LOCATION: 'wc-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=2" alt="logo" />
      ),
    },
    {
      ID: 3,
      QUOT_ITEM_PART_MANF: '893-1388',
      QUOT_ITEM_PART_CUST: '1010',
      QUOT_ITEM_PART_DESC: 'Healing salve 4 x 4',
      GRID_DESC: 'Healing salve 4 x 4',
      QUOT_ITEM_QNTY: '10',
      QUOT_UOM: '',
      QUOT_RESALE: '26999.999000',
      QUOT_EXT_PRICE: '269999.99',
      QUOT_NOTES:
        "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.",
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '12.00',
      QUOT_PRINCIPAL: 'Intp10092019',
      QUOT_LOCATION: 'smk-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=3" alt="logo" />
      ),
    },
    {
      ID: 4,
      QUOT_ITEM_PART_MANF: 'HT5F9AS',
      QUOT_ITEM_PART_CUST: '',
      QUOT_ITEM_PART_DESC: 'lenevo laptops description',
      GRID_DESC: 'lenevo laptops description',
      QUOT_ITEM_QNTY: '29',
      QUOT_UOM: '',
      QUOT_RESALE: '25.000000',
      QUOT_EXT_PRICE: '725.00',
      QUOT_NOTES: '',
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '17.00',
      QUOT_PRINCIPAL: 'Intp10092019',
      QUOT_LOCATION: 'tap-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=4" alt="logo" />
      ),
    },
    {
      ID: 5,
      QUOT_ITEM_PART_MANF: 'ENG 10 09 2019',
      QUOT_ITEM_PART_CUST: '',
      QUOT_ITEM_PART_DESC: '  lenevo laptops description',
      GRID_DESC: '  lenevo laptops description',
      QUOT_ITEM_QNTY: '12',
      QUOT_UOM: '',
      QUOT_RESALE: '26999.999000',
      QUOT_EXT_PRICE: '323999.99',
      QUOT_NOTES: '',
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '18.00',
      QUOT_PRINCIPAL: 'Intp10092019',
      QUOT_LOCATION: 'sink-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=5" alt="logo" />
      ),
    },
  ],
  P13092019: [
    {
      ID: 6,
      QUOT_ITEM_PART_MANF: 'ABC-30938',
      QUOT_ITEM_PART_CUST: '12579',
      QUOT_ITEM_PART_DESC: 'Grip threading, 4x4',
      GRID_DESC: 'Grip threading, 4x4',
      QUOT_ITEM_QNTY: '8',
      QUOT_UOM: 'EA',
      QUOT_RESALE: '26999.999000',
      QUOT_EXT_PRICE: '27999.99',
      QUOT_NOTES: '',
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '0.00',
      QUOT_PRINCIPAL: 'P13092019',
      QUOT_LOCATION: 'shower-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=6" alt="logo" />
      ),
    },
  ],
};

const linkGroupData = {
  Intp10092019: [
    {
      ID: 1,
      QUOT_ITEM_PART_MANF: 'ENG 10 09 2019',
      QUOT_ITEM_PART_CUST: '7657849',
      QUOT_ITEM_PART_DESC: 'Axis Fixation Beam, 7.5 x 155 mm',
      GRID_DESC: 'Axis Fixation Beam, 7.5 x 155 mm',
      QUOT_ITEM_QNTY: '10',
      QUOT_UOM: 'EA',
      QUOT_RESALE: '26999.999000',
      QUOT_EXT_PRICE: '269999.99',
      QUOT_NOTES:
        'Et reiciendis similique sed quam aliquam et deleniti unde hic autem consequatur eos libero dignissimos. Ut sint animi aut voluptatem quia 33 natus quia.',
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '9.34',
      QUOT_PRINCIPAL: 'Intp10092019',
      QUOT_LOCATION: 'wl-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=1" alt="logo" />
      ),
    },
    {
      ID: 2,
      QUOT_ITEM_PART_MANF: '130-75155',
      QUOT_ITEM_PART_CUST: '258369147',
      QUOT_ITEM_PART_DESC: '7 SS fan blade M8 threading',
      GRID_DESC: '7 SS fan blade M8 threading',
      QUOT_ITEM_QNTY: '5',
      QUOT_UOM: '',
      QUOT_RESALE: '125000.999000',
      QUOT_EXT_PRICE: '135000.00',
      QUOT_NOTES:
        'Sed quasi velit aut quia tenetur est quia modi est optio repudiandae sed ipsum exercitationem. Cum eveniet dolor qui autem corrupti est voluptatem quae a veniam aperiam ut velit deleniti.',
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '12.90',
      QUOT_PRINCIPAL: 'Intp10092019',
      QUOT_LOCATION: 'wc-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=2" alt="logo" />
      ),
    },
    {
      ID: 3,
      QUOT_ITEM_PART_MANF: '893-1388',
      QUOT_ITEM_PART_CUST: '1010',
      QUOT_ITEM_PART_DESC: 'Healing salve 4 x 4',
      GRID_DESC: 'Healing salve 4 x 4',
      QUOT_ITEM_QNTY: '10',
      QUOT_UOM: '',
      QUOT_RESALE: '26999.999000',
      QUOT_EXT_PRICE: '269999.99',
      QUOT_NOTES:
        "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.",
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '12.00',
      QUOT_PRINCIPAL: 'Intp10092019',
      QUOT_LOCATION: 'smk-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=3" alt="logo" />
      ),
    },
    {
      ID: 4,
      QUOT_ITEM_PART_MANF: 'HT5F9AS',
      QUOT_ITEM_PART_CUST: '',
      QUOT_ITEM_PART_DESC: 'lenevo laptops description',
      GRID_DESC: 'lenevo laptops description',
      QUOT_ITEM_QNTY: '29',
      QUOT_UOM: '',
      QUOT_RESALE: '25.000000',
      QUOT_EXT_PRICE: '725.00',
      QUOT_NOTES: '',
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '17.00',
      QUOT_PRINCIPAL: 'Intp10092019',
      QUOT_LOCATION: 'tap-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=4" alt="logo" />
      ),
    },
    {
      ID: 5,
      QUOT_ITEM_PART_MANF: 'ENG 10 09 2019',
      QUOT_ITEM_PART_CUST: '',
      QUOT_ITEM_PART_DESC: '  lenevo laptops description',
      GRID_DESC: '  lenevo laptops description',
      QUOT_ITEM_QNTY: '12',
      QUOT_UOM: '',
      QUOT_RESALE: '26999.999000',
      QUOT_EXT_PRICE: '323999.99',
      QUOT_NOTES: '',
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '18.00',
      QUOT_PRINCIPAL: 'Intp10092019',
      QUOT_LOCATION: 'sink-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=5" alt="logo" />
      ),
    },
  ],
  P13092019: [
    {
      ID: 6,
      QUOT_ITEM_PART_MANF: 'ABC-30938',
      QUOT_ITEM_PART_CUST: '12579',
      QUOT_ITEM_PART_DESC: 'Grip threading, 4x4',
      GRID_DESC: 'Grip threading, 4x4',
      QUOT_ITEM_QNTY: '8',
      QUOT_UOM: 'EA',
      QUOT_RESALE: '26999.999000',
      QUOT_EXT_PRICE: '27999.99',
      QUOT_NOTES: '',
      QUOT_LEAD_TIME: '100',
      QUOT_WEIGHT: '0.00',
      QUOT_PRINCIPAL: 'P13092019',
      QUOT_LOCATION: 'shower-1',
      QUOT_IMG_URL: (
        <img src="https://picsum.photos/70/40?random=6" alt="logo" />
      ),
    },
  ],
};

const newArray = [];
const linkedArray = [];

Object.keys(groupData).forEach((item) => {
  groupData[item].forEach((data, index) => {
    for (const key of Object.keys(data)) {
      if (key == 'QUOT_PRINCIPAL' && index == 0) {
        data.QUOT_ITEM_PART_MANF = parse(
          `<strong>${item}</strong>` +
            `<br/><br/>${groupData[item][0].QUOT_ITEM_PART_MANF}`
        );
      }
    }
  });
  for (const data of groupData[item]) {
    newArray.push(data);
  }
});

Object.keys(linkGroupData).forEach((item) => {
  linkGroupData[item].forEach((data, index) => {
    for (const key of Object.keys(data)) {
      if (key === 'QUOT_PRINCIPAL') {
        if (index === 0)
          data.QUOT_ITEM_PART_MANF = parse(
            `<strong>${item}</strong>` +
              `<br/><br/><a href="/" target="_blank">
            ${data.QUOT_ITEM_PART_MANF}
          </a>`
          );
        else
          data.QUOT_ITEM_PART_MANF = parse(
            `<a href="/" target="_blank">
            ${data.QUOT_ITEM_PART_MANF}
          </a>`
          );
      }
    }
  });
  for (const data of linkGroupData[item]) {
    linkedArray.push(data);
  }
});

const columnAlign = (data) => {
  if (data == 'QUOT_RESALE' || data == 'QUOT_EXT_PRICE') {
    return 'text-right';
  } else if (data == 'QUOT_ITEM_QNTY' || data == 'QUOT_UOM') {
    return 'text-center';
  } else {
    return '';
  }
};
const formatter = new Intl.NumberFormat('en-US', {
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
});

const Quotes = ({ templateId }) => {
  const quoteGridRef = useRef(null);
  const toast = useRef(null);
  const gridRef = useRef(null);
  const addressBox = useRef(null);
  const textBox = useRef(null);

  const [columns, setColumns] = useState({
    allColumns: [],
    selColumns: [],
  });

  const [totalWidth, setTotalWidth] = useState(0);
  const [pdfURL, setPdfURL] = useState();
  const [templateList, setTemplateList] = useState();
  const [quoteLabels, setQuoteLabels] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState();
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [quoteDialog, setQuoteDialog] = useState(false);
  const [defaultTemplate, setDefaultTemplate] = useState(false);
  const [onBtnClick, setOnBtnClick] = useState(false);
  const [isValidate, setIsValidate] = useState(false);
  const [previewPDF, setPreviewPDF] = useState(false);
  const [dataSource, setDataSource] = useState(quoteData[0].quoteDetails);

  const [visible, setVisible] = useState({
    topicVisible: true,
    partLinkToSpec: false,
    desc: false,
    descWrap: true,
    noteVisible: false,
    noteFooterVisible: false,
    manufacturerFooterNoteVisible: false,
    templateName: '',
    logo: '',
    previewURL: CRM_HEADER_LOGO,
    customAddress: false,
    companyName: '',
    companyAddress: '',
    superQuote: false,
    defaultTemplate: false,
    templateType: 'quote',
    includeName: false,
    includeSignature: false,
    groupByMan: false,
    showSecCustomer: false,
    compInfoAlignRight: false,
    titleShowManufacturer: false,
    showPreparedBy: false,
    showSalesTeam: false,
    showTotalWeight: false,
    hideLine: false,
    displayFooter: false,
    totalLabelForCustomer: '',
    totalLabelForDistributor: '',
    unitPriceDecimalDigit: 2,
  });

  const concatLabel = {
    labelVisible: false,
    primaryLabel: 'QUOT_UOM',
    secondaryLabel: 'QUOT_RESALE',
  };

  const handleErrors = (error) => {
    toast.current.show({
      severity: error.severity,
      summary: error.summary,
      detail: error.detail,
      life: 3000,
    });
  };

  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();

  const [customPrinci, customUnitPrice] = getCustomValBasedOnLabel(
    CUSTOM_LABELS,
    ['IDS_PRINCI', 'IDS_UNIT_PRICE']
  );

  const dataSourceWithPartLink = () => {
    if (visible.partLinkToSpec) {
      if (visible.groupByMan) {
        setDataSource(linkedArray);
      } else {
        setDataSource(
          quoteData[0].quoteDetails.map((item) => ({
            ...item,
            QUOT_ITEM_PART_MANF: (
              <a href="/" target="_blank">
                {item.QUOT_ITEM_PART_MANF}
              </a>
            ),
          }))
        );
      }
    } else {
      if (visible.groupByMan) {
        setDataSource(newArray);
      } else {
        setDataSource(quoteData[0].quoteDetails);
      }
    }
  };

  /**
   * Dynamic Route: get template based on template ID
   * and initialize template
   */
  useEffect(() => {
    const selectTemplateByQuery = async () => {
      if (templateId) {
        const pdfTemplate = await (await getPdfTemplate()).data;
        const teamplate = pdfTemplate.find((b) => b.templateId == templateId);
        if (teamplate) {
          setSelectedTemplate(teamplate.templateId);
        }
      }
    };
    selectTemplateByQuery();
  }, [templateId]);

  useEffect(() => {
    if (!visible.logo || !visible.previewURL) {
      setVisible((prev) => ({
        ...prev,
        previewURL: CRM_HEADER_LOGO,
      }));
    } else {
      setVisible((prev) => ({
        ...prev,
        previewURL: URL?.createObjectURL(visible.logo) || CRM_HEADER_LOGO,
      }));

      return () => URL?.revokeObjectURL(URL.createObjectURL(visible.logo));
    }
  }, [visible.logo, selectedTemplate]);

  useEffect(() => {
    const fetchLabels = async () => {
      try {
        const params = {
          isSuperQuote: visible.superQuote,
          groupByManufactureNo: false,
        };
        const labelData = await (
          await getQuoteLabels(params)
        ).data.filter((list) => {
          return (
            list.labelId !== 'QUOT_NOTES' &&
            list.labelId !== 'QUOT_ITEM_PART_DESC'
          );
        });
        setQuoteLabels(labelData);
        setColumns({
          ...columns,
          allColumns: labelData,
          selColumns: [],
        });
        if (selectedTemplate) {
          const pdfConfig = await (await getPdfConfig(selectedTemplate)).data;
          const selectedColumn = pdfConfig.columns
            ?.filter((item) =>
              labelData.find((subItem) => item.field == subItem.labelId)
            )
            .map((col) => {
              return {
                id: col.id,
                labelId: col.field,
                name: col.fieldName,
                dataField: col.fieldName,
                width: col.width,
              };
            });
          const otherColumn = labelData
            .filter((item) =>
              pdfConfig.columns.every((otherItem) => {
                return item.labelId !== otherItem.field;
              })
            )
            .sort((a, b) => a.id - b.id);

          setColumns({
            ...columns,
            selColumns: selectedColumn,
            allColumns: otherColumn,
          });

          const getTemplateType = (data) => {
            if (data.salesOrder) return 'salesOrder';
            else if (data.medical) return 'medical';
            else return 'quote';
          };

          setVisible((prev) => ({
            ...prev,
            topicVisible: pdfConfig.template.topic == 1,
            partLinkToSpec: pdfConfig.template.partLinkToSpec == 1,
            desc: pdfConfig.template.description == 1,
            descWrap: pdfConfig.template.descWrap == 1,
            noteVisible: pdfConfig.template.lineItem == 1,
            noteFooterVisible: pdfConfig.template.footerNote == 1,
            manufacturerFooterNoteVisible:
              pdfConfig.template.manufacturerFooterNote == 1,
            templateName: pdfConfig.template.templateName,
            previewURL: pdfConfig.template.logo || CRM_HEADER_LOGO,
            customAddress: false,
            companyName: pdfConfig.template.companyName ?? '',
            companyAddress: pdfConfig.template.companyAddress ?? '',
            superQuote: pdfConfig.template.superQuote == 1,
            defaultTemplate: pdfConfig.template.defaultFlag == 1,
            templateType: getTemplateType(pdfConfig.template),
            includeName:
              (pdfConfig.template.salesOrder || pdfConfig.template.medical) &&
              pdfConfig.template.includeName == 1,
            includeSignature:
              (pdfConfig.template.salesOrder || pdfConfig.template.medical) &&
              pdfConfig.template.includeSignature == 1,
            groupByMan: pdfConfig.template.groupByManufacture == 1,
            showSecCustomer: pdfConfig.template.showSecondaryCustomer == 1,
            compInfoAlignRight: pdfConfig.template.compInfoAlignRight == 1,
            titleShowManufacturer:
              pdfConfig.template.titleShowManufacturer == 1,
            showPreparedBy: pdfConfig.template.showPreparedBy == 1,
            showSalesTeam: pdfConfig.template.showSalesTeam == 1,
            showTotalWeight: pdfConfig.template.showTotalWeight == 1,
            hideLine: pdfConfig.template.hideLine == 1,
            displayFooter: pdfConfig.template.displayFooter == 1,
            totalLabelForCustomer:
              pdfConfig.template.totalLabelForCustomer ?? '',
            totalLabelForDistributor:
              pdfConfig.template.totalLabelForDistributor ?? '',
            unitPriceDecimalDigit:
              pdfConfig.template.unitPriceDecimalDigit ?? 2,
          }));
          setOnBtnClick(false);
        }
      } catch (error) {
        const message = [];
        message.severity = 'error';
        message.summary = 'PDF preview failed';
        message.detail = 'Please try after sometime';
        handleErrors(message);
      }
      if (visible.superQuote && visible.groupByMan) {
        setDataSource(newArray);
        setColumns({
          ...columns,
          allColumns: columns.allColumns.filter((item) => {
            return item.labelId != 'QUOT_PRINCIPAL';
          }),
          // selColumns: columns.selColumns.filter((item) => {
          //   return item.labelId != 'QUOT_PRINCIPAL';
          // }),
        });
      } else {
        setDataSource(quoteData[0].quoteDetails);
      }
      dataSourceWithPartLink();
    };
    fetchLabels();
  }, [visible.superQuote, selectedTemplate]);

  useEffect(() => {
    const fetchTemplates = async () => {
      const pdfTemplate = await (await getPdfTemplate())?.data;
      setTemplateList(pdfTemplate);
    };
    fetchTemplates();
  }, [onBtnClick]);

  /**
   * Used to remove width when
   * superquote is enabled and group By manuf
   * is changed
   */
  useEffect(() => {
    if (visible.superQuote) {
      setColumns((prev) => ({
        ...prev,
        selColumns: prev.selColumns.map(({ width, ...object }) => object),
      }));
    }
  }, [visible.groupByMan]);

  useEffect(() => {
    dataSourceWithPartLink();
  }, [selectedTemplate, visible]);

  const onTemplateSelChanged = (e) => {
    setIsValidate(false);
    setSelectedTemplate(e.value || '');
    setVisible((prev) => ({
      ...prev,
      logo: '',
      previewURL: CRM_HEADER_LOGO,
    }));
  };

  const onSelectFile = (e) => {
    // Task #10021: Quick Fix - Create valid file formats
    const VALID_FILES = VALID_FILE_TYPES.map((type) => {
      if (type == '.svg') {
        return `image/${type.replace('.', '')}+xml`;
      } else if (type == '.tif') {
        return 'image/tiff';
      }
      return `image/${type.replace('.', '')}`;
    });

    if (!e.target.files || e.target.files.length === 0) {
      setVisible((prev) => ({
        ...prev,
        logo: prev.logo || undefined,
      }));
      return;
    }

    // Task #10021: Quick Fix - Validate File type before Upload
    else if (!VALID_FILES.includes(e.target.files[0].type)) {
      toast.current.show({
        severity: 'error',
        summary: 'Invalid Image Format',
        detail: 'Invalid file type, only JPEG,JPG,BMP,GIF and PNG are allowed!',
        life: 3000,
      });
      return;
    }
    setVisible((prev) => ({
      ...prev,
      logo: e.target.files[0],
    }));
    // CRM-6357 : Reset Image Picker after Save Changes for Same Image load issue fix
    e.target.value = '';
  };

  function onDragStart(e) {
    e.itemData = columns[e.fromData][e.fromIndex];
  }

  const onAdd = (e) => {
    const tasks = columns[e.toData].map(({ width, ...object }) => object);
    setColumns((_taskList) => ({
      ..._taskList,
      [e.toData]: [
        ...tasks.slice(0, e.toIndex),
        e.itemData,
        ...tasks.slice(e.toIndex),
      ],
    }));
  };

  function onRemove(e) {
    const tasks = columns[e.fromData].map(({ width, ...object }) => object);
    setColumns((_taskList) => ({
      ..._taskList,
      [e.fromData]: [
        ...tasks.slice(0, e.fromIndex),
        ...tasks.slice(e.fromIndex + 1),
      ],
    }));
  }

  function onReorder(e) {
    const fromTask = columns[e.fromData].map(({ width, ...object }) => object);
    const afterRemove = {
      ...columns,
      [e.fromData]: [
        ...fromTask.slice(0, e.fromIndex),
        ...fromTask.slice(e.fromIndex + 1),
      ],
    };

    const toTask = afterRemove[e.toData].map(({ width, ...object }) => object);
    setColumns({
      ...afterRemove,
      [e.toData]: [
        ...toTask.slice(0, e.toIndex),
        e.itemData,
        ...toTask.slice(e.toIndex),
      ],
    });
  }

  function renderCellBasedOnDescSwitch(rowData, labelId) {
    const { data, key } = rowData;
    const { desc, descWrap, unitPriceDecimalDigit } = visible;
    const { labelVisible, primaryLabel, secondaryLabel } = concatLabel;
    if (labelId === 'SrNo') return <center>{key}</center>;

    // Description Below part Number and Description Wrap Enabled
    if (desc && labelId === 'QUOT_ITEM_PART_MANF' && descWrap) {
      const partManf = data[labelId];
      const partDesc = data.QUOT_ITEM_PART_DESC;
      return (
        <>
          <span>{partManf}</span>
          <br />
          <span>{partDesc}</span>
        </>
      );
    }
    if (labelVisible && labelId === primaryLabel) {
      const primaryValue = data[labelId];
      const secondaryValue = data[secondaryLabel];
      return (
        <>
          <span>{primaryValue}</span>
          <span>{secondaryValue}</span>
        </>
      );
    }

    const value = data[labelId];
    switch (labelId) {
      case 'QUOT_RESALE':
        return convertNumToCurrency(value, unitPriceDecimalDigit);
      case 'QUOT_EXT_PRICE':
        return convertNumToCurrency(value);
      case 'QUOT_WEIGHT':
        return formatter.format(value);
      default:
        return value;
    }
  }

  const columnOptions = columns.selColumns
    .flatMap((col) => {
      if (col.labelId == 'QUOT_PRINCIPAL') {
        return visible.groupByMan ? [] : col;
      } else {
        return col;
      }
    })
    .map((task) => {
      return {
        key: task.id,
        dataField: task.labelId,
        dataType: task.type,
        caption: task.name,
        allowSorting: false,
        visible: true,
        cssClass: columnAlign(task.labelId),
        width: task.width && (task.width * totalWidth) / 100,
      };
    });
  let columnOptionsWithSrNo = [...columnOptions];
  if (!visible.hideLine) {
    columnOptionsWithSrNo = [
      {
        key: 0,
        caption: 'Line',
        dataField: 'SrNo',
        width: '55px',
        allowResizing: false,
        allowSorting: false,
        dataType: 'boolean',
      },
      ...columnOptions,
    ];
  }

  const QuoteDataRow = (quoteRowInfo) => {
    const PartIndex = columns.selColumns.findIndex(
      (x) => x.labelId === 'QUOT_ITEM_PART_MANF'
    );
    return (
      <>
        <tr className="main-row">
          {columnOptionsWithSrNo.map((qC) =>
            visible.groupByMan && qC.dataField == 'QUOT_PRINCIPAL' ? (
              ''
            ) : (
              <td className={columnAlign(qC.dataField)} key={qC.key}>
                {renderCellBasedOnDescSwitch(quoteRowInfo, qC.dataField)}
              </td>
            )
          )}
        </tr>
        {visible.desc && !visible.descWrap && (
          <tr className="main-row">
            <td colSpan={PartIndex + 1} />
            <td colSpan={columns.selColumns.length}>
              {quoteRowInfo.data.QUOT_ITEM_PART_DESC}
            </td>
          </tr>
        )}
        {visible.noteVisible && (
          <tr className="main-row">
            <td />
            <td colSpan={columns.selColumns.length}>
              <em>Note: {quoteRowInfo.data.QUOT_NOTES}</em>
            </td>
          </tr>
        )}
      </>
    );
  };

  const onInputChange = (e, type) => {
    setIsValidate(false);
    setVisible((prev) => {
      if (type == 'companyName' && e.replace(/\s+/g, '').length == 0) {
        return {
          ...prev,
          companyAddress: '',
          companyName: e,
        };
      }
      return {
        ...prev,
        [type]: e,
      };
    });
    if (type == 'superQuote' && !e)
      setVisible((prev) => ({
        ...prev,
        groupByMan: e,
      }));
    setDataSource(quoteData[0].quoteDetails);
  };

  const onSwitchChange = (enable, type) => {
    setVisible((prev) => ({
      ...prev,
      [type]: !enable,
    }));

    if (type == 'groupByMan') {
      const principalObj = quoteLabels.find(
        (column) => column.labelId == 'QUOT_PRINCIPAL'
      );
      if (enable) {
        setColumns({
          ...columns,
          allColumns: !columns.allColumns.find(
            (col) => col.labelId == 'QUOT_PRINCIPAL'
          )
            ? [...columns.allColumns, principalObj]
            : columns.allColumns,
        });
      } else {
        setColumns({
          allColumns: columns.allColumns.filter((item) => {
            return item.labelId != 'QUOT_PRINCIPAL';
          }),
          selColumns: columns.selColumns.filter((item) => {
            return item.labelId != 'QUOT_PRINCIPAL';
          }),
        });
      }
    }
  };

  const onClickList = (e) => {
    const itemObj = {
      itemData: e.itemData,
      fromData: e.fromData,
      toData: e.toData,
      toIndex: e.toIndex,
      fromIndex: e.itemIndex,
    };
    onAdd(itemObj);
    onRemove(itemObj);
    onReorder(itemObj);
  };

  // Disable Description Wrap switch if there is no "QUOT_ITEM_PART_MANF" in selected columns
  const disableDescWrapSwitch = !columns.selColumns.some(
    (quote) => quote.labelId === 'QUOT_ITEM_PART_MANF'
  );

  const disableManufacturer = !columns.selColumns.some(
    (quote) => quote.labelId === 'QUOT_PRINCIPAL'
  );

  const allColumnList = columns.allColumns.filter((product) => {
    return (
      product.labelId !== 'QUOT_NOTES' &&
      product.labelId !== 'QUOT_ITEM_PART_DESC'
    );
  });

  const getPdfRequestParam = async (id) => {
    const param = { templateId: id, quoteNumber: '' };

    return postQuotePDF(param).then((response) => {
      if (response.status === 200) {
        return new Blob([response.data], { type: 'application/pdf' });
      }
    });
  };
  const onPreviewBtnClicked = async () => {
    nProgress.start();
    await getPdfRequestParam(selectedTemplate)
      .then((blob) => {
        setPreviewPDF(true);
        const fileURL = window.URL.createObjectURL(blob);
        setPdfURL(fileURL);
        nProgress.done();
      })
      .catch(() => {
        toast.current.show({
          severity: 'error',
          summary: 'PDF preview failed',
          detail: 'Please try after sometime',
          life: 3000,
        });
      });
  };

  useLayoutEffect(() => {
    setTotalWidth(gridRef.current.offsetWidth);
  }, []);

  const pdfApiRequest = () => {
    const percentageFactor = 100 / totalWidth;
    const param = {
      templateName: visible.templateName?.toString().trim(),
      topic: visible.topicVisible,
      footerNote: visible.noteFooterVisible,
      manufacturerFooterNote: visible.manufacturerFooterNoteVisible,
      lineItemNote: visible.noteVisible,
      partLinkToSpec: visible.partLinkToSpec,
      description: visible.desc,
      descWrap: visible.descWrap,
      companyName: visible.companyName.trim() || '',
      companyAddress: visible.companyAddress ?? '',
      saveAs: visible.saveAs,
      currentTemplateId: visible.currentTemplateId,
      superQuote: visible.superQuote,
      defaultFlag: visible.defaultTemplate,
      quote: visible.templateType == 'quote',
      salesOrder: visible.templateType == 'salesOrder',
      medical: visible.templateType == 'medical',
      includeName:
        (visible.templateType == 'salesOrder' ||
          visible.templateType == 'medical') &&
        visible.includeName,
      includeSignature:
        (visible.templateType == 'salesOrder' ||
          visible.templateType == 'medical') &&
        visible.includeSignature,
      groupByManufacture: visible.groupByMan,
      showSecondaryCustomer: visible.showSecCustomer,
      compInfoAlignRight: visible.compInfoAlignRight,
      titleShowManufacturer: visible.titleShowManufacturer,
      showPreparedBy: visible.showPreparedBy,
      showSalesTeam: visible.showSalesTeam,
      showTotalWeight: visible.showTotalWeight,
      hideLine: visible.hideLine,
      displayFooter: visible.displayFooter,
      data: [],
      totalLabelForCustomer: visible.totalLabelForCustomer,
      totalLabelForDistributor: visible.totalLabelForDistributor,
      unitPriceDecimalDigit: visible.unitPriceDecimalDigit,
    };
    const colCount = quoteGridRef.current?.instance.columnCount();

    const widthPerColumn = (i) => {
      const width = quoteGridRef.current.instance.columnOption(i, 'width')
        ? quoteGridRef.current.instance.columnOption(i, 'width')
        : quoteGridRef.current.instance.columnOption(i, 'visibleWidth');
      return (width * percentageFactor).toFixed(3);
    };
    const startIndex = !visible.hideLine ? 1 : 0;
    for (let i = startIndex; i < colCount; i++) {
      const { name, labelId } = columns.selColumns.find(
        (col) =>
          col.labelId ===
          quoteGridRef.current.instance.columnOption(i, 'dataField')
      );
      const visibleIndex = columns.selColumns.findIndex(
        (col) => col.labelId === labelId
      );
      param.data.push({
        field: labelId,
        fieldName: name,
        customLabel: 0,
        type: 0,
        width: widthPerColumn(i),
        order: visibleIndex + 1,
      });
    }
    if (
      visible.superQuote &&
      visible.groupByMan &&
      columns.selColumns.some((item) => item.labelId === 'QUOT_PRINCIPAL')
    ) {
      param.data.push({
        field: 'QUOT_PRINCIPAL',
        fieldName: 'Manufacturer',
        customLabel: 0,
        type: 0,
        width: 0,
        order:
          columns.selColumns.findIndex(
            (col) => col.labelId == 'QUOT_PRINCIPAL'
          ) + 1,
      });
    }
    return param;
  };

  const imageUpload = async (data, formData) => {
    try {
      const logoUpload = await uploadQuoteLogo(data, formData);
      if (logoUpload.status === 200) {
        const pdfConfig = logoUpload.data;
        setVisible((prev) => ({
          ...prev,
          previewURL: pdfConfig.template.logo || CRM_HEADER_LOGO,
        }));
      }
    } catch (error) {
      const message = [];
      message.severity = 'error';
      message.summary = 'PDF preview failed';
      message.detail = 'Please try after sometime';
      handleErrors(message);
    }
  };

  const onSaveBtnClicked = async () => {
    setIsValidate(false);
    setVisible((prev) => ({
      ...prev,
      saveAs: false,
      currentTemplateId: '',
    }));
    const param = pdfApiRequest();
    await postPdfConfig(param)
      .then(async (response) => {
        if (response && response.status === 200) {
          if (response.data.error) {
            const message = [];
            message.severity = 'error';
            message.summary = 'PDF Save failed';
            message.detail = response.data.message;
            handleErrors(message);
          } else {
            setSelectedTemplate(response.data);
            setOnBtnClick(true);
            let newLogo = null;

            if (!visible.logo) {
              await fetch(CRM_HEADER_LOGO, {
                method: 'GET',
                credentials: 'include',
              })
                .then((data) => data.blob())
                .then(async (data) => {
                  newLogo = data;
                });
            } else {
              newLogo = visible.logo;
            }

            const formData = new FormData();
            formData.append('logo', newLogo);

            imageUpload(response.data, formData);

            toast.current.show({
              severity: 'success',
              summary: 'Saved Successfully',
              detail: 'PDF Template Saved Successfully',
              life: 3000,
            });
          }
        }
      })
      .catch((er) => {
        const message = [];
        message.severity = 'error';
        message.summary = 'PDF Save failed';
        message.detail = 'Duplicate Template Name';
        handleErrors(message);
      });
  };
  const onUpdateBtnClicked = async () => {
    setIsValidate(false);
    setVisible((prev1) => ({
      ...prev1,
      saveAs: false,
      currentTemplateId: '',
    }));
    const param = pdfApiRequest();
    await updatePdfConfig(selectedTemplate, param)
      .then(async (response) => {
        if (response && response.status === 200) {
          if (response.data.error) {
            const message = [];
            message.severity = 'error';
            message.summary = 'PDF Save failed';
            message.detail = response.data.message;
            handleErrors(message);
          } else {
            setOnBtnClick(true);
            if (visible.logo) {
              const formData = new FormData();
              formData.append('logo', visible.logo);

              imageUpload(selectedTemplate, formData);
            }
            toast.current.show({
              severity: 'success',
              summary: 'Update Successful',
              detail: 'PDF Template Updated Successfully',
              life: 3000,
            });
          }
        }
      })
      .catch((err) => {
        const message = [];
        message.severity = 'error';
        message.summary = 'PDF Update failed';
        message.detail = 'Duplicate Template Name';
        handleErrors(message);
      });
  };

  const onTextFocus = () => {
    textBox.current?.instance.focus();
  };

  const quoteDialogHide = () => {
    setQuoteDialog(false);
  };

  const defaultTemplateShow = () => {
    setDefaultTemplate(true);
  };

  const defaultTemplateHide = () => {
    setDefaultTemplate(false);
  };

  const confirmSave = () => {
    setQuoteDialog(true);
  };

  const onSaveAsBtnClicked = async () => {
    setIsValidate(false);
    setVisible((prev) => ({
      ...prev,
      saveAs: true,
      currentTemplateId: selectedTemplate,
    }));
    const param = pdfApiRequest();
    await postPdfConfig(param)
      .then(async (res) => {
        if (res && res.status === 200) {
          if (res.data.error) {
            const message = [];
            message.severity = 'error';
            message.summary = 'PDF Save failed';
            message.detail = res.data.message;
            handleErrors(message);
            onTextFocus();
          } else {
            setSelectedTemplate(res.data);
            setOnBtnClick(true);

            let newLogo = null;
            if (!visible.logo) {
              await fetch(CRM_HEADER_LOGO, {
                method: 'GET',
                credentials: 'include',
              })
                .then((data) => data.blob())
                .then(async (data) => {
                  newLogo = data;
                });
            } else {
              newLogo = visible.logo;
            }
            const formData = new FormData();
            formData.append('logo', newLogo);
            imageUpload(res.data, formData);

            quoteDialogHide();
            toast.current.show({
              severity: 'success',
              summary: 'Saved Successfully',
              detail: 'PDF Template Saved Successfully',
              life: 3000,
            });
          }
        }
      })
      .catch((error1) => {
        const message = [];
        message.severity = 'error';
        message.summary = 'PDF Save failed';
        message.detail = 'Server Error';
        handleErrors(message);
      });
  };

  const quoteDialogBody = (
    <>
      <div className="formgrid grid m-0">
        {selectedTemplate && (
          <div className="flex align-items-center flex-wrap m-4">
            <span className="m-3 dx-font-xs ">New Template Name</span>
            <TextBox
              value={visible.templateName}
              onValueChange={(e) => {
                onInputChange(e, 'templateName');
              }}
              onKeyUp={(e) => {
                setIsValidate(false);
              }}
              ref={textBox}
              focusStateEnabled
              placeholder="Template Name"
              className={isValidate ? 'border-red-500' : ''}
            />

            {isValidate && (
              <div className="dx-item dx-validationsummary-item mt-1 ml-10">
                <div className="dx-item-content dx-validationsummary-item-content">
                  Please enter Template Name
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );

  const validationClicked = () => {
    setIsValidate(true);
    textBox.current?.instance.option('value', '');
    textBox.current?.instance.focus();
  };

  const quoteDialogFooter = (
    <>
      <Button
        icon="pi pi-check"
        type="default"
        text="Save"
        className="bg-primary mr-2"
        onClick={
          visible.templateName?.toString().trim() == ''
            ? validationClicked
            : onSaveAsBtnClicked
        }
        elementAttr={saveConfirmBtnAttr}
      />
      <Button
        icon="pi pi-times"
        type="danger"
        text="Cancel"
        className="mr-2"
        onClick={quoteDialogHide}
        elementAttr={cancelBtnAttr}
      />
    </>
  );

  const onDeleteDialogHide = () => {
    setDeleteDialog(false);
  };

  const onDeleteBtnClicked = async () => {
    return deletePdfConfig(selectedTemplate)
      .then((response) => {
        if (response.status == 200) {
          onDeleteDialogHide();
          toast.current.show({
            severity: 'success',
            summary: 'Delete Successful',
            detail: 'PDF Template Deleted Successfully',
            life: 3000,
          });
          setSelectedTemplate();
          setVisible((prev) => ({
            ...prev,
            templateName: '',
            logo: '',
          }));

          setOnBtnClick(true);
          setColumns({
            ...columns,
            allColumns: quoteLabels,
            selColumns: [],
          });
        }
      })
      .catch((error) => {
        const message = [];
        message.severity = 'error';
        message.summary = 'PDF Delete failed';
        message.detail = 'Please try after sometimes';
        handleErrors(message);
      });
  };

  const confirmDelete = () => {
    setDeleteDialog(true);
  };

  const deleteFooterDialogTemplate = (
    <>
      <Button
        icon="pi pi-check"
        type="danger"
        text="Delete"
        className="bg-error mr-2"
        onClick={onDeleteBtnClicked}
        elementAttr={deleteConfirmBtnAttr}
      />
      <Button
        icon="pi pi-times"
        type="default"
        text="Cancel"
        className="bg-yellow-600 mr-2"
        onClick={onDeleteDialogHide}
        elementAttr={cancelBtnAttr}
      />
    </>
  );

  return (
    <div className="quote-container">
      <Toast ref={toast} />
      <SettingsTab
        allColumnList={allColumnList}
        quoteLabels={quoteLabels}
        onPreviewBtnClicked={onPreviewBtnClicked}
        onSaveBtnClicked={onSaveBtnClicked}
        onSaveAsBtnClicked={confirmSave}
        onUpdateBtnClicked={onUpdateBtnClicked}
        onDeleteBtnClicked={confirmDelete}
        onDragStart={onDragStart}
        onAdd={onAdd}
        onRemove={onRemove}
        onReorder={onReorder}
        selectedColumns={columns.selColumns}
        disableDescWrapSwitch={disableDescWrapSwitch}
        disableManufacturer={disableManufacturer}
        pdfTemplate={templateList}
        visible={visible}
        setVisible={setVisible}
        onInputChange={onInputChange}
        isValidate={isValidate}
        setIsValidate={setIsValidate}
        validationClicked={validationClicked}
        onTemplateSelChanged={onTemplateSelChanged}
        selectedTemplate={selectedTemplate}
        setSelectedTemplate={setSelectedTemplate}
        columns={columns}
        setColumns={setColumns}
        onSwitchChange={onSwitchChange}
        onClickList={onClickList}
        previewLogo={visible.previewURL}
        defaultTemplateShow={defaultTemplateShow}
        customPrinci={customPrinci}
        customUnitPrice={customUnitPrice}
      />
      <section className="main pb-7">
        <p className="quote-header mb-3">Sample Preview</p>
        <div className="preview">
          <div className="preview-padding">
            {visible.templateType == 'quote' && (
              <QuoteReportTemplate
                showSecCustomer={visible.showSecCustomer}
                compInfoAlignRight={visible.compInfoAlignRight}
                titleShowManufacturer={visible.titleShowManufacturer}
                isTopicVisible={visible.topicVisible}
                previewLogo={visible.previewURL}
                onSelectFile={onSelectFile}
                isCustomAddress={visible.customAddress}
                visible={visible}
                onInputChange={onInputChange}
                addressBox={addressBox}
                validFileTypes={VALID_FILE_TYPES}
              />
            )}
            {visible.templateType == 'salesOrder' && (
              <SOReportTemplate
                showSecCustomer={visible.showSecCustomer}
                compInfoAlignRight={visible.compInfoAlignRight}
                titleShowManufacturer={visible.titleShowManufacturer}
                isTopicVisible={visible.topicVisible}
                previewLogo={visible.previewURL}
                onSelectFile={onSelectFile}
                isCustomAddress={visible.customAddress}
                visible={visible}
                onInputChange={onInputChange}
                addressBox={addressBox}
                validFileTypes={VALID_FILE_TYPES}
              />
            )}
            {visible.templateType == 'medical' && (
              <MedicalReportTemplate
                showSecCustomer={visible.showSecCustomer}
                compInfoAlignRight={visible.compInfoAlignRight}
                titleShowManufacturer={visible.titleShowManufacturer}
                isTopicVisible={visible.topicVisible}
                previewLogo={visible.previewURL}
                onSelectFile={onSelectFile}
                isCustomAddress={visible.customAddress}
                visible={visible}
                onInputChange={onInputChange}
                addressBox={addressBox}
                validFileTypes={VALID_FILE_TYPES}
              />
            )}
            <div className="quote-grid-container w-full" ref={gridRef}>
              {columns.selColumns.length == 0 && (
                <span className="dx-datagrid-nodata mt-8">
                  Please Choose columns from picklist
                </span>
              )}

              {columnOptionsWithSrNo.length >= 1 &&
                columnOptionsWithSrNo.some((e) => e.caption !== 'Line') && (
                  <>
                    <DataTable
                      allowColumnReordering={false}
                      allowColumnResizing={true}
                      allowScrolling={false}
                      columns={columnOptionsWithSrNo}
                      columnAutoWidth={true}
                      dataSource={dataSource}
                      dataRowRender={QuoteDataRow}
                      keyExpr="ID"
                      noDataText="Please Choose the columns from picklist"
                      showBorders={true}
                      innerRef={quoteGridRef}
                      className="w-full"
                    />

                    <hr
                      className="my-3"
                      style={{ backgroundColor: '#b4c5de' }}
                    />
                    <p style={{ textAlign: 'right' }}>
                      <div>
                        <strong>
                          Grand Total:
                          {convertNumToCurrency(quoteData[0].GRAND_TOTAL)}
                        </strong>
                      </div>
                      <div className={!visible.showTotalWeight ? 'hidden' : ''}>
                        <strong>
                          Total Weight:
                          {quoteData[0].TOTAL_WEIGHT}
                        </strong>
                      </div>
                    </p>
                    <hr
                      className="my-3"
                      style={{ backgroundColor: '#b4c5de' }}
                    />
                    {visible.noteFooterVisible && !visible.displayFooter && (
                      <div className="footer-comment">
                        <p className="mb-3">
                          Note: {quoteData[0].QUOT_COMMENTS}
                        </p>
                      </div>
                    )}
                    {visible.manufacturerFooterNoteVisible && (
                      <div className="footer-comment">
                        <p className="mb-3">{quoteData[0].QUOTE_TERMS}</p>
                      </div>
                    )}
                  </>
                )}
            </div>
          </div>

          {visible.noteFooterVisible && visible.displayFooter && (
            <>
              <hr className="my-2" style={{ backgroundColor: '#b4c5de' }} />
              <div className="footer-comment">
                <p className="mx-6 mb-2">Note: {quoteData[0].QUOT_COMMENTS}</p>
              </div>
            </>
          )}
        </div>
      </section>
      {previewPDF && (
        <Modal setOpen={setPreviewPDF} title="Preview PDF">
          <PreviewPdf src={pdfURL} />
        </Modal>
      )}
      {deleteDialog && (
        <DeleteRecordDialogTemplate
          header="Confirm delete"
          message={`Are you sure to delete ${visible.templateName}?`}
          data={selectedTemplate}
          visible={deleteDialog}
          onHide={onDeleteDialogHide}
          footer={deleteFooterDialogTemplate}
        />
      )}
      {quoteDialog && (
        <QuoteDialogTemplate
          header="Save Template"
          body={quoteDialogBody}
          footer={quoteDialogFooter}
          dimension={{ width: '500', height: '200' }}
          onShown={onTextFocus}
          visible={quoteDialog}
          onHide={quoteDialogHide}
        />
      )}
      {defaultTemplate && (
        <DefaultTemplateDialog
          visible={defaultTemplate}
          onHide={defaultTemplateHide}
          templateList={(templateList || []).filter(
            (item) => item.superQuote == 0
          )}
          customPrinci={customPrinci}
        />
      )}
      <style global jsx>{`
        .quote-container {
          display: flex;
          height: 100%;
          margin: 0 auto;
        }

        .quote-header {
          font-size: 20px;
          text-align: center;
          font-weight: bold;
          margin: 8px auto;
          padding-bottom: 2px;
          letter-spacing: 2px;
          width: 50%;
        }
        .quote-column-chooser {
          width: 50%;
        }
        .quote-note-visibility-label {
          width: 80%;
        }
        .quote-settings-value {
          width: 20%;
        }
        .quote-sidebar,
        .main {
          width: 25%;
          left: 0;
          position: relative;
          transition: all 0.3s ease-out;
          overflow: auto;
          height: 85vh;
          bottom: 0;
          -webkit-overflow-scrolling: touch;
          background: #f5f5f5;
          font-size: 14px;
        }

        .main {
          background-color: white;
          position: initial;
          left: 25%;
          width: 75%;
          right: 0;
          padding: 10px 150px;
        }

        .preview {
          min-height: 530px;
          border: 1px solid #000;
        }

        .preview-padding {
          padding: 20px 30px 20px 50px;
        }

        .footer-comment {
          font-size: 14px;
        }

        .quote-widget-container {
          display: flex;
          width: 25%;
          height: 550px;
          border: 8px solid #f5f5f5;
          outline: 1px solid #e9e9e9;
        }
        .quote-list-title {
          font-size: 14px;
          text-align: center;
          margin-bottom: 10px;
          border-bottom: 1px solid #000;
          padding-bottom: 3px;
        }
        .dx-scrollable-wrapper {
          position: relative;
          width: 100%;
          // height: 25%;
        }
        .dx-list-item-content {
          padding: 10px 0px;
        }
        .dx-fieldset {
          margin: 10px 15px;
          padding: 0;
        }
        .dx-fieldset-header {
          margin: 0 0 10px;
        }
        .dx-overlay-shader .dx-popup-content {
          padding: 0px;
        }
        .dx-radiogroup-horizontal {
          display: flex;
          flex-wrap: nowrap !important;
        }
        .dx-datagrid-content .dx-datagrid-table .dx-row > td,
        .dx-datagrid-content .dx-datagrid-table .dx-row > tr > td {
          vertical-align: top;
        }

        .dx-datagrid-nodata {
          position: relative;
          left: 35%;
        }
        .dx-datagrid-headers .dx-header-row {
          background: #bdbdbd !important;
        }
        .dx-field-label {
          color: #333;
        }
        .dx-switch-handle::before {
          background-color: #7ab8eb;
        }
        .dx-datagrid {
          font-size: 12px;
        }
        .save-as-btn {
          background-color: #10b2ff !important;
        }
        .preview-btn {
          background-color: #db0f04 !important;
        }
        .dx-button-mode-contained .dx-icon {
          color: #fff !important;
        }
        .p-toast-top-right {
          z-index: 9999 !important;
        }
        .ml-10 {
          margin-left: 10rem;
        }
        .logo-box {
          width: auto !important;
          height: auto !important;
          min-width: auto !important;
          min-height: auto !important;
          margin: 0 !important;
          max-width: 200px;
          max-height: 130px;
          object-fit: contain;
        }
        .text-wrapper {
          word-break: break-all;
          max-width: 90px;
          display: block;
        }
        .choose-btn .dx-button-content {
          padding: 7px 10px 8px;
        }
      `}</style>
    </div>
  );
};

export default Quotes;
