import { useCallback, useMemo, useRef, useState } from 'react';
import Popup from 'devextreme-react/popup';
import ScrollView from 'devextreme-react/scroll-view';
import { Button, Template } from 'devextreme-react';
import CustomStore from 'devextreme/data/custom_store';
import DataGrid, {
  Column,
  Editing,
  Scrolling,
  Button as Button1,
  Lookup,
  Toolbar,
  Item,
  Texts,
  RequiredRule,
} from 'devextreme-react/data-grid';
import { Toast } from 'primereact/toast';
import {
  MedPrinciEditorOptions,
  facilityColumns,
  handleErrors,
  manufacturers,
} from '@modules/medical/utils';
import { useMenu } from '@contexts/menu';
import {
  deleteQuoteDefaultTemplate,
  getQuoteDefaultTemplate,
  postQuoteDefaultTemplate,
  updateQuoteDefaultTemplate,
} from '@services/api';
import DeleteRecordDialog from '@modules/expenses/components/expenses/dialogs/deleteRecordDialog';
import SelectBoxItemRender from '@modules/medical/components/SelectBoxItemRender';

const DefaultTemplateDialog = ({
  visible,
  onHide,
  templateList,
  customPrinci,
}) => {
  const { user } = useMenu();
  const gridRef = useRef(null);
  const toast = useRef(null);

  const [deleteDialog, setDeleteDialog] = useState(false);
  const [quoteData, setQuoteData] = useState('');

  const defaultTemplate = useMemo(
    () =>
      new CustomStore({
        key: 'id',
        load: () =>
          getQuoteDefaultTemplate()
            .then(handleErrors)
            .catch(() => []),
        update: async (key, values) => {
          const params = {
            id: key,
            updatedUser: user.userId,
            ...values,
          };

          return updateQuoteDefaultTemplate(params)
            .then(async (response) => {
              if (response.status !== 200 && response.status !== 201) {
                throw Error(response.statusText);
              }
              toast.current.show({
                severity: 'success',
                summary: 'Successful',
                detail: `${customPrinci} default template updated successfully`,
                life: 3000,
              });
              return response.data;
            })
            .catch((e) => {
              toast.current.show({
                severity: 'error',
                summary: 'Update Failed',
                detail: `Duplicate ${customPrinci}`,
                life: 3000,
              });
              return false;
            });
        },
        remove: async (key) => {
          return deleteQuoteDefaultTemplate(key)
            .then((response) => {
              if (response.status !== 200 && response.status !== 201) {
                throw Error(response.statusText);
              }
              toast.current.show({
                severity: 'success',
                summary: 'Successful',
                detail: `${customPrinci} default template deleted successfully`,
                life: 3000,
              });
              return response.data;
            })
            .catch(() => {
              toast.current.show({
                severity: 'error',
                summary: 'Delete failed',
                detail: `Please try after sometimes`,
                life: 3000,
              });
            });
        },
        insert: async (values) => {
          return postQuoteDefaultTemplate(values)
            .then(async (response) => {
              if (response.status !== 200 && response.status !== 201) {
                throw Error(response.statusText);
              }
              toast.current.show({
                severity: 'success',
                summary: 'Successful',
                detail: `New ${customPrinci} default template created successfully`,
                life: 3000,
              });
              return response.data;
            })
            .catch(() => {
              toast.current.show({
                severity: 'error',
                summary: 'Save Failed',
                detail: `Duplicate ${customPrinci}`,
                life: 3000,
              });
            });
        },
      }),
    [customPrinci]
  );

  const calculateCellValue = (e, name) => {
    if (name == 'princiId') return e.princiId === 0 ? null : e.princiId;
    else if (name == 'distributorTemplateId')
      return e.distributorTemplateId === 0 ? null : e.distributorTemplateId;
    else return e.customerTemplateId === 0 ? null : e.customerTemplateId;
  };

  const initNewRow = useCallback((e) => {
    const medicalData = {
      princiId: 0,
      distributorTemplateId: 0,
      customerTemplateId: 0,
      insertedUser: user.userId,
      updatedUser: 0,
    };
    e.data = medicalData;
  }, []);

  const addNewQuoteTemplate = () => {
    const defaultTemplateGridInstance = gridRef.current.instance;
    defaultTemplateGridInstance.saveEditData().then(() => {
      defaultTemplateGridInstance.addRow();
      defaultTemplateGridInstance.saveEditData();
    });
  };

  const confirmDelete = useCallback((e) => {
    console.log(e);
    setQuoteData(e.row.data);
    setDeleteDialog(true);
  }, []);

  const deleteRecords = useCallback(async () => {
    try {
      const instance = gridRef.current.instance;
      const rowIndex = instance.getRowIndexByKey(quoteData.id);
      instance.deleteRow(rowIndex);
      setDeleteDialog(false);
    } catch (e) {
      console.log(e);
    }
  }, [quoteData.id]);

  const onDeleteDialogHide = useCallback(() => {
    setDeleteDialog(false);
  }, []);

  const disableDelete = (e) => e.row.data.princiId == 0;

  const deleteDialogFooter = (
    <>
      <Button
        icon="pi pi-check"
        type="danger"
        text="Delete"
        className="bg-error mr-2"
        onClick={deleteRecords}
      />
      <Button
        icon="pi pi-times"
        type="default"
        text="Cancel"
        className="bg-yellow-600 mr-2"
        onClick={() => {
          setDeleteDialog(false);
        }}
      />
    </>
  );

  const PrincipalSelectBox = (data) => (
    <SelectBoxItemRender
      columnData={facilityColumns(data)}
      id={`principal${data.recId}`}
      title="Other Information"
      displayValue={data.compName}
    />
  );

  const renderPopup = () => {
    return (
      <>
        <ScrollView width="100%" height="100%">
          <DataGrid
            className="expanded-table px-2"
            id="defaultTemplateGrid"
            showColumnHeaders={true}
            dataSource={defaultTemplate}
            key="id"
            ref={gridRef}
            noDataText={`No data found`}
            onInitNewRow={initNewRow}
            showBorders={true}
            showColumnLines={true}
          >
            <Scrolling showScrollbar={true} />
            <Editing
              mode="cell"
              useIcons={true}
              allowAdding={true}
              allowUpdating={true}
              allowDeleting={true}
              selectTextOnEditStart={true}
            >
              <Texts confirmDeleteMessage="" />
            </Editing>
            <Column type="buttons">
              <Button1
                icon="trash"
                hint="Delete"
                onClick={confirmDelete}
                disabled={disableDelete}
              />
            </Column>
            <Column
              dataField="princiId"
              caption={customPrinci}
              calculateCellValue={(e) => calculateCellValue(e, 'princiId')}
              editorOptions={MedPrinciEditorOptions}
              width={150}
              cssClass="dataGrid-cell"
            >
              <RequiredRule />
              <Lookup
                dataSource={manufacturers}
                valueExpr="recId"
                displayExpr="compName"
              />
              <Template
                name="PrincipalItemRender"
                render={PrincipalSelectBox}
              />
            </Column>
            <Column
              dataField="distributorTemplateId"
              caption="For Distributor"
              calculateCellValue={(e) =>
                calculateCellValue(e, 'distributorTemplateId')
              }
              cssClass="dataGrid-cell"
            >
              <Lookup
                dataSource={templateList}
                valueExpr="templateId"
                displayExpr="templateName"
              />
            </Column>
            <Column
              dataField="customerTemplateId"
              caption="For Customer"
              calculateCellValue={(e) =>
                calculateCellValue(e, 'customerTemplateId')
              }
              cssClass="dataGrid-cell"
            >
              <Lookup
                dataSource={templateList}
                valueExpr="templateId"
                displayExpr="templateName"
              />
            </Column>
            <Toolbar>
              <Item location="before">
                <Button
                  icon="add"
                  onClick={addNewQuoteTemplate}
                  type="default"
                  className="bg-primary m-2"
                  text="Add New"
                />
              </Item>
            </Toolbar>
          </DataGrid>

          <DeleteRecordDialog
            header="Confirm delete"
            message={`Please confirm you wish to delete the ${customPrinci} default template ?`}
            visible={deleteDialog}
            onHide={onDeleteDialogHide}
            footer={deleteDialogFooter}
          />
        </ScrollView>
      </>
    );
  };

  return (
    <>
      <Toast ref={toast} baseZIndex="1000" />
      <Popup
        width="800"
        height="400"
        showTitle={true}
        title={`${customPrinci} default template`}
        dragEnabled={false}
        hideOnOutsideClick={false}
        visible={visible}
        onHiding={onHide}
        shadingColor="#00000090"
        contentRender={renderPopup}
      />
    </>
  );
};

export default DefaultTemplateDialog;
