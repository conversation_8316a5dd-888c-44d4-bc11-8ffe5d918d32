import { useCallback, useEffect, useRef, useState } from 'react';
import { Toast } from 'primereact/toast';
import { Popup, Button, DateBox } from 'devextreme-react';
import { useDefaults } from '@contexts/defaults';
import GridBox from '@components/dropdownTagBox/GridBox';
import {
  convertDateFormat,
  getCustomValBasedOnLabel,
  getTodaysDate,
} from 'src/utils';
import {
  divisionReProcessCustomer,
  divisionReProcessDivision,
  divisionReProcessPrincipal,
} from '@services/api';

const updateBtnAttr = { id: 'update-btn' };
const cancelBtnAttr = { id: 'cancel-btn' };
const principalAttr = { id: 'principal-option' };
const customerAttr = { id: 'customer-option' };
const divisionAttr = { id: 'division-option' };

const cellSpanTemplate = (cellElement, cellInfo) => {
  cellElement.innerHTML = `<span title="${cellInfo.value}">${cellInfo.value}</span>`;
};
const labelId = ['IDS_PRINCI', 'IDS_CUSTOMER', 'IDS_STATE'];
const divisionColumns = [
  {
    dataField: 'divisionName',
    caption: 'Division Name',
    filterOperations: ['contains'],
  },
];
const ReprocessDialog = ({
  showDialog,
  onHide,
  reProcessParams,
  setReProcessParams,
  onReprocessBtnClick,
}) => {
  const toast = useRef(null);

  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();

  const [customPrincipal, customCustomer, customState] =
    getCustomValBasedOnLabel(CUSTOM_LABELS, labelId);

  const companyColumns = [
    {
      dataField: 'compName',
      caption: 'Name',
      filterOperations: ['contains'],
      cellTemplate: (cellElement, cellInfo) => {
        cellSpanTemplate(cellElement, cellInfo);
      },
    },
    {
      dataField: 'state',
      caption: customState,
      filterOperations: ['contains'],
      cellTemplate: (cellElement, cellInfo) => {
        cellSpanTemplate(cellElement, cellInfo);
      },
    },
    {
      dataField: 'street',
      caption: 'Street',
      filterOperations: ['contains'],
      cellTemplate: (cellElement, cellInfo) => {
        cellSpanTemplate(cellElement, cellInfo);
      },
    },
  ];

  const [reProcessData, setReProcessData] = useState({
    principalData: [],
    customerData: [],
    divisionData: [],
  });
  const [isLoading, setIsLoading] = useState(false);
  const [fromDate, setFromDate] = useState(getTodaysDate());
  const [principalValue, setPrincipalValue] = useState([]);
  const [customerValue, setCustomerValue] = useState([]);
  const [divisionValue, setDivisionValue] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      const principalData = await (await divisionReProcessPrincipal()).data;
      const customerData = await (await divisionReProcessCustomer()).data;
      const divisionData = await (await divisionReProcessDivision()).data;
      setReProcessData({
        principalData,
        customerData,
        divisionData,
      });
    };
    fetchData();
  }, []);

  useEffect(() => {
    setReProcessParams((prev) => ({
      ...prev,
      fromDate: convertDateFormat(fromDate, 'YYYY-MM-DD'),
      principal: principalValue,
      customer: customerValue,
      division: divisionValue,
    }));
  }, [
    fromDate,
    principalValue,
    customerValue,
    divisionValue,
    setReProcessParams,
  ]);

  const minDateChangeHandler = useCallback(
    (e) => {
      if (e.value) setFromDate(e.value);
    },
    [fromDate]
  );
  const handleReprocessBtnClick = async () => {
    setIsLoading(true);
    try {
      await onReprocessBtnClick();
    } finally {
      setIsLoading(false);
    }
  };

  const createDialogFooter = (
    <>
      <Button
        type="default"
        text="Reprocess"
        icon="pi pi-check"
        onClick={handleReprocessBtnClick}
        className="bg-primary mr-2"
        elementAttr={updateBtnAttr}
        disabled={isLoading}
      />
      <Button
        type="danger"
        text="Cancel"
        icon="pi pi-times"
        className="bg-error mr-2"
        onClick={onHide}
        elementAttr={cancelBtnAttr}
      />
    </>
  );

  const renderPopup = () => {
    return (
      <>
        <Toast ref={toast} />
        <div className="dx-field m-4 mb-8">
          <label className="dx-field-item-label mb-4">From Date</label>
          <DateBox
            value={fromDate}
            onValueChanged={minDateChangeHandler}
            displayFormat="MM-dd-yyyy"
            placeholder="From Date"
            className="mb-4"
          />

          <div className="flex">
            <div className="dx-field w-full gridBox mb-4">
              <GridBox
                gridDataSource={reProcessData?.principalData}
                gridColumns={companyColumns}
                customState={customState}
                boxName={customPrincipal}
                gridBoxValue={principalValue}
                setGridBoxValue={setPrincipalValue}
                dataKey="recId"
                dataText="compName"
                elementAttr={principalAttr}
                width={400}
                defaultSelectionFilter={[]}
              ></GridBox>
            </div>
            <div className="dx-field w-full gridBox mt-1 mb-0 ml-3">
              <GridBox
                gridDataSource={reProcessData?.customerData}
                gridColumns={companyColumns}
                customState={customState}
                boxName={customCustomer}
                gridBoxValue={customerValue}
                setGridBoxValue={setCustomerValue}
                dataKey="recId"
                dataText="compName"
                elementAttr={customerAttr}
                width={400}
                defaultSelectionFilter={[]}
              ></GridBox>
            </div>
            <div className="dx-field w-full gridBox mt-1 mb-0 ml-3">
              <GridBox
                gridDataSource={reProcessData?.divisionData}
                gridColumns={divisionColumns}
                boxName="Division"
                gridBoxValue={divisionValue}
                setGridBoxValue={setDivisionValue}
                dataKey="divisionId"
                dataText="divisionName"
                elementAttr={divisionAttr}
                width={300}
                defaultSelectionFilter={[]}
              ></GridBox>
            </div>
          </div>
        </div>
        <div className="p-dialog-footer popup text-right">
          {createDialogFooter}
        </div>
      </>
    );
  };

  return (
    <Popup
      width={800}
      height="auto"
      maxHeight="100%"
      showTitle={true}
      title="Reprocess Split"
      dragEnabled={false}
      hideOnOutsideClick={false}
      visible={showDialog}
      onHiding={onHide}
      shadingColor="#00000090"
      contentRender={renderPopup}
    />
  );
};

export default ReprocessDialog;
