import { forwardRef } from 'react';
import TreeMap, { Tooltip } from 'devextreme-react/tree-map';
import { convertNumToCurrency } from 'src/utils';

const DataStructureChart = forwardRef(({ chartData, title }, ref) => {
  const customizeTooltip = (arg) => {
    const { data } = arg.node;
    return {
      text: arg.node.isLeaf()
        ? `<span class="city">${
            data.name
          }</span><br/><br/>Sales: ${convertNumToCurrency(arg.value)}`
        : null,
    };
  };

  return (
    <TreeMap
      id="treemap"
      className="chart"
      dataSource={chartData}
      title={title}
      ref={ref}
    >
      <Tooltip
        enabled={true}
        format="currency"
        customizeTooltip={customizeTooltip}
      />
      <style global jsx>
        {`
          .city {
            font-weight: 500;
          }
        `}
      </style>
    </TreeMap>
  );
});

export default DataStructureChart;
