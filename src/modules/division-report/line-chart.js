import { forwardRef } from 'react';
import {
  Chart,
  Series,
  ArgumentAxis,
  CommonSeriesSettings,
  Legend,
  Margin,
  Tooltip,
  Grid,
} from 'devextreme-react/chart';

const getSalesSource = [{ value: 'sales', name: 'Sales' }];

const salesSource = getSalesSource;

const type = 'line';

const LineChart = forwardRef(({ chartData, title }, ref) => {
  return (
    <>
      <Chart
        id="line"
        className="chart"
        title={title}
        dataSource={chartData}
        ref={ref}
      >
        <CommonSeriesSettings argumentField="month" type={type} />
        {salesSource.map((item) => (
          <Series key={item.value} valueField={item.value} name={item.name} />
        ))}
        <Margin bottom={20} />
        <ArgumentAxis
          valueMarginsEnabled={false}
          discreteAxisDivisionMode="crossLabels"
        >
          <Grid visible={true} />
        </ArgumentAxis>
        <Legend
          verticalAlignment="bottom"
          horizontalAlignment="center"
          itemTextPosition="bottom"
        />
        {/* <Export enabled={true} /> */}
        <Tooltip enabled={true} format="currency" />
      </Chart>

      <style global jsx>{`
        .options {
          padding: 20px;
          background-color: rgba(191, 191, 191, 0.15);
          margin-top: 20px;
        }
        .option {
          margin-top: 10px;
        }
        .caption {
          font-size: 18px;
          font-weight: 500;
        }
        .option > span {
          margin-right: 10px;
        }
        .option > .dx-widget {
          display: inline-block;
          vertical-align: middle;
        }
      `}</style>
    </>
  );
});

export default LineChart;
