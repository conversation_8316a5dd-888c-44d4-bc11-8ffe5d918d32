import { useEffect, useMemo, useState } from 'react';
import { SelectBox } from 'devextreme-react';
import GridBox from '@components/dropdownTagBox/GridBox';
import { monthList, quarterList } from 'src/constants';
import { getCurrentYear, getCustomValBasedOnLabel } from 'src/utils';
import { useDefaults } from '@contexts/defaults';

const yearList = new Array(3).fill().map((_, i) => {
  return { id: getCurrentYear() + i - 2, name: getCurrentYear() + i - 2 };
});

const yearAttr = { id: 'year-input' };
const quarterAttr = { id: 'quarter-input' };
const monthAttr = { id: 'month-input' };
const principalAttr = { id: 'principal-input' };
const customerAttr = { id: 'customer-input' };
const regionAttr = { id: 'region-input' };

const HeadSettingsTab = ({
  params,
  setParams,
  paramData,
  isValidate,
  setNoValidate,
}) => {
  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();
  const [principalValue, setPrincipalValue] = useState(params.principals);
  const [customerValue, setCustomerValue] = useState(params.customers);
  const [regionValue, setRegionValue] = useState(params.regions);
  const [quarterDisabled, setQuarterDisabled] = useState(false);
  const [monthDisabled, setMonthDisabled] = useState(false);

  const labelId = ['IDS_PRINCI', 'IDS_CUSTOMER', 'IDS_REGION', 'IDS_STATE'];
  const [customPrincipal, customCustomer, customRegion, customState] =
    getCustomValBasedOnLabel(CUSTOM_LABELS, labelId);

  const cellSpanTemplate = (cellElement, cellInfo) => {
    cellElement.innerHTML = `<span title="${cellInfo.value}">${cellInfo.value}</span>`;
  };

  const companyColumns = [
    {
      dataField: 'compName',
      caption: 'Name',
      filterOperations: ['contains'],
      cellTemplate: (cellElement, cellInfo) => {
        cellSpanTemplate(cellElement, cellInfo);
      },
    },
    {
      dataField: 'state',
      caption: customState,
      filterOperations: ['contains'],
      cellTemplate: (cellElement, cellInfo) => {
        cellSpanTemplate(cellElement, cellInfo);
      },
    },
    {
      dataField: 'street',
      caption: 'Street',
      filterOperations: ['contains'],
      cellTemplate: (cellElement, cellInfo) => {
        cellSpanTemplate(cellElement, cellInfo);
      },
    },
  ];
  const regionColumns = [
    {
      dataField: 'compName',
      caption: 'Name',
      filterOperations: ['contains'],
    },
  ];

  const onSelectValueChanged = (e, name) => {
    setNoValidate();
    setParams((prev) => ({
      ...prev,
      [name]: e.value,
    }));

    if (name == 'year') {
      setParams((prev) => ({
        ...prev,
        quarter: 0,
        month: 0,
      }));
    }
    if (name == 'quarter' && e.value !== 0) {
      setMonthDisabled(true);
    } else {
      setMonthDisabled(false);
    }
    if (name == 'month' && e.value !== 0) {
      setQuarterDisabled(true);
    } else {
      setQuarterDisabled(false);
    }
  };

  useEffect(() => {
    setParams((prev) => ({
      ...prev,
      principals: principalValue,
      customers: customerValue,
      regions: regionValue,
    }));
  }, [principalValue, customerValue, regionValue, setParams]);
  const regionData = useMemo(
    () =>
      paramData.regionData.map((item) => ({
        recId: item.id,
        compName: item.region,
      })),
    [paramData.regionData]
  );
  return (
    <aside className="quote-sidebar form">
      <div className="dx-fieldset mx-3 my-2">
        <div className="dx-field flex">
          <div className="dx-field select mb-0">
            <div className="dx-field flex">
              <div className="dx-field-label">Year</div>
              <div className="dx-field-value w-full">
                <SelectBox
                  placeholder="Year"
                  dataSource={yearList}
                  value={params.year}
                  valueExpr="id"
                  displayExpr="name"
                  searchEnabled={false}
                  searchMode="contains"
                  searchExpr="name"
                  searchTimeout={200}
                  minSearchLength={0}
                  showDataBeforeSearch={true}
                  onValueChanged={(e) => {
                    onSelectValueChanged(e, 'year');
                  }}
                  focusStateEnabled={true}
                  name="pdfTemplate"
                  onKeyUp={() => {
                    setNoValidate();
                  }}
                  className={isValidate.year ? 'mt-1 border-red-500' : 'mt-1'}
                  elementAttr={yearAttr}
                />
              </div>
            </div>
            <div className="dx-field flex">
              <div className="dx-field-label">Quarter</div>
              <div className="dx-field-value w-full">
                <SelectBox
                  placeholder="Quarter"
                  dataSource={quarterList}
                  value={params.quarter}
                  valueExpr="id"
                  displayExpr="name"
                  searchEnabled={false}
                  searchMode="contains"
                  searchExpr="name"
                  searchTimeout={200}
                  minSearchLength={0}
                  showDataBeforeSearch={true}
                  onValueChanged={(e) => {
                    onSelectValueChanged(e, 'quarter');
                  }}
                  focusStateEnabled={true}
                  name="pdfTemplate"
                  onKeyUp={() => {
                    setNoValidate();
                  }}
                  className={
                    isValidate.quarter ? 'mt-1 border-red-500' : 'mt-1'
                  }
                  disabled={quarterDisabled}
                  elementAttr={quarterAttr}
                />
              </div>
            </div>
            <div className="dx-field flex">
              <div className="dx-field-label">Month</div>
              <div className="dx-field-value w-full">
                <SelectBox
                  placeholder="Month"
                  dataSource={monthList}
                  value={params.month}
                  valueExpr="id"
                  displayExpr="name"
                  searchEnabled={false}
                  searchMode="contains"
                  searchExpr="name"
                  searchTimeout={200}
                  minSearchLength={0}
                  showDataBeforeSearch={true}
                  onValueChanged={(e) => {
                    onSelectValueChanged(e, 'month');
                  }}
                  focusStateEnabled={true}
                  name="pdfTemplate"
                  onKeyUp={() => {
                    setNoValidate();
                  }}
                  className={isValidate.month ? 'mt-1 border-red-500' : 'mt-1'}
                  elementAttr={monthAttr}
                  disabled={monthDisabled}
                />
              </div>
            </div>
          </div>
          <div className="dx-field w-full flex">
            <div className="dx-field w-full gridBox mt-1 mb-0 ml-3">
              <GridBox
                gridDataSource={paramData.principalData}
                gridColumns={companyColumns}
                customState={customState}
                boxName={customPrincipal}
                gridBoxValue={principalValue}
                setGridBoxValue={setPrincipalValue}
                dataKey="recId"
                dataText="compName"
                elementAttr={principalAttr}
              ></GridBox>
            </div>
            <div className="dx-field w-full gridBox mt-1 mb-0 ml-3">
              <GridBox
                gridDataSource={paramData.customerData}
                gridColumns={companyColumns}
                customState={customState}
                boxName={customCustomer}
                gridBoxValue={customerValue}
                setGridBoxValue={setCustomerValue}
                dataKey="recId"
                dataText="compName"
                elementAttr={customerAttr}
              ></GridBox>
            </div>
            <div className="dx-field w-full gridBox mt-1 mb-0 ml-3">
              <GridBox
                gridDataSource={regionData}
                gridColumns={regionColumns}
                boxName={customRegion}
                gridBoxValue={regionValue}
                setGridBoxValue={setRegionValue}
                fieldName="region"
                dataKey="recId"
                dataText="compName"
                elementAttr={regionAttr}
              ></GridBox>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .quote-setting-content[disabled] {
          pointer-events: none;
          opacity: 0.5;
          background: #ccc;
        }
        .sub-head {
          background: #c8d6f9;
          height: 40px;
          font-size: 18px;
          text-align: center;
          line-height: 40px;
          margin: 0;
        }

        .note-section {
          margin: 10px 0;
          padding: 10px 10px;
          font-size: 13px;
          background: #fff;
          font-style: italic;
        }

        .dx-field.select {
          width: 300px;
        }
      `}</style>
    </aside>
  );
};

export default HeadSettingsTab;
