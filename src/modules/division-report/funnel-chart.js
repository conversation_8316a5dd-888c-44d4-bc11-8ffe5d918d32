import { forwardRef } from 'react';

import Funnel, {
  Title,
  Margin,
  Tooltip,
  Item,
  Border,
  Label,
} from 'devextreme-react/funnel';
import { convertNumToCurrency } from 'src/utils/index.js';

function formatLabel(arg) {
  return `<span class="label">${convertNumToCurrency(
    arg.item.value
  )}</span><br/>${arg.item.argument}`;
}

const customizeTooltip = (e) => {
  return {
    text: `<b">${e.item.argument}</b><br/>${convertNumToCurrency(e.value)}`,
  };
};

const FunnelChart = forwardRef(({ chartData, title }, ref) => {
  return (
    <>
      <Funnel
        id="funnel"
        className="chart"
        dataSource={chartData}
        palette="Soft Pastel"
        argumentField="argument"
        valueField="value"
        ref={ref}
      >
        <Title text={title}>
          <Margin bottom={30} />
        </Title>
        {/* <Export enabled={true} /> */}
        <Tooltip
          enabled={true}
          format="currency"
          customizeTooltip={customizeTooltip}
        />
        <Item>
          <Border visible={true} />
        </Item>
        <Label
          visible={true}
          position="inside"
          backgroundColor="none"
          customizeText={formatLabel}
        />
      </Funnel>
      <style global jsx>{`
        #funnel .label {
          font-size: 14px;
          padding: 10px;
          font-weight: 900;
        }
      `}</style>
    </>
  );
});

export default FunnelChart;
