import { useState, useEffect } from 'react';
import DataGridBox from '@components/dropdownTagBox/DataGridBox';

const SettingsTab = ({ params, setParams, paramData }) => {
  const gridData = paramData.userData;

  const [userValue, setUserValue] = useState(
    gridData.reduce((obj, item) => {
      const { name, ...id } = item;
      const uid = Object.keys(id);
      return Object.assign(obj, { [uid]: [] });
    }, {})
  );

  useEffect(() => {
    setParams((prev) => ({
      ...prev,
      divisions: userValue,
    }));
  }, [userValue, setParams]);

  const onselectionchanged = (val, id) => {
    setUserValue((prev) => ({
      ...prev,
      [id]: val,
    }));
  };

  return (
    <aside className="quote-sidebar mt-2 ml-2 form">
      <div className="dx-field mx-2">
        {gridData.map((item) => {
          const keyId = Object.keys(item)[0];
          return (
            <DataGridBox
              key={item.name}
              gridDataSource={item[keyId]}
              columnField="userName"
              columnCaption={item.name}
              gridBoxValue={userValue[keyId]}
              setGridBoxValue={onselectionchanged}
              id={keyId}
            />
          );
        })}
      </div>

      <style jsx>{`
        .quote-sidebar .dx-field {
          width: 18rem;
          max-height: 64rem;
          overflow-y: auto;
        }
      `}</style>
    </aside>
  );
};

export default SettingsTab;
