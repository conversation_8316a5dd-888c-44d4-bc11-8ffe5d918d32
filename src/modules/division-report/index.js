import { useEffect, useRef, useState } from 'react';
import LineChart from './line-chart';
import BarChart from './bar-chart';
import DataStructureChart from './data-structure-chart';
import FunnelChart from './funnel-chart';
import SettingsTab from './settings-tab';
import HeadSettingsTab from './head-settings-tab';
import {
  convertNumToCurrency,
  getCustomValBasedOnLabel,
} from 'src/utils/index.js';
import { Button } from 'devextreme-react';
import { Toast } from 'primereact/toast';

import {
  divisionReportDashboard,
  divisionPrincipals,
  divisionCustomers,
  divisionRegions,
  divisionUsers,
  divisionReprocess,
} from '@services/api';
import { getCurrentYear } from 'src/utils';
import { useDefaults } from '@contexts/defaults';
import ReprocessDialog from './reprocessDialog';

const reportAttr = { id: 'regenerate-report' };
const reprocessAttr = { id: 'reprocess-report' };

const DivisionReport = () => {
  const [data, setData] = useState({
    salesReport: null,
    manufactureReport: null,
    divisionReport: null,
    customerReport: null,
  });
  const [reprocessDialog, setReprocessDialog] = useState(false);
  const [params, setParams] = useState({
    year: getCurrentYear(),
    quarter: 0,
    month: 0,
    principals: [],
    customers: [],
    regions: [],
    divisions: [],
  });

  const [paramData, setParamData] = useState({
    principalData: [],
    customerData: [],
    regionData: [],
    userData: [],
  });

  const [reProcessParams, setReProcessParams] = useState({
    fromDate: '',
    principal: [],
    customer: [],
    division: [],
  });

  const [isValidate, setIsValidate] = useState({
    year: false,
    quarter: false,
    month: false,
  });

  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();

  const labelId = ['IDS_PRINCI', 'IDS_CUSTOMER'];
  const [customPrincipal, customCustomer] = getCustomValBasedOnLabel(
    CUSTOM_LABELS,
    labelId
  );

  const toast = useRef(null);

  const lineChartRef = useRef(null);
  const barChartRef = useRef(null);
  const dsChartRef = useRef(null);
  const funnelChartRef = useRef(null);
  lineChartRef?.current?.instance.option(
    'loadingIndicator.text',
    `Loading Sales...`
  );
  barChartRef?.current?.instance.option(
    'loadingIndicator.text',
    `Loading ${customPrincipal ?? ''}...`
  );
  dsChartRef?.current?.instance.option(
    'loadingIndicator.text',
    `Loading Division...`
  );
  funnelChartRef?.current?.instance.option(
    'loadingIndicator.text',
    `Loading ${customCustomer ?? ''}...`
  );
  lineChartRef?.current?.instance.option('loadingIndicator.font.size', '20');
  barChartRef?.current?.instance.option('loadingIndicator.font.size', '20');
  dsChartRef?.current?.instance.option('loadingIndicator.font.size', '20');
  funnelChartRef?.current?.instance.option('loadingIndicator.font.size', '20');

  useEffect(() => {
    const fetchData = async () => {
      const principalData = await (await divisionPrincipals()).data;
      const customerData = await (await divisionCustomers()).data;
      const regionData = await (await divisionRegions()).data;
      const userData = await (await divisionUsers()).data;
      setParamData({
        ...paramData,
        principalData: principalData,
        customerData: customerData,
        regionData: regionData,
        userData: userData,
      });
    };
    fetchData();
    reportButtonClicked();
  }, []);

  const divisionParams = () => {
    let divisionStr = '';
    for (const [key, value] of Object.entries(params.divisions)) {
      paramData.userData.forEach((item1, key1) => {
        if (paramData.userData[key1][key]?.length && value?.length) {
          if (paramData.userData[key1][key]?.length == value?.length) {
            divisionStr += `{${key}},`;
          } else {
            divisionStr += value.length ? `{${key}: ${value}},` : ``;
          }
        }
      });
    }

    return {
      year: params.year,
      quarter: params.quarter,
      month: params.month,
      principals: params.principals.map((item) => item.recId).toString(),
      customers: params.customers.map((item) => item.recId).toString(),
      regions: params.regions.map((item) => item.recId).toString(),
      divisions: divisionStr,
    };
  };

  const onReportError = ({ message }) => {
    setData({
      ...data,
      salesReport: '',
      totalSales: 0,
      manufactureReport: '',
      divisionReport: '',
      customerReport: '',
    });
    toast.current.show({
      severity: 'error',
      summary: 'Server Error',
      detail: message,
      life: 3000,
    });
  };

  const setNoValidate = () => {
    setIsValidate({ year: false, quarter: false, month: false });
  };

  const reprocessButtonClicked = () => {
    setReprocessDialog(true);
  };

  const reportButtonClicked = async () => {
    if (params.year == null || params.quarter == null || params.month == null) {
      onReportError({ message: 'Please select required fields' });
      setIsValidate({
        year: params.year == null ? true : false,
        quarter: params.quarter == null ? true : false,
        month: params.month == null ? true : false,
      });
      return;
    }
    lineChartRef?.current?.instance.showLoadingIndicator();
    barChartRef?.current?.instance.showLoadingIndicator();
    dsChartRef?.current?.instance.showLoadingIndicator();
    funnelChartRef?.current?.instance.showLoadingIndicator();
    const param = divisionParams();

    await divisionReportDashboard(param).then(async (response) => {
      if (response?.status === 200) {
        const result = response.data.data;

        const salesData = result[0].salesReport[0].map((item) => {
          return {
            month: item.REP_DATA_NAME,
            sales: Number(item.REP_VALUE),
          };
        });
        const totalSales = Number(result[0].salesReport[1].totalSales);
        const manufacturerData = result[1].manufactureReport.map((itemNew) => {
          return {
            REP_DATA_NAME: itemNew.REP_DATA_NAME,
            REP_VALUE: Number(itemNew.REP_VALUE),
          };
        });

        const divisionData = result[2].divisionReport.map((itemNew) => {
          return {
            name: itemNew.REP_DATA_NAME,
            value: Number(itemNew.REP_VALUE),
          };
        });

        const customerData = result[3].customerReport.map((itemNew) => {
          return {
            argument: itemNew.REP_DATA_NAME,
            value: Number(itemNew.REP_VALUE),
          };
        });
        if (
          salesData.length ||
          manufacturerData.length ||
          divisionData.length ||
          customerData.length
        )
          setData((prev) => ({
            ...prev,
            salesReport: salesData,
            totalSales: totalSales,
            manufactureReport: manufacturerData,
            divisionReport: divisionData,
            customerReport: customerData,
          }));
      } else {
        onReportError({ message: 'Report Display Failed' });
      }
    });
  };

  const onReprocessBtnClick = async () => {
    const param = {
      fromDate: reProcessParams.fromDate,
      principals: reProcessParams.principal
        .map((item) => item.recId)
        .toString(),
      customers: reProcessParams.customer.map((item) => item.recId).toString(),
      divisions: reProcessParams.division
        .map((item) => item.divisionId)
        .toString(),
    };

    await divisionReprocess(param).then(async (response) => {
      if (response?.status === 200) {
        const result = response.data;
        if (result == 'success') {
          toast.current.show({
            severity: 'success',
            summary: 'Reprocessed',
            detail:
              'Reprocessed, Please regenerate the report to reflect the updates.',
            life: 3000,
          });
          setReprocessDialog(false);
        }
      } else {
        onReportError({ message: 'Failed to reprocess the division splits' });
      }
    });
  };

  return (
    <div className="container">
      {reprocessDialog && (
        <ReprocessDialog
          reProcessParams={reProcessParams}
          setReProcessParams={setReProcessParams}
          onReprocessBtnClick={onReprocessBtnClick}
          showDialog={reprocessDialog}
          onHide={() => setReprocessDialog(false)}
        />
      )}
      <Toast ref={toast} baseZIndex={2000} />
      <HeadSettingsTab
        params={params}
        setParams={setParams}
        paramData={paramData}
        isValidate={isValidate}
        setNoValidate={setNoValidate}
      />
      <div className="grid-container">
        <SettingsTab
          params={params}
          setParams={setParams}
          paramData={paramData}
        />
        <div className="container">
          <div className="grid mb-3">
            <div className="col-12">
              <div className="chart w-full flex justify-content-between align-items-center">
                <div>
                  <h2 className="m-0">
                    TOTAL SALES :
                    {!isNaN(data.totalSales) &&
                      convertNumToCurrency(data.totalSales)}
                  </h2>
                </div>
                <div>
                  <Button
                    type="success"
                    text="Reprocess Split"
                    onClick={reprocessButtonClicked}
                    elementAttr={reprocessAttr}
                    className="mr-3"
                  />
                  <Button
                    type="success"
                    text="Regenerate Report"
                    onClick={reportButtonClicked}
                    elementAttr={reportAttr}
                  />
                </div>
              </div>
            </div>
            <div className="col-6 h-30rem">
              <LineChart
                chartData={data.salesReport}
                title="Sales"
                ref={lineChartRef}
              />
            </div>
            <div className="col-6 h-30rem">
              <BarChart
                chartData={data.manufactureReport}
                title={customPrincipal}
                xAxis={customPrincipal}
                ref={barChartRef}
              />
            </div>
            <div className="col-6 h-30rem">
              <DataStructureChart
                chartData={data.divisionReport}
                title="Division"
                ref={dsChartRef}
              />
            </div>
            <div className="col-6 h-30rem">
              <FunnelChart
                chartData={data.customerReport}
                title={customCustomer}
                ref={funnelChartRef}
              />
            </div>
          </div>
        </div>
      </div>

      <style global jsx>{`
        .container {
          height: 100%;
          width: 100%;
          margin: 0 auto;
        }
        .chart-container {
          height: 100%;
          width: 75%;
          margin: 0 auto;
        }
        .grid-container {
          display: flex;
          height: 100%;
          width: 100%;
          margin: 0 auto;
        }
        .chart {
          height: 100%;
          width: 100%;
          padding: 5px 10px;
          margin: 5px;
          border: 1px solid #ababab;
          background: #fff;
        }
        .gridBox .dx-dropdowneditor-input-wrapper .dx-placeholder {
          // position: relative;
          // top: 0;
          // left: -40%;
          // right: 0;
          font-weight: bold;
          color: #000;
        }
        .gridBox .dx-dropdowneditor-icon::before {
          content: '\f027';
        }
      `}</style>
    </div>
  );
};

export default DivisionReport;
