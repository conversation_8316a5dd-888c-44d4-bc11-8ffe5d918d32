import { forwardRef } from 'react';

import {
  Chart,
  Series,
  CommonSeriesSettings,
  Legend,
  Label,
  Format,
  CommonAxisSettings,
  ArgumentAxis,
  Tooltip,
} from 'devextreme-react/chart';

const axisDataRender = (e) => {
  // limit the number of characters to 20 and show hint with full text
  return e.valueText.length > 25
    ? e.valueText.substring(0, 25) + '...'
    : e.valueText;
};

const customizeTooltip = (e) => {
  return {
    text: `<b>${e.argumentText} </b><br/>${e.valueText}`,
  };
};

const BarChart = forwardRef(({ chartData, title, xAxis }, ref) => {
  return (
    <>
      <Chart
        id="bar"
        className="chart"
        title={title}
        dataSource={chartData}
        ref={ref}
        resolveLabelOverlapping="stack"
      >
        <CommonSeriesSettings
          argumentField="REP_DATA_NAME"
          type="bar"
          hoverMode="allArgumentPoints"
          selectionMode="allArgumentPoints"
          barWidth={20}
          minBarSize={4}
        >
          <Label visible={true} overlappingBehavior="stack" rotationAngle={-25}>
            <Format type="currency" precision={0} />
          </Label>
        </CommonSeriesSettings>
        <CommonAxisSettings>
          <Label font={{ size: 10 }} />
        </CommonAxisSettings>
        <ArgumentAxis>
          <Label
            wordWrap="normal"
            overlappingBehavior="rotate"
            rotationAngle={-25}
            customizeText={axisDataRender}
            customizeHint={(e) => {
              return e.valueText;
            }}
          />
        </ArgumentAxis>
        <Series valueField="REP_VALUE" name={xAxis} />
        <Legend
          verticalAlignment="bottom"
          horizontalAlignment="center"
        ></Legend>
        <Tooltip
          enabled={true}
          format="currency"
          customizeTooltip={customizeTooltip}
        />
        {/* <Export enabled={true} /> */}
      </Chart>
    </>
  );
});

export default BarChart;
