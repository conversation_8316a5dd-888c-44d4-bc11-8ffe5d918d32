/** Next JS and React JS imports */
import { useState, useRef, useCallback, useMemo } from 'react';

/** PrimeReact imports */
import { Toast } from 'primereact/toast';

/** DevExtreme imports */
import DataGrid, {
  Column,
  Selection,
  Editing,
  Toolbar,
  Item,
  FilterRow,
  Paging,
  Pager,
  Scrolling,
} from 'devextreme-react/data-grid';
import CustomStore from 'devextreme/data/custom_store';

/** Other Third party packages */
import * as dayjs from 'dayjs';
import EditRegisterDialog from './editRegisterDialog';
import DeleteRegisterDialog from './deleteRegisterDialog';

/** Custom Service */
import {
  getRegistrations,
  deleteRegistrations,
  updateRegitsrations,
} from '@services/api';
import { Button } from 'devextreme-react';
import {
  convertDateFormat,
  getCustomValBasedOnLabel,
  getTodaysDate,
} from 'src/utils';
import { useDefaults } from '@contexts/defaults';
import { DATE_FILTER_FORMAT } from 'src/constants';
const labelId = [
  'IDS_PRINCI',
  'IDS_CUSTOMER',
  'IDS_DISTRI',
  'IDS_MFG_PART_NUM',
  'IDS_PROGRAM',
  'IDS_VALUE',
  'IDS_OPP_OWNER',
  'IDS_DISTRI_CON',
  'IDS_SALES_TEAM',
  'IDS_APPROVED_DATE',
];
const editBtnAttr = { id: 'edit-selected-btn' };
const deleteBtnAttr = { id: 'delete-selected-btn' };
const confirmBtnAttr = { id: 'confirm-delete-btn' };
const cancelBtnAttr = { id: 'cancel-btn' };

/** Utility Function */
function handleErrors(response) {
  if (response.status !== 200) {
    throw Error(response.statusText);
  }
  return response.data;
}

let dataGrid;

const regCellTemplate = (cellData) => {
  return (
    <a
      href={`/RepfabricCRM/opploop/opportunity/OpportunityView.xhtml?opid=${cellData.data.oppItemOppId}`}
      className="text-blue-600"
    >
      {cellData?.value}
    </a>
  );
};

const getColumnDataField = (ref, index) =>
  ref.current.instance.columnOption(index, 'dataField');

const getFilterValue = (filter) =>
  typeof filter.filterValue == 'object'
    ? convertDateFormat(filter.filterValue, 'YYYY-MM-DD')
    : filter.filterValue;

const filterArray = (filter, ref) => {
  let filterData = 'filter=null';
  if (filter) {
    filterData =
      typeof filter[0] == 'object'
        ? filter.map((item) => {
            if (typeof item == 'object') {
              return `${getColumnDataField(
                ref,
                item.columnIndex
              )}=${getFilterValue(item)}&`;
            }
          })
        : `${getColumnDataField(ref, filter.columnIndex)}=${getFilterValue(
            filter
          )}
`;
  }
  return filterData.toString().replace(/,/g, '');
};

const RegisterDatatable = () => {
  const {
    defaults: { CUSTOM_LABELS, DATE_FORMAT },
  } = useDefaults();
  const [EditDialog, setEditDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [registerData, setRegisterData] = useState([]);
  const [modeData, setModeData] = useState([]);
  const [selectedRegister, setSelectedRegister] = useState([]);
  const [changeData, setChangeData] = useState('');
  const gridRef = useRef(null);
  const toast = useRef(null);

  function onInitialized(e) {
    dataGrid = e.component;
  }
  const registrationSource = useMemo(
    () =>
      new CustomStore({
        key: 'recId',
        load: async (loadOptions) => {
          const page =
            loadOptions.skip == 0 ? 1 : loadOptions.skip / loadOptions.take + 1;
          const [{ selector, ...order }] = loadOptions.sort ?? [
            { selector: 'recId', desc: false },
          ];
          const sortOrder =
            Object.values(order).toString() == 'true' ? 'desc' : 'asc';

          const filterValue = filterArray(loadOptions.filter, gridRef);

          const param = `page=${page}&size=${loadOptions.take}&sort=${selector}&order=${sortOrder}&${filterValue}`;
          const result = await getRegistrations(param)
            .then(handleErrors)
            .catch(() => []);
          return {
            data: result.contents,
            totalCount: result.totalItems,
          };
        },
      }),
    []
  );
  const emptyRegisterData = {
    status: '',
    approveDate: dayjs.utc(getTodaysDate()).toDate(),
    expiryDate: dayjs.utc(getTodaysDate()).toDate(),
  };
  const [
    customPrincipal,
    customCustomer,
    customDistributor,
    customPartNo,
    customTopic,
    customValue,
    customOppOwner,
    customDistriCon,
    customSalesTeam,
    customApprovedDate,
  ] = getCustomValBasedOnLabel(CUSTOM_LABELS, labelId);

  const calculateApprDateFilter = (filterValue, selectedFilterOperation) => {
    return ['approvedDate', selectedFilterOperation, new Date(filterValue)];
  };

  const calculateExpDateFilter = (filterValue, selectedFilterOperation) => {
    return ['expiryDate', selectedFilterOperation, new Date(filterValue)];
  };

  const gridColumns = [
    {
      dataField: 'regNumber',
      caption: 'Registration Number',
      cellRender: regCellTemplate,
      filterOperations: ['contains'],
      filterId: 'regNumber-filter',
    },
    {
      dataField: 'principal',
      caption: customPrincipal,
      filterOperations: ['contains'],
      filterId: 'principal-filter',
    },
    {
      dataField: 'customer',
      caption: customCustomer,
      filterOperations: ['contains'],
      filterId: 'customer-filter',
    },
    {
      dataField: 'salesTeam',
      caption: customSalesTeam,
      filterOperations: ['contains'],
      filterId: 'salesTeam-filter',
    },
    {
      dataField: 'distriContName',
      caption: customDistriCon,
      filterOperations: ['contains'],
      filterId: 'distriContName-filter',
    },
    {
      dataField: 'oppOwnerName',
      caption: customOppOwner,
      filterOperations: ['contains'],
      filterId: 'oppOwnerName-filter',
    },
    {
      dataField: 'distributor',
      caption: customDistributor,
      filterOperations: ['contains'],
      filterId: 'distributor-filter',
    },
    {
      dataField: 'approvedDate',
      caption: customApprovedDate,
      dataType: 'date',
      filterOperations: ['='],
      calculateFilterExpression: calculateApprDateFilter,
      format: DATE_FILTER_FORMAT[DATE_FORMAT],
      editorOptions: {
        displayFormat: DATE_FILTER_FORMAT[DATE_FORMAT],
        useMaskBehavior: true,
      },
      filterId: 'approvedDate-filter',
    },
    {
      dataField: 'expiryDate',
      caption: 'Expiry Date',
      dataType: 'date',
      filterOperations: ['='],
      calculateFilterExpression: calculateExpDateFilter,
      format: DATE_FILTER_FORMAT[DATE_FORMAT],
      editorOptions: {
        displayFormat: DATE_FILTER_FORMAT[DATE_FORMAT],
        useMaskBehavior: true,
      },
      filterId: 'expiryDate-filter',
    },

    {
      dataField: 'itemPartMnf',
      caption: customPartNo,
      filterOperations: ['contains'],
      filterId: 'itemPartMnf-filter',
    },

    {
      dataField: 'itemCustProg',
      caption: customTopic,
      filterOperations: ['contains'],
      filterId: 'itemCustProg-filter',
    },
    {
      dataField: 'status',
      caption: 'Status',
      filterOperations: ['contains'],
      filterId: 'status-filter',
    },
    {
      dataField: 'oppValue',
      caption: customValue,
      filterOperations: ['contains'],
      filterId: 'oppValue-filter',
    },
  ];

  const onEditorPreparing = (e) => {
    gridColumns.forEach(({ dataField, filterId }) => {
      if (e.parentType == 'filterRow' && e.dataField == dataField) {
        e.editorOptions.inputAttr.id = filterId;
      }
    });
  };

  const onInputChange = useCallback((e, name) => {
    setRegisterData((_registerData) => ({
      ..._registerData,
      [name]: e,
    }));
  }, []);

  const editRegister = (e) => {
    const rowData = gridRef.current.instance.getSelectedRowsData();
    if (rowData.length > 0) {
      setRegisterData(emptyRegisterData);
      setSelectedRegister(rowData);
      setModeData({
        title: 'Data Service',
      });
      setChangeData('');
      setEditDialog(true);
    } else {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Please Select rows for editing`,
        life: 3000,
      });
    }
  };

  const getFieldValue = () => {
    switch (changeData) {
      case 1:
        return {
          field: 'REGI_APPROVE_DATE',
          value: dayjs(registerData.approveDate).format('YYYY-MM-DD'),
        };
      case 2:
        return {
          field: 'REGI_EXPIRY_DATE',
          value: dayjs(registerData.expiryDate).format('YYYY-MM-DD'),
        };
      default:
        return { field: 'REGI_STATUS', value: registerData.status };
    }
  };

  const updateRegistration = async () => {
    const updateData = selectedRegister;
    const { field, value } = getFieldValue();
    let errorCount = 0;
    const params = updateData.map((element) => {
      if (
        (field == 'REGI_APPROVE_DATE' && element.expiryDate < value) ||
        (field == 'REGI_EXPIRY_DATE' && element.approvedDate > value)
      ) {
        errorCount++;
        return {
          oppItemOppId: element.oppItemOppId,
          regNumber: element.regNumber,
          field,
          value:
            field == 'REGI_APPROVE_DATE'
              ? element.approvedDate
              : element.expiryDate,
        };
      }
      return {
        oppItemOppId: element.oppItemOppId,
        regNumber: element.regNumber,
        field,
        value,
      };
    });
    if (errorCount > 0) {
      const errorField =
        field == 'REGI_APPROVE_DATE' ? 'Approved date' : 'Expiry date';
      toast.current.show({
        severity: 'error',
        summary: 'Update Failed',
        detail: `${errorField} update failed for ${errorCount} records`,
        life: 3000,
      });
    }
    try {
      await updateRegitsrations(params);

      gridRef.current?.instance.refresh();
      gridRef.current?.instance.deselectAll();

      setEditDialog(false);
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Update Failed',
        detail: 'Please try after sometime',
        life: 3000,
      });
    }
  };

  const confirmDelete = (e) => {
    if (e.row) {
      setRegisterData([e.row.data]);
      setDeleteDialog(true);
    } else {
      const rowData = gridRef.current.instance.getSelectedRowsData();
      if (rowData.length > 0) {
        setRegisterData(rowData);
        setDeleteDialog(true);
      } else {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: `Please Select rows for deleting`,
          life: 3000,
        });
      }
    }
  };

  const resetDelete = () => {
    // Refresh the datagrid for changes effected
    gridRef.current?.instance.refresh();
    setDeleteDialog(false);
    gridRef.current?.instance.deselectAll();
    setSelectedRegister([]);
  };

  const deleteRecords = () => {
    const params = registerData.map((item) => {
      return {
        oppItemOppId: item.oppItemOppId,
        regNumber: item.regNumber,
      };
    });
    return deleteRegistrations(params).then((response) => {
      if (response.status === 200) {
        toast.current.show({
          severity: 'success',
          summary: 'Successful',
          detail: `Registration deleted Successfully`,
          life: 3000,
        });
      }
      resetDelete();
    });
  };

  const onDeleteDialogHide = useCallback(() => {
    setDeleteDialog(false);
  }, []);

  const reportDialogTemplate = (
    <>
      <Button
        icon="pi pi-check"
        type="danger"
        text="Delete"
        className="bg-error mr-2"
        onClick={deleteRecords}
        elementAttr={confirmBtnAttr}
      />
      <Button
        icon="pi pi-times"
        type="default"
        text="Cancel"
        className="bg-yellow-600 mr-2"
        onClick={resetDelete}
        elementAttr={cancelBtnAttr}
      />
    </>
  );

  const clearFilter = () => {
    gridRef.current?.instance.clearFilter();
    gridRef.current?.instance.deselectAll();
  };

  return (
    <div className="grid ">
      <div className="col-12">
        <Toast ref={toast} />
        <div className="w-full m-2">
          <Toast ref={toast} />
          <DataGrid
            id="grid-container"
            ref={gridRef}
            dataSource={registrationSource}
            allowColumnReordering={true}
            rowAlternationEnabled={true}
            showBorders={true}
            remoteOperations={true}
            allowColumnResizing={true}
            columnAutoWidth={true}
            noDataText="No Registration Found"
            onInitialized={onInitialized}
            onEditorPreparing={onEditorPreparing}
          >
            <Paging defaultPageSize={20} />
            <Pager
              showPageSizeSelector={false}
              showNavigationButtons={true}
              showInfo={true}
            />

            <Editing
              mode="cell"
              useIcons={false}
              allowUpdating={false}
              allowDeleting={false}
              selectTextOnEditStart={false}
            />

            <Selection
              mode="multiple"
              width={300}
              selectAllMode="page"
              // deferred={true}
              showCheckBoxesMode="always"
            />

            <Scrolling mode="standard" showScrollbar="always" />

            <FilterRow visible={true} />

            {gridColumns.map((column, index) => (
              <Column key={column.dataField} {...column} />
            ))}

            <Toolbar>
              <Item location="before">
                <Button
                  icon="edit"
                  type="default"
                  className="bg-primary"
                  text="Edit Selected Rows"
                  onClick={editRegister}
                  elementAttr={editBtnAttr}
                />
              </Item>
              <Item location="before">
                <Button
                  icon="trash"
                  type="danger"
                  text="Delete Selected Rows"
                  onClick={confirmDelete}
                  elementAttr={deleteBtnAttr}
                />
              </Item>
              <Item location="after">
                <Button
                  icon="clear"
                  onClick={clearFilter}
                  hint="Clear All Filters"
                />
              </Item>
            </Toolbar>
          </DataGrid>

          {EditDialog && (
            <EditRegisterDialog
              visible={EditDialog}
              onHide={() => {
                setEditDialog(false);
              }}
              registerData={registerData}
              modeData={modeData}
              onInputChange={onInputChange}
              onUpdateButtonClick={updateRegistration}
              changeData={changeData}
              setChangeData={setChangeData}
            />
          )}

          {deleteDialog && (
            <DeleteRegisterDialog
              header="Confirm delete"
              message="Please Confirm you wish to Delete Selected Row ?"
              data={registerData}
              visible={deleteDialog}
              onHide={onDeleteDialogHide}
              footer={reportDialogTemplate}
            />
          )}
          <style global jsx>{`
            #grid-container > .dx-datagrid > .dx-datagrid-headers {
              position: -webkit-sticky;
              position: sticky;
              background-color: #fff;
              z-index: 1;
              top: 50px;
            }

            .master-detail-caption {
              padding: 0 0 5px 10px;
              font-size: 14px;
              font-weight: bold;
            }

            .dx-scrollbar-horizontal {
              // position: -webkit-sticky;
              // position: sticky;
              top: 0;
            }

            .dx-show-clear-button .dx-icon-clear {
              top: 18px;
            }
            .dx-texteditor-buttons-container {
              height: 35px;
            }
          `}</style>
        </div>
      </div>
    </div>
  );
};

export default RegisterDatatable;
