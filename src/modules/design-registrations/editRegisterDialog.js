import { useRef } from 'react';
import { Toast } from 'primereact/toast';
import { Popup, Button, SelectBox, DateBox } from 'devextreme-react';
import ScrollView from 'devextreme-react/scroll-view';
import { DATE_FILTER_FORMAT } from 'src/constants';
import { useDefaults } from '@contexts/defaults';

const changeList = [
  {
    label: '[Select]',
    value: '',
  },
  {
    label: 'Approved Date',
    value: 1,
  },
  {
    label: 'Expiry Date',
    value: 2,
  },
  {
    label: 'Status',
    value: 3,
  },
];
const statusList = [
  {
    label: '[Select]',
    value: '',
  },
  {
    label: 'Requested',
    value: '0',
  },
  {
    label: 'Approved',
    value: '1',
  },
  {
    label: 'Pending',
    value: '2',
  },
  {
    label: 'Denied',
    value: '3',
  },
];

const updateBtnAttr = { id: 'update-btn' };
const cancelBtnAttr = { id: 'cancel-btn' };
const changeAttr = { id: 'change-option' };
const statusAttr = { id: 'status-option' };

const EditRegisterDialog = ({
  visible,
  onHide,
  registerData,
  modeData,
  onInputChange,
  onUpdateButtonClick,
  changeData,
  setChangeData,
}) => {
  const {
    defaults: { DATE_FORMAT },
  } = useDefaults();

  const toast = useRef(null);

  const createDialogFooter = (
    <>
      <Button
        type="default"
        text="Update Selected"
        icon="pi pi-check"
        onClick={onUpdateButtonClick}
        className="bg-primary mr-2"
        disabled={!changeData || (changeData == 3 && !registerData.status)}
        elementAttr={updateBtnAttr}
      />
      <Button
        type="danger"
        text="Cancel"
        icon="pi pi-times"
        className="bg-error mr-2"
        onClick={onHide}
        elementAttr={cancelBtnAttr}
      />
    </>
  );

  const renderPopup = () => {
    return (
      <>
        <Toast ref={toast} />
        <ScrollView width="100%" height="100%">
          <div className="mt-2 mb-0 pr-3">
            <div className="formgrid grid m-0">
              <div className="field col">
                <label htmlFor="change">Change</label>

                <SelectBox
                  dataSource={changeList}
                  displayExpr="label"
                  valueExpr="value"
                  value={changeData}
                  onValueChanged={(e) => setChangeData(e.value)}
                  placeholder="Select Option"
                  elementAttr={changeAttr}
                />
              </div>
            </div>
            <div className="formgrid grid m-0 mt-4">
              {changeData == 1 && (
                <div className="field col">
                  <label htmlFor="date">Approved Date</label>

                  <DateBox
                    value={registerData.approveDate}
                    name="approveDate"
                    onValueChanged={(e) =>
                      onInputChange(e.value, 'approveDate')
                    }
                    displayFormat={DATE_FILTER_FORMAT[DATE_FORMAT]}
                    useMaskBehavior={true}
                  />
                </div>
              )}
              {changeData == 2 && (
                <div className="field col">
                  <label htmlFor="date">Expiry Date</label>

                  <DateBox
                    value={registerData.expiryDate}
                    name="expiryDate"
                    onValueChanged={(e) => onInputChange(e.value, 'expiryDate')}
                    displayFormat={DATE_FILTER_FORMAT[DATE_FORMAT]}
                    useMaskBehavior={true}
                  />
                </div>
              )}
              {changeData == 3 && (
                <div className="field col">
                  <label htmlFor="status">Status</label>

                  <SelectBox
                    dataSource={statusList}
                    displayExpr="label"
                    valueExpr="value"
                    value={registerData.status}
                    onValueChanged={(e) => onInputChange(e.value, 'status')}
                    placeholder="Select Option"
                    elementAttr={statusAttr}
                  />
                </div>
              )}
            </div>
          </div>
        </ScrollView>
        <div className="p-dialog-footer popup text-right">
          {createDialogFooter}
        </div>
      </>
    );
  };

  return (
    <Popup
      width={300}
      height={300}
      showTitle={true}
      title={modeData.title}
      dragEnabled={false}
      hideOnOutsideClick={false}
      visible={visible}
      onHiding={onHide}
      shadingColor="#00000090"
      contentRender={renderPopup}
    />
  );
};

export default EditRegisterDialog;
