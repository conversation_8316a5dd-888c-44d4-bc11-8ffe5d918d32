import Popup from 'devextreme-react/popup';

const DeleteRegisterDialog = ({
  header,
  message,
  data,
  visible,
  footer,
  onHide,
}) => {
  const renderPopup = () => {
    return (
      <>
        <div className="confirmation-content flex my-2">
          <i
            className="pi pi-exclamation-triangle mx-3"
            style={{ fontSize: '3rem' }}
          />
          {data && <span className="flex align-items-center">{message}</span>}
        </div>

        <div className="p-dialog-footer popup text-right">{footer}</div>
      </>
    );
  };
  return (
    <Popup
      width={550}
      height={160}
      showTitle={true}
      title={header}
      dragEnabled={false}
      hideOnOutsideClick={true}
      visible={visible}
      onHiding={onHide}
      shadingColor="#00000090"
      contentRender={renderPopup}
    />
  );
};

export default DeleteRegisterDialog;
