import { useEffect, useState } from 'react';

import nProgress from 'nprogress';

import { useMenu } from '@contexts/menu';
import { getXreportsURL, getXreportsUserAuth } from '@services/api';
import { useDefaults } from '@contexts/defaults';
import { LoadPanel } from 'devextreme-react/load-panel';

const embedStyleOptions = {
  height: '85vh',
  border: 'none',
};
const loadPanelPosition = {
  of: '#xreports-embed',
};

const XReports = () => {
  const [embedUrl, setEmbedUrl] = useState('');
  const [loading, setLoading] = useState(true);

  const {
    user: { userId, sessionId },
  } = useMenu();
  const {
    defaults: { INSTANCE_NAME: instanceName },
  } = useDefaults();

  useEffect(() => {
    const fetchUrl = async () => {
      try {
        nProgress.start();
        const [urlResponse, userAuthResponse] = await Promise.all([
          getXreportsURL(),
          getXreportsUserAuth(),
        ]);

        const url = urlResponse.data.url;
        const userAuth = userAuthResponse.data;
        if (instanceName) {
          setEmbedUrl(
            `${url}?token=${userAuth}&userId=${userId}&sessionId=${sessionId}&instanceName=${instanceName}`
          );
        }
      } catch {
        setLoading(false);
        nProgress.done();
      } finally {
        setLoading(false);
        nProgress.done();
      }
    };
    fetchUrl();
  }, [instanceName]);

  const hideSpinner = () => {
    setLoading(false);
    nProgress.done();
  };
  return (
    <>
      <div id="xreports-embed">
        <iframe
          src={embedUrl}
          width="100%"
          style={embedStyleOptions}
          onLoad={hideSpinner}
          allowFullScreen
          allow="fullscreen"
        />
      </div>
      {loading ? (
        <LoadPanel
          position={loadPanelPosition}
          shadingColor="rgba(0,0,0,0.1)"
          showPane={true}
          visible={true}
          message="Loading X-Reports..."
        />
      ) : null}
    </>
  );
};

export default XReports;
