import React, { useCallback, useEffect, useState, useRef } from 'react';
import { getSyncProcess, postEnableSyncTable } from '@services/api';
import { Button, List, LoadPanel, Toast } from 'devextreme-react';
import ListItem from './ListItem';
import { set } from 'nprogress';

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func.apply(null, args);
    }, delay);
  };
};
const onListInitialized = (e) => {
  e.component.option('selectionByClick', false);
};
const position = { of: '#list-container' };
const enableAllAttr = { id: 'enableAll' };
const disableAllAttr = { id: 'disableAll' };
const Index = () => {
  const [data, setData] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [loading, setLoading] = useState(false);
  const toast = useRef(null);

  useEffect(() => {
    setLoading(true);
    getSyncProcess()
      .then((response) => {
        if (response.status !== 200 && response.status !== 201) {
          showToast('error', 'Failed to fetch data', response.statusText);
          return;
        }
        const enabledItems = response.data.filter((item) => item.enabled);
        setSelectedKeys(enabledItems);
        setData(response.data);
      })
      .catch((error) => {
        showToast('error', 'Error fetching data', error.message);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  const showToast = (severity, summary, detail) => {
    toast.current.instance.show({
      message: detail,
      type: severity,
      displayTime: 3000,
      closeOnClick: true,
    });
  };
  const updateData = (updatedItem) => {
    setData((prevData) =>
      prevData.map((item) => (item.id === updatedItem.id ? updatedItem : item))
    );
  };
  const onEnableAll = useCallback(async () => {
    const param = data.map(({ name, ...rest }) => ({ ...rest, enabled: 1 }));
    try {
      setLoading(true);
      const response = await postEnableSyncTable(param);
      if (response.status !== 200 && response.status !== 201) {
        showToast('error', 'Failed to enable all', response.statusText);
        return;
      }
      setData((prev) => prev.map((item) => ({ ...item, enabled: 1 })));
      setSelectedKeys(data.map((item) => item));
    } catch (error) {
      showToast('error', 'Error enabling all', error.message);
    } finally {
      setLoading(false);
    }
  }, [data]);

  const onDisableAll = useCallback(async () => {
    const param = data.map(({ name, ...rest }) => ({ ...rest, enabled: 0 }));
    try {
      setLoading(true);
      const response = await postEnableSyncTable(param);
      if (response.status !== 200 && response.status !== 201) {
        showToast('error', 'Failed to disable all', response.statusText);
        return;
      }
      setData((prev) => prev.map((item) => ({ ...item, enabled: 0 })));
      setSelectedKeys([]);
    } catch (error) {
      showToast('error', 'Error disabling all', error.message);
    } finally {
      setLoading(false);
    }
  }, [data]);

  const handleEnableItems = async (itemsToEnable) => {
    const param = itemsToEnable.map(({ name, ...rest }) => ({
      ...rest,
      enabled: 1,
    }));
    try {
      setLoading(true);
      const response = await postEnableSyncTable(param);
      if (response.status !== 200 && response.status !== 201) {
        showToast('error', 'Failed to enable items', response.statusText);
        return;
      }
      setData((prev) =>
        prev.map((item) =>
          itemsToEnable.includes(item) ? { ...item, enabled: 1 } : item
        )
      );
    } catch (error) {
      showToast('error', 'Error enabling items', error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDisableItems = async (itemsToDisable) => {
    const param = itemsToDisable.map(({ name, ...rest }) => ({
      ...rest,
      enabled: 0,
    }));
    try {
      setLoading(true);
      const response = await postEnableSyncTable(param);
      if (response.status !== 200 && response.status !== 201) {
        showToast('error', 'Failed to disable items', response.statusText);
        return;
      }
      setData((prev) =>
        prev.map((item) =>
          itemsToDisable.includes(item) ? { ...item, enabled: 0 } : item
        )
      );
    } catch (error) {
      showToast('error', 'Error disabling items', error.message);
    } finally {
      setLoading(false);
    }
  };

  const debouncedHandleEnableItems = useCallback(
    debounce(handleEnableItems, 300),
    []
  );
  const debouncedHandleDisableItems = useCallback(
    debounce(handleDisableItems, 300),
    []
  );

  const onSelectionChanged = useCallback(
    async ({ addedItems, removedItems }) => {
      if (addedItems.length > 0) {
        debouncedHandleEnableItems(addedItems);
        setSelectedKeys((prev) => [...prev, ...addedItems]);
      }
      if (removedItems.length > 0) {
        debouncedHandleDisableItems(removedItems);
        setSelectedKeys((prev) =>
          prev.filter((item) => !removedItems.includes(item))
        );
      }
    },
    [debouncedHandleEnableItems, debouncedHandleDisableItems]
  );
  const updateSyncLimit = useCallback(
    async (id, newLimit) => {
      const itemToUpdate = data.find((item) => item.id === id);

      if (!itemToUpdate || itemToUpdate.syncLimit === newLimit) return;

      const updatedItem = { ...itemToUpdate, syncLimit: newLimit };

      const param = [{ ...updatedItem }];

      try {
        setLoading(true);
        const response = await postEnableSyncTable(param);
        if (response.status !== 200 && response.status !== 201) {
          showToast(
            'error',
            'Failed to update sync limit',
            response.statusText
          );
          return;
        }

        // Update the local state with the new sync limit and preserve the current 'enabled' state
        updateData(updatedItem);

        setSelectedKeys((prev) =>
          prev.map((item) => (item.id === id ? updatedItem : item))
        );
      } catch (error) {
        showToast('error', 'Error updating sync limit', error.message);
      } finally {
        setLoading(false);
      }
    },
    [data]
  );
  const selectedItemKeys = data.filter((item) => item.enabled == 1);
  console.log(selectedItemKeys, 'selectedItemKeys');
  return (
    <div className="w-6 h-full p-5 flex flex-column ">
      <Toast ref={toast} />
      <div className="text-xl font-semibold mb-5 text-center">
        Triggers Status
      </div>
      <div className="flex justify-content-between mb-5">
        <Button
          type="default"
          className="bg-primary"
          text="Enable all"
          onClick={onEnableAll}
          disabled={selectedItemKeys.length === data.length}
          elementAttr={enableAllAttr}
        />
        <Button
          type="default"
          className="bg-primary"
          text="Disable all"
          onClick={onDisableAll}
          disabled={selectedItemKeys.length === 0}
          elementAttr={disableAllAttr}
        />
      </div>

      <div id="list-container" style={{ background: '#b0c4de' }}>
        <List
          dataSource={data}
          height="70vh"
          showSelectionControls={true}
          selectionMode="multiple"
          pageLoadMode="scrollBottom"
          displayExpr="name"
          selectedItemKeys={selectedItemKeys}
          onSelectionChanged={onSelectionChanged}
          itemRender={(data) => (
            <ListItem data={data} onUpdate={updateSyncLimit} />
          )}
          onInitialized={onListInitialized}
        />
      </div>
      <LoadPanel
        shadingColor="rgba(0,0,0,0.2)"
        position={position}
        visible={loading}
        showIndicator={true}
        shading={true}
        showPane={true}
        hideOnOutsideClick={false}
      />
    </div>
  );
};

export default Index;
