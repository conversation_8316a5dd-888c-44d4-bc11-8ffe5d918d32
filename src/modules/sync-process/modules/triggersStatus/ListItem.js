import { NumberBox } from 'devextreme-react';
import { useState, useEffect, useCallback } from 'react';

const ListItem = ({ data, onUpdate }) => {
  const { name, syncLimit, id } = data;
  const [currentValue, setCurrentValue] = useState(syncLimit);
  const [disabledInput, setDisabledInput] = useState(false);

  // Synchronize currentValue with prop changes
  useEffect(() => {
    setCurrentValue(syncLimit);
  }, [syncLimit]);

  const handleValueChange = useCallback(
    async (newLimit) => {
      if (newLimit === syncLimit) {
        return;
      }
      setDisabledInput(true);
      try {
        await onUpdate(id, newLimit);
      } catch (error) {
        console.error('Error updating sync limit:', error.message);
      } finally {
        setDisabledInput(false);
      }
    },
    [syncLimit, id, onUpdate]
  );

  const handleBlur = useCallback(() => {
    handleValueChange(currentValue);
  }, [currentValue, handleValueChange]);

  const handleInputChange = (e) => {
    setCurrentValue(e.value);
  };

  return (
    <div className="flex justify-content-between align-items-center">
      <div>{name}</div>
      <div className="flex justify-content-between align-items-center">
        Limit :
        <NumberBox
          value={currentValue}
          min={0}
          max={100000}
          showSpinButtons={true}
          width={170}
          onValueChanged={handleInputChange}
          onFocusOut={handleBlur}
          onEnterKey={handleBlur}
          disabled={disabledInput}
        />
      </div>
    </div>
  );
};

export default ListItem;
