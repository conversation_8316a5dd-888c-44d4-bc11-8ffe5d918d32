import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Button, CheckBox } from 'devextreme-react';
import { Toast } from 'primereact/toast';
import ConfirmDialog from '@modules/sync-process/components/confirmDialog';
import {
  clearProcessTable,
  clearSyncErrors,
  getSyncCrornJobStatus,
  postEnableSyncCronJob,
  retryUnprocessed,
} from '@services/api';
const checkboxAttr = { id: 'checkBox' };

const Index = () => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState({
    show: false,
    message: '',
    header: '',
    footer: null,
  });
  const toast = useRef(null);

  const showToast = (severity, summary, detail) => {
    toast.current.show({
      severity,
      summary,
      detail,
      life: 3000,
    });
  };

  useEffect(() => {
    const fetchSyncCronJobStatus = async () => {
      try {
        const response = await getSyncCrornJobStatus();
        if (response.status !== 200 && response.status !== 201) {
          showToast('error', 'Error', response.statusText);
          return;
        }
        setIsEnabled(response.data);
      } catch (error) {
        console.error('Error fetching data:', error);
        showToast('error', 'Error', 'Failed to fetch sync cron job status');
      }
    };
    fetchSyncCronJobStatus();
  }, []);

  const onCronJobChanged = useCallback(async (e) => {
    try {
      const response = await postEnableSyncCronJob(+e.value);
      if (response.status !== 200 && response.status !== 201) {
        showToast('error', 'Error', response.statusText);
        return;
      }
      setIsEnabled(e.value);
    } catch (error) {
      console.error('Error enabling items:', error);
      showToast('error', 'Error', 'Failed to change cron job status');
    }
  }, []);

  const onCloseDialog = () => setConfirmDialog({ show: false });

  const handleConfirmAction = async (action, successDetail) => {
    try {
      await action();
      showToast('success', 'Successful', successDetail);
    } catch (error) {
      showToast('error', 'Failed', 'Please try after sometime');
    } finally {
      onCloseDialog();
    }
  };

  const onConfirmClearTable = () =>
    handleConfirmAction(clearProcessTable, 'Clear Process Table');
  const onConfirmClearError = () =>
    handleConfirmAction(clearSyncErrors, 'Clear errors');
  const onConfirmRetryRequest = () =>
    handleConfirmAction(retryUnprocessed, 'Retry unprocessed request');

  const createFooter = (onConfirm) => (
    <>
      <Button
        icon="pi pi-check"
        type="danger"
        text="Yes"
        className="bg-error mr-2"
        onClick={onConfirm}
      />
      <Button
        icon="pi pi-times"
        type="default"
        text="No"
        className="bg-yellow-600 mr-2"
        onClick={onCloseDialog}
      />
    </>
  );

  const onClearProcessTable = useCallback(() => {
    setConfirmDialog({
      show: true,
      message: 'Are you sure to clear process table?',
      footer: createFooter(onConfirmClearTable),
    });
  }, []);

  const onClearErrors = useCallback(() => {
    setConfirmDialog({
      show: true,
      message: 'Are you sure to clear errors?',
      footer: createFooter(onConfirmClearError),
    });
  }, []);

  const onRetryRequest = useCallback(() => {
    setConfirmDialog({
      show: true,
      message: 'Are you sure to retry unprocessed request?',
      footer: createFooter(onConfirmRetryRequest),
    });
  }, []);

  return (
    <div className="w-6 h-full p-5 flex flex-column align-items-center border-right-1">
      <Toast ref={toast} />
      <div className="text-xl font-semibold mb-5 text-center">Sync Actions</div>
      <div className="w-6">
        <div className="w-full flex justify-content-between">
          <div>CronJob</div>
          <div>
            <CheckBox
              defaultValue={isEnabled}
              text={isEnabled ? 'Enabled' : 'Disabled'}
              onValueChanged={onCronJobChanged}
              elementAttr={checkboxAttr}
            />
          </div>
        </div>
        <div className="flex flex-column flex-start">
          <Button
            type="default"
            className="bg-primary my-2 w-auto"
            text="Clear Process Table"
            onClick={onClearProcessTable}
          />
          <Button
            type="default"
            className="bg-primary my-2"
            text="Clear Errors"
            onClick={onClearErrors}
          />
          <Button
            type="default"
            className="bg-primary my-2"
            text="Retry unprocessed request"
            onClick={onRetryRequest}
          />
        </div>
      </div>
      {confirmDialog.show && (
        <ConfirmDialog
          header="Confirm"
          onHide={onCloseDialog}
          {...confirmDialog}
        />
      )}
    </div>
  );
};

export default Index;
