import { useMenu } from '@contexts/menu';
import DeleteRecordDialog from '@modules/expenses/components/expenses/dialogs/deleteRecordDialog';
import {
  deleteMedicalLineItem,
  deletePOLineItem,
  deleteQuoteLineItem,
  getMedCustomerPrice,
  getMedicalLineItem,
  getMedicalPrinciCommRate,
  getMedLinePartNo,
  postMedicalLineItem,
  postPOLineItem,
  postQuoteLineItem,
  updateMedicalLineItem,
  updatePOLineItem,
  updateQuoteLineItem,
} from '@services/api';
import { Button, DataGrid } from 'devextreme-react';
import DxDataGrid from 'devextreme/ui/data_grid';
import {
  Column,
  Scrolling,
  Button as Button1,
  Editing,
  Lookup,
  Popup,
} from 'devextreme-react/data-grid';
import CustomStore from 'devextreme/data/custom_store';
import { Toast } from 'primereact/toast';
import { useCallback, useMemo, useRef, useState } from 'react';
import { amountFormat } from 'src/constants';
import SelectBoxItemRender from './components/SelectBoxItemRender';
import { Template } from 'devextreme-react/core/template';
import {
  calculateExtRate,
  calculateMultiplier,
  handleErrors,
  handleYoxelErrors,
  onCellPrepared,
  PartNoEditorOptions,
} from './utils';
import { getCustomValBasedOnLabel } from 'src/utils';
import { useDefaults } from '@contexts/defaults';

const onRowInserted = (e) => {
  const instance = e.component;
  instance.navigateToRow(e.data.recordId).then(() => {
    const index = instance.getRowIndexByKey(e.data.recordId);
    instance.editCell(index, 'medicalPartManufacturer');
  });
};
const generateColumn = (data, headers) => [
  { title: headers[0], value: data.princiName },
  { title: headers[1], value: data.princiPartNo },
  { title: headers[2], value: data.prodFamily },
  { title: headers[3], value: data.prodLine },
  { title: headers[4], value: data.prodDesc },
  { title: headers[5], value: `$ ${data.prodStdPrice}` },
  { title: headers[6], value: `$ ${data.prodCommRate}` },
];
const MedicalLineItem = ({
  detailData,
  masterData,
  lineItemRef,
  refreshGrid,
}) => {
  const { user } = useMenu();
  const toast = useRef(null);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [medData, setMedData] = useState([]);
  const [editingMode, setEditingMode] = useState('cell');
  const amountEditorOptions = { format: amountFormat };
  const gridAttr = useMemo(() => {
    return {
      id: 'lineItemGrid' + detailData.recordId,
    };
  }, [detailData.recordId]);

  const medicalDetailGridId = 'medicalDetailGrid';
  const medicalDetailGrid = document.getElementById(medicalDetailGridId);
  const medicalDetailInstance = DxDataGrid?.getInstance(medicalDetailGrid);

  const medicalDetailData = medicalDetailInstance
    .getDataSource()
    .items()
    .find((d) => d.recordId == detailData.recordId);

  const onInitNewLineItemRow = useCallback(
    (e) => {
      const medicalLineItemData = {
        medicalDetailId: medicalDetailData?.recordId,
        medicalId: medicalDetailData?.medicalId,
        medicalQuantityRequired: 1,
        medicalMultiplier: 1,
        medicalUom: '',
        insertedUser: user.userId,
        updatedUser: 0,
      };
      e.data = medicalLineItemData;
    },
    [medicalDetailData?.recordId]
  );

  const {
    defaults: { CUSTOM_LABELS, YOXEL_TOKEN },
  } = useDefaults();

  const [customPrinci, customPartNo, customProdFam, customProdLine] =
    getCustomValBasedOnLabel(CUSTOM_LABELS, [
      'IDS_PRINCI',
      'IDS_PART_NUM',
      'IDS_PROD_FAMILY',
      'IDS_PROD_LINE',
    ]);

  const onEditBtnClick = useCallback((e) => {
    e.component.option('editing.popup.toolbarItems', [
      {
        toolbar: 'bottom',
        location: 'center',
        widget: 'dxButton',
        options: {
          text: 'Save',
          type: 'success',
          onClick: function () {
            e.component.saveEditData();
          },
        },
      },
      {
        toolbar: 'bottom',
        location: 'center',
        widget: 'dxButton',
        options: {
          text: 'Cancel',
          type: 'danger',
          onClick: function () {
            e.component.cancelEditData();
          },
        },
      },
    ]);
    // State Doesnt effect immediately so added below line
    e.component.option('editing.mode', 'popup');
    setEditingMode('popup');
    e.component.editRow(e.row.rowIndex);
  }, []);
  const onEditCanceled = useCallback(() => setEditingMode('cell'), []);

  const PartNo = useMemo(
    () =>
      new CustomStore({
        key: 'princiPartNo',
        loadMode: 'raw',
        load: () => {
          return getMedLinePartNo(medicalDetailData.medicalPrincipal)
            .then(handleErrors)
            .catch(() => []);
        },
      }),
    []
  );

  const medicalLineItem = useMemo(
    () =>
      new CustomStore({
        key: 'recordId',
        load: () =>
          getMedicalLineItem(medicalDetailData?.recordId)
            .then(handleErrors)
            .catch(() => []),
        update: async (key, values) => {
          const params = {
            recordId: key,
            updatedUser: user.userId,
            ...values,
          };

          return updateMedicalLineItem(params)
            .then(async (response) => {
              if (response.status !== 200 && response.status !== 201) {
                throw Error(response.statusText);
              }

              const comissionRate = await getMedicalPrinciCommRate(
                medicalDetailData?.medicalPrincipal
              );

              const quoteLineItemParam = {
                recordId: response.data.recordId,
                id: response.data.medicalQuoteDetailId,
                'order-qty': +response.data.medicalQuantityRequired,
                description: response.data.medicalPartDescription,
                'unit-price': +response.data.medicalUnitPrice,
                'cust-price-flag': response.data.medicalCustomerPriceFlag,
                'ext-price': +response.data.medicalExtendedPrice,
                uom: response.data.medicalUom,
                multiplier: +response.data.medicalMultiplier,
                'std-rate': +response.data.medicalStandardRate,
                'part-num': response.data.medicalPartManufacturer,
                'principal-id': 0,
                'uom-units': 1,
                medOwner: 1,
              };

              const poLineItemParam = {
                recordId: response.data.recordId,
                id: response.data.medicalPurchaseOrderDetailId,
                'part-num': response.data.medicalPartManufacturer,
                description: response.data.medicalPartDescription,
                'order-qty': +response.data.medicalQuantityRequired,
                'std-rate': +response.data.medicalStandardRate,
                multiplier: +response.data.medicalMultiplier,
                'unit-price': +response.data.medicalUnitPrice,
                'total-price': +response.data.medicalExtendedPrice,
                uom: response.data.medicalUom,
                'plan-qty': +response.data.medicalQuantityRequired,
                'uom-units': 1,
                'comm-rate': +comissionRate.data,
                medOwner: 1,
              };

              const poLineItemPostParam = {
                ...poLineItemParam,
                'shipping-qty': 0,
                'shipping-status-name': 0,
                'shipping-date': null,
                'invc-date': null,
              };

              if (
                medicalDetailData.medicalQuotationId != null &&
                quoteLineItemParam['part-num'] !== null
              ) {
                if (quoteLineItemParam.id != null) {
                  updateQuoteLineItem(
                    medicalDetailData.medicalQuotationId,
                    quoteLineItemParam,
                    YOXEL_TOKEN
                  )
                    .then(handleYoxelErrors)
                    .catch((e) => {
                      console.log(e);
                    });
                } else {
                  const quoteLineItem = await postQuoteLineItem(
                    medicalDetailData.medicalQuotationId,
                    quoteLineItemParam,
                    YOXEL_TOKEN
                  )
                    .then(handleYoxelErrors)
                    .catch((e) => {
                      console.log(e);
                    });

                  if (quoteLineItem) {
                    const lineParams = {
                      recordId: quoteLineItemParam.recordId,
                      medicalQuoteDetailId: quoteLineItem.id,
                    };
                    updateMedicalLineItem(lineParams)
                      .then(handleErrors)
                      .catch(() => {
                        throw Error('Network Error');
                      });
                  }
                }
              }

              if (
                medicalDetailData.medcialPurchaseOrderId != null &&
                poLineItemParam['part-num'] !== null
              ) {
                if (poLineItemParam.id == null) {
                  const POLineItem = await postPOLineItem(
                    medicalDetailData.medcialPurchaseOrderId,
                    poLineItemPostParam,
                    YOXEL_TOKEN
                  )
                    .then(handleYoxelErrors)
                    .catch((e) => {
                      console.log(e);
                    });

                  if (POLineItem) {
                    const lineParams = {
                      recordId: poLineItemParam.recordId,
                      medicalPurchaseOrderDetailId: POLineItem.id,
                    };
                    updateMedicalLineItem(lineParams)
                      .then(handleErrors)
                      .catch(() => {
                        throw Error('Network Error');
                      });
                  }
                } else {
                  updatePOLineItem(
                    medicalDetailData.medcialPurchaseOrderId,
                    poLineItemParam,
                    YOXEL_TOKEN
                  )
                    .then(handleYoxelErrors)
                    .catch((e) => {
                      console.log(e);
                    });
                }
              }
              return response.data;
            })

            .catch((e) => {
              console.log(e);
            });
        },
        remove: async (key) => {
          return deleteMedicalLineItem(key)
            .then((response) => {
              if (response.status !== 200 && response.status !== 201) {
                throw Error(response.statusText);
              }
              const lineItemParam = response.data;
              if (medicalDetailData.medicalQuotationId != null) {
                if (lineItemParam.medicalQuoteDetailId != null) {
                  deleteQuoteLineItem(
                    medicalDetailData.medicalQuotationId,
                    lineItemParam.medicalQuoteDetailId,
                    1,
                    YOXEL_TOKEN
                  )
                    .then(handleYoxelErrors)
                    .catch((e) => {
                      console.log(e);
                    });
                }
              }
              if (medicalDetailData.medcialPurchaseOrderId != null) {
                if (lineItemParam.medicalPurchaseOrderDetailId != null) {
                  deletePOLineItem(
                    medicalDetailData.medcialPurchaseOrderId,
                    lineItemParam.medicalPurchaseOrderDetailId,
                    1,
                    YOXEL_TOKEN
                  )
                    .then(handleYoxelErrors)
                    .catch((e) => {
                      console.log(e);
                    });
                }
              }
            })
            .catch(() => {
              throw Error('Network Error');
            });
        },
        insert: async (values) => {
          return postMedicalLineItem(values)
            .then(handleErrors)
            .catch(() => {
              throw Error('Network Error');
            });
        },
      }),
    []
  );

  const tooltipColumn = [
    customPrinci,
    customPartNo,
    customProdFam,
    customProdLine,
    'Description',
    'Std. Price',
    'Comm. Rate',
  ];

  const PartNoSelectBox = (data) => (
    <SelectBoxItemRender
      columnData={generateColumn(data, tooltipColumn)}
      id={`partNo${data.recId}`}
      title="Product Information"
      displayValue={data.princiPartNo}
    />
  );

  const setMedPartMnfCellValue = useCallback(
    async (newData, value, currentRowData) => {
      const uomUnits = 1;
      const { data: customerPrice } = await getMedCustomerPrice({
        princiId: medicalDetailData.medicalPrincipal,
        partNumber: value.princiPartNo,
        customerId: masterData.medicalSpecifier,
        distributorId: masterData.medicalFacility ?? 0,
        qty: currentRowData.medicalQuantityRequired,
      });
      const prodStdPrice = value.prodStdPrice ? +value.prodStdPrice : 0;

      newData.medicalMultiplier = calculateMultiplier(
        customerPrice,
        prodStdPrice
      );

      newData.medicalUnitPrice =
        customerPrice === 0
          ? +(prodStdPrice * newData.medicalMultiplier).toFixed(6)
          : +customerPrice.toFixed(6);
      newData.medicalCustomerPriceFlag = customerPrice === 0 ? 0 : 1;
      newData.medicalUomUnits = uomUnits;
      newData.medicalPartManufacturer = value.princiPartNo;
      newData.medicalPartDescription = value.prodDesc;
      newData.medicalStandardRate = prodStdPrice;
      newData.medicalExtendedPrice = calculateExtRate(
        newData.medicalUnitPrice,
        currentRowData.medicalQuantityRequired,
        uomUnits
      );
    },
    []
  );

  const setMedLineQntyReqCellValue = useCallback(
    async (newData, value, currentRowData) => {
      newData.medicalQuantityRequired = +value.toFixed(3);
      const uomUnits = 1;
      const { data: customerPrice } = await getMedCustomerPrice({
        princiId: medicalDetailData.medicalPrincipal,
        partNumber: currentRowData.medicalPartManufacturer,
        customerId: masterData.medicalSpecifier,
        distributorId: masterData.medicalFacility ?? 0,
        qty: value,
      });
      const partNoData = await PartNo.load();
      const standardPrice = partNoData.find(
        (part) => part.princiPartNo == currentRowData.medicalPartManufacturer
      )?.prodStdPrice;

      const prodStdPrice =
        standardPrice && +standardPrice !== 0
          ? +standardPrice
          : +currentRowData.medicalStandardRate;

      newData.medicalMultiplier = calculateMultiplier(
        customerPrice,
        prodStdPrice
      );

      newData.medicalUnitPrice =
        customerPrice === 0
          ? +(prodStdPrice * newData.medicalMultiplier).toFixed(6)
          : +customerPrice.toFixed(6);
      newData.medicalCustomerPriceFlag = customerPrice === 0 ? 0 : 1;
      newData.medicalUomUnits = uomUnits;
      newData.medicalStandardRate = prodStdPrice;
      newData.medicalExtendedPrice = calculateExtRate(
        newData.medicalUnitPrice,
        value,
        uomUnits
      );
    },
    []
  );

  const setMedLineStdRateCellValue = useCallback(
    async (newData, value, currentRowData) => {
      newData.medicalStandardRate = +value;
      newData.medicalUnitPrice = +(
        newData.medicalStandardRate * currentRowData.medicalMultiplier
      ).toFixed(6);
      newData.medicalExtendedPrice = calculateExtRate(
        newData.medicalUnitPrice,
        currentRowData.medicalQuantityRequired,
        1
      );
    },
    []
  );
  const setMedLineMultiCellValue = useCallback(
    async (newData, value, currentRowData) => {
      newData.medicalMultiplier = +value;
      newData.medicalUnitPrice = +(
        currentRowData.medicalStandardRate * newData.medicalMultiplier
      ).toFixed(6);
      newData.medicalExtendedPrice = calculateExtRate(
        newData.medicalUnitPrice,
        currentRowData.medicalQuantityRequired,
        1
      );
    },
    []
  );

  const setMedLineUnitPriceCellValue = useCallback(
    async (newData, value, currentRowData) => {
      newData.medicalUnitPrice = +value;
      newData.medicalMultiplier = calculateMultiplier(
        newData.medicalUnitPrice,
        currentRowData.medicalStandardRate
      );
      newData.medicalExtendedPrice = calculateExtRate(
        value,
        currentRowData.medicalQuantityRequired,
        1
      );
    },
    []
  );

  const confirmDelete = useCallback((e) => {
    if (e.row) {
      setMedData(e.row.data);
      setDeleteDialog(true);
    }
  }, []);

  const deleteRecords = () =>
    medicalLineItem.remove(medData.recordId).then(() => {
      setDeleteDialog(false);
      refreshGrid();
      toast.current.show({
        severity: 'success',
        summary: 'Successful',
        detail: `Line item deleted successfully`,
        life: 3000,
      });
    });

  const onDeleteDialogHide = useCallback(() => {
    setDeleteDialog(false);
  }, []);

  const deleteFooterDialog = (
    <>
      <Button
        icon="pi pi-check"
        type="danger"
        text="Delete"
        className="bg-error mr-2"
        onClick={deleteRecords}
      />
      <Button
        icon="pi pi-times"
        type="default"
        text="Cancel"
        className="bg-yellow-600 mr-2"
        onClick={() => {
          setDeleteDialog(false);
        }}
      />
    </>
  );

  const onEditorPrepare = (e) => {
    if (e.parentType === 'dataRow') {
      if (
        e.dataField === 'medicalUnitPrice' ||
        e.dataField === 'medicalQuantityRequired'
      ) {
        // your column to set character limit.
        e.editorOptions.min = 1;
        e.editorOptions.max = 999999999.9999;
        // Disable Amount Column Edit when part number not selected
        if (e.row.data.medicalPartManufacturer === null) {
          e.editorOptions.disabled = true;
        }
      }
      if (e.dataField === 'medicalUnitPrice')
        if (e.row.data.medicalCustomerPriceFlag == 0)
          e.editorOptions.disabled = true;
        else e.editorOptions.disabled = false;

      if (
        e.dataField === 'medicalStandardRate' ||
        e.dataField === 'medicalMultiplier'
      )
        if (e.row.data.medicalCustomerPriceFlag !== 0)
          e.editorOptions.disabled = true;
        else e.editorOptions.disabled = false;
    }
  };

  return (
    <>
      <Toast ref={toast} />
      <DataGrid
        className="expanded-table"
        showColumnHeaders={true}
        dataSource={medicalLineItem}
        key="recordId"
        ref={lineItemRef}
        noDataText={`No ${customPrinci} line item found`}
        onCellPrepared={onCellPrepared}
        onEditorPreparing={onEditorPrepare}
        onRowUpdated={refreshGrid}
        elementAttr={gridAttr}
        onInitNewRow={onInitNewLineItemRow}
        onRowInserted={onRowInserted}
        id="medicalLineItemGrid"
      >
        <Scrolling showScrollbar={true} />
        <Editing
          mode={editingMode}
          useIcons={true}
          allowUpdating={true}
          allowDeleting={false}
          selectTextOnEditStart={true}
        >
          <Popup
            title="Edit Line Items"
            showTitle={true}
            width="60%"
            height="auto"
            onHiding={onEditCanceled}
            dragEnabled={false}
            showCloseButton={true}
          />
        </Editing>
        <Column type="buttons">
          <Button1 icon="edit" onClick={onEditBtnClick} />
          <Button1 icon="trash" onClick={confirmDelete} />
        </Column>
        <Column
          dataField="medicalPartManufacturer"
          caption="Part No"
          editorOptions={PartNoEditorOptions}
          setCellValue={setMedPartMnfCellValue}
          cssClass="dataGrid-cell"
        >
          <Lookup dataSource={PartNo} displayExpr="princiPartNo" />
          <Template name="SelectBoxItemRender" render={PartNoSelectBox} />
        </Column>
        <Column
          dataField="medicalPartDescription"
          caption="Description"
          cssClass="dataGrid-cell"
        />

        <Column
          dataField="medicalQuantityRequired"
          caption="Required Quantity"
          dataType="number"
          setCellValue={setMedLineQntyReqCellValue}
        />
        <Column
          dataField="medicalStandardRate"
          caption="List Price"
          format={amountFormat}
          alignment="right"
          dataType="number"
          editorOptions={amountEditorOptions}
          setCellValue={setMedLineStdRateCellValue}
        />
        <Column
          dataField="medicalMultiplier"
          caption="Multiplier"
          dataType="number"
          setCellValue={setMedLineMultiCellValue}
        />
        <Column
          dataField="medicalUnitPrice"
          caption="Unit Price"
          format={amountFormat}
          alignment="right"
          dataType="number"
          setCellValue={setMedLineUnitPriceCellValue}
          editorOptions={amountEditorOptions}
        />
        <Column
          dataField="medicalExtendedPrice"
          caption="Extended Price"
          format={amountFormat}
          alignment="right"
          dataType="number"
          allowEditing={false}
          editorOptions={amountEditorOptions}
        />
      </DataGrid>
      {deleteDialog && (
        <DeleteRecordDialog
          header="Confirm delete"
          message={`Please confirm you wish to delete the ${customPrinci} line item ?`}
          visible={deleteDialog}
          onHide={onDeleteDialogHide}
          footer={deleteFooterDialog}
        />
      )}
    </>
  );
};

export default MedicalLineItem;
