import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import DxDataGrid, {
  Column,
  MasterDetail,
  Button as ButtonGrid,
  FilterRow,
  Editing,
  Item,
  Toolbar,
  Lookup,
  Scrolling,
  Texts,
} from 'devextreme-react/data-grid';
import { Button, Popup, Template } from 'devextreme-react';
import DataGrid from 'devextreme/ui/data_grid';
import CustomStore from 'devextreme/data/custom_store';
import notify from 'devextreme/ui/notify';

import { Toast } from 'primereact/toast';
import { useMenu } from '@contexts/menu';
import DeleteRecordDialog from '@modules/expenses/components/expenses/dialogs/deleteRecordDialog';
import {
  getMedicalReport,
  postMedicalReport,
  updateMedicalReport,
  deleteMedicalReport,
  postMedicalDetail,
  postMedicalCalendar,
  updateMedicalCalendar,
  getValidateCalendar,
  deleteMedicalCalendar,
} from '@services/api';
import {
  amountFormat,
  DATE_FILTER_FORMAT,
  TIME_FILTER_FORMAT,
  medicalStatusArray,
} from 'src/constants';
import { useDefaults } from '@contexts/defaults';
import MedicalDetail from './medicalDetail';
import {
  addMedicalDateTimeByTZ,
  convertDateFormat,
  getCustomValBasedOnLabel,
  getDateTimeByTimeZone,
  getTodayDatePlusN,
} from 'src/utils';
import {
  doctorColumns,
  facilities,
  facilityColumns,
  handleErrors,
  MedCaseNoEditorOptions,
  MedDateEditorOptions,
  MedDescEditorOptions,
  MedDoctorEditorOptions,
  MedFacilityEditorOptions,
  onCellPrepared,
  onMedicalGridEditorPreparing,
  onRowExpanding,
  personCompanies,
  salesPersons,
  setScheduledCellValue,
} from './utils';
import SelectBoxItemRender from './components/SelectBoxItemRender';
import dayjs from 'dayjs';
import MedicalScheduler from './components/MedicalScheduler';

const calculateCellValue = (e, value) => {
  return e[value] == 0 ? null : e[value];
};

let dataGrid;
const onInitialized = (e) => {
  dataGrid = e.component;
};

const FacilitySelectBox = (data) => (
  <SelectBoxItemRender
    columnData={facilityColumns(data)}
    id={`facility${data?.recId}`}
    title="Other Information"
    displayValue={data?.compName}
  />
);
const DoctorSelectBox = (data) => (
  <SelectBoxItemRender
    columnData={doctorColumns(data)}
    id={`doctor${data?.id}`}
    title="Other Information"
    displayValue={data?.name}
  />
);

// Add new Medical record on "Add New" Button Clicked
const addNewRow = () => {
  const masterGrid = document.getElementById('medicalGrid');
  const instance = DataGrid.getInstance(masterGrid);
  instance.saveEditData().then(() => {
    instance.addRow();
    instance.saveEditData();
  });
};

const Medical = () => {
  const { user } = useMenu();
  const medHeaderRef = useRef(null);
  const medDetailRef = useRef(null);
  const showDateRef = useRef(null);
  const schedulerRef = useRef(null);
  const toast = useRef(null);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [medData, setMedData] = useState([]);
  const {
    defaults: { DATE_FORMAT, TIME_FORMAT, LOCAL_TIME_ZONE, CUSTOM_LABELS },
  } = useDefaults();

  const handleMedicalHeaderResponse = (response) => {
    if (response.status !== 200 && response.status !== 201) {
      throw new Error(response.statusText);
    }
    return Promise.all(
      response.data.map(async (medical) => {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        const medSchDate = await getDateTimeByTimeZone(
          medical.medicalScheduled,
          LOCAL_TIME_ZONE
        );
        const medSchToDate = await getDateTimeByTimeZone(
          medical.medicalScheduledTo,
          LOCAL_TIME_ZONE
        );
        return {
          ...medical,
          medicalScheduled: medSchDate,
          medicalScheduledTo: medSchToDate,
        };
      })
    );
  };

  const [customPrinci] = getCustomValBasedOnLabel(CUSTOM_LABELS, [
    'IDS_PRINCI',
  ]);
  const onRowInserted = useCallback((e) => {
    toast.current.show({
      severity: 'success',
      summary: 'Successful',
      detail: `New Medical record created successfully`,
      life: 3000,
    });
    const instance = e.component;
    instance.navigateToRow(e.data.medicalId).then(() => {
      const index = instance.getRowIndexByKey(e.data.medicalId);
      instance.editCell(index, 'medicalDescription');
    });
  }, []);
  const onRowRemoved = useCallback(() => {
    toast.current.show({
      severity: 'success',
      summary: 'Successful',
      detail: `Medical record deleted successfully`,
      life: 3000,
    });
  }, []);
  const onRowUpdated = useCallback((e) => {
    if (e.data.recordId)
      toast.current.show({
        severity: 'success',
        summary: 'Successful',
        detail: `Medical record updated successfully`,
        life: 3000,
      });
  }, []);
  const medicalFollowUpFormat = DATE_FILTER_FORMAT[DATE_FORMAT];
  const MedSchedulerFormat = `${medicalFollowUpFormat} ${TIME_FILTER_FORMAT[TIME_FORMAT]}`;

  const MedicalDetailGrid = ({ data }) => {
    return (
      <MedicalDetail
        masterData={data.data}
        medDetailRef={medDetailRef}
        medHeaderRef={medHeaderRef}
        customPrinci={customPrinci}
      />
    );
  };

  const addNewMedicalDetail = useCallback((data) => {
    const medicalDetailData = {
      medicalId: data.row.key,
      medicalPrincipal: 0,
      medicalStage: 0,
      medicalValue: 0,
      medicalEmailFlag: 0,
      medicalFollowUp: convertDateFormat(getTodayDatePlusN(0), 'YYYY-MM-DD'),
      medicalSignedId: 0,
      medicalSignedName: '',
      medicalSignedImg: '',
      medicalSignedTime: new Date(),
      insertedUser: user.userId,
      updatedUser: 0,
    };
    postMedicalDetail(medicalDetailData)
      .then((response) => {
        if (response.status === 201) {
          toast.current.show({
            severity: 'success',
            summary: 'Successful',
            detail: `${customPrinci} detail created successfully`,
            life: 3000,
          });
        }
        if (!medHeaderRef.current.instance.isRowExpanded(data.row.key)) {
          medHeaderRef.current.instance.expandRow(data.row.key);
        }
        medHeaderRef.current.instance.refresh(true);
        // refresh medHeaderRef grid and focus on medDetailRef on load success
        setTimeout(
          () =>
            medDetailRef.current.instance.refresh().then(() => {
              medDetailRef.current.instance.editCell(0, 'medicalPrincipal');
            }),
          500
        );
      })
      .catch((error) => console.log('ooops :(', error));
  }, []);

  const initNewRow = useCallback(
    (e) => {
      // While adding new row,
      // FollowUp should be tomorrows date and Scheduled Date are todays date and time
      const medicalData = {
        medicalOwner: user.userId,
        medicalScheduled: addMedicalDateTimeByTZ(
          1,
          '00:00:00',
          'YYYY-MM-DD HH:mm:ss'
        ),
        medicalScheduledTo: addMedicalDateTimeByTZ(
          1,
          '00:00:00',
          'YYYY-MM-DD HH:mm:ss'
        ),
        medicalFollowUp: convertDateFormat(getTodayDatePlusN(0), 'YYYY-MM-DD'),
        medicalValue: 0,
        insertedUser: user.userId,
        updatedUser: 0,
      };
      e.data = medicalData;
    },
    [user.userId, LOCAL_TIME_ZONE]
  );

  const medicalReport = useMemo(
    () =>
      new CustomStore({
        key: 'medicalId',
        load: () =>
          getMedicalReport()
            .then(handleMedicalHeaderResponse)
            .catch(() => []),
        update: async (key, values) => {
          const params = {
            medicalId: key,
            updatedUser: user.userId,
            ...values,
          };
          if (
            values.hasOwnProperty('medicalOwner') &&
            values.medicalOwner !== null
          ) {
            const medicalScheduled = medHeaderRef.current.instance.cellValue(
              medHeaderRef.current.instance.getRowIndexByKey(key),
              'medicalScheduled'
            );
            const medicalScheduledTo = medHeaderRef.current.instance.cellValue(
              medHeaderRef.current.instance.getRowIndexByKey(key),
              'medicalScheduledTo'
            );
            const calendarParam = {
              medicalOwner: params.medicalOwner,
              medicalScheduled: dayjs(medicalScheduled)
                .add(5, 'hour')
                .format('YYYY-MM-DD HH:mm:ss'),
              medicalScheduledTo: dayjs(medicalScheduledTo)
                .add(5, 'hour')
                .format('YYYY-MM-DD HH:mm:ss'),
            };
            if (
              new Date(calendarParam.medicalScheduled).getTime() !==
              new Date(calendarParam.medicalScheduledTo).getTime()
            ) {
              const response = await getValidateCalendar(calendarParam);

              if (response.data == 1) {
                toast.current.show({
                  severity: 'error',
                  summary: 'Error',
                  detail: `The event for this user within this time frame has already been scheduled.`,
                  life: 10000,
                });
                return false;
              } else {
                const medicalRowData =
                  await medHeaderRef.current.instance.byKey(key);
                const startDateTime = dayjs(
                  medicalRowData.medicalScheduled
                ).format('YYYY-MM-DD HH:mm:ss');
                const endDateTime = dayjs(
                  medicalRowData.medicalScheduledTo
                ).format('YYYY-MM-DD HH:mm:ss');
                createCalendarEvent(medicalRowData, startDateTime, endDateTime);
              }
            }
          }
          return updateMedicalReport(params)
            .then(handleErrors)
            .catch(() => {
              throw Error('Network Error');
            });
        },
        remove: async (key) => {
          return deleteMedicalReport(key)
            .then((response) => {
              const result = handleErrors(response);
              if (result.medicalEventId)
                deleteMedicalCalendar(result.medicalEventId);
            })
            .catch(() => {
              throw Error('Network Error');
            });
        },
        insert: async (values) => {
          return postMedicalReport(values)
            .then(handleErrors)
            .catch(() => {
              throw Error('Network Error');
            });
        },
      }),
    [LOCAL_TIME_ZONE]
  );

  const confirmDelete = useCallback((e) => {
    setMedData(e.row.data);
    setDeleteDialog(true);
  }, []);

  const deleteRecords = useCallback(async () => {
    try {
      const masterGrid = document.getElementById('medicalGrid');
      const instance = DataGrid.getInstance(masterGrid);
      const rowIndex = instance.getRowIndexByKey(medData.medicalId);
      instance.deleteRow(rowIndex);
      setDeleteDialog(false);
    } catch (e) {
      console.log(e);
    }
  }, [medData.medicalId]);

  const onDeleteDialogHide = useCallback(() => {
    setDeleteDialog(false);
  }, []);

  const reportDialogTemplate = (
    <>
      <Button
        icon="pi pi-check"
        type="danger"
        text="Delete"
        className="bg-error mr-2"
        onClick={deleteRecords}
      />
      <Button
        icon="pi pi-times"
        type="default"
        text="Cancel"
        className="bg-yellow-600 mr-2"
        onClick={() => {
          setDeleteDialog(false);
        }}
      />
    </>
  );

  const [showDatePopup, setShowDatePopup] = useState({
    show: false,
    props: '',
  });

  const [medicalSource, setMedicalSource] = useState([]);

  useEffect(() => {
    const selectedDate = dayjs(showDatePopup.props.value).format(
      'YYYY-MM-DD HH:mm:ss'
    );
    const customStore = new CustomStore({
      load: async () =>
        getMedicalReport()
          .then((medicalData) => {
            setMedicalSource(
              medicalData.data
                .filter(
                  (data) =>
                    data.medicalOwner == showDatePopup.props.data.medicalOwner
                )
                .map((item) => {
                  const startDate = item.medicalScheduled;
                  const endDate = item.medicalScheduledTo;
                  const newDate = getDateTimeByTimeZone(
                    startDate,
                    LOCAL_TIME_ZONE
                  );
                  return {
                    text: item.medicalDescription,
                    startDate: new Date(startDate),
                    endDate: new Date(endDate),
                    disabled: newDate !== selectedDate,
                  };
                })
            );
          })
          .catch(() => []),
    });

    customStore.load().then(() => {
      setTimeout(() => schedulerRef.current?.instance.repaint(), 250);
    });
  }, [showDatePopup]);

  const closeDatePopup = () =>
    setShowDatePopup({ ...showDatePopup, show: false, props: '' });

  const dateTimeEditorRender = useCallback((e) => {
    if (e.column.name === 'medicalScheduled' && e.rowType === 'data') {
      setShowDatePopup({ show: true, props: e });
    }
  }, []);

  const createCalendarEvent = async (medicalRowData, startDate, endDate) => {
    const facility = facilities
      .load()
      .resolveArgs[0].find(
        (fac) => fac.recId == medicalRowData.medicalFacility
      )?.compName;
    const doctor = personCompanies
      .load()
      .resolveArgs[0].find(
        (person) => person.id == medicalRowData.medicalSpecifier
      )?.name;
    const params = {
      id: medicalRowData.medicalEventId,
      userId: medicalRowData.medicalOwner,
      medicalScheduled: startDate,
      medicalScheduledTo: endDate,
      caseName: `${medicalRowData.medicalDescription} | ${facility ?? ''} | ${
        doctor ?? ''
      } | ${medicalRowData.medicalCaseNumber ?? ''}`,
      description: `Case Name:${
        medicalRowData.medicalDescription
      }\n Case Number:${medicalRowData.medicalCaseNumber ?? ''}\n Doctor:${
        doctor ?? ''
      }\n Facility:${facility ?? ''}`,
    };
    if (medicalRowData.medicalEventId == null) {
      await postMedicalCalendar(params).then(({ data }) => {
        if (data) {
          medHeaderRef.current?.instance.cellValue(
            medHeaderRef.current.instance.getRowIndexByKey(
              medicalRowData.medicalId
            ),
            'medicalEventId',
            data
          );
          medHeaderRef.current?.instance.saveEditData().then(() => {
            medHeaderRef.current.instance.refresh(true);
          });
        }
      });
    } else updateMedicalCalendar(params);
  };

  const schedulerRender = () => {
    const propsData = showDatePopup.props;
    const onValueChanged = (e) => {
      const notifyDisableDate = () => {
        notify(
          'Cannot create or move an appointment/event to disabled time regions.',
          'warning',
          1000
        );
      };

      let startDate,
        endDate,
        startDateVal,
        endDateVal,
        startDateNew,
        endDateNew,
        startDateTime,
        endDateTime;
      if (e.cellData) {
        startDateVal = e.component.option('selectedCellData')[0].startDate;
        endDateVal =
          e.component.option('selectedCellData')[
            e.component.option('selectedCellData').length - 1
          ].endDate;
        startDate = dayjs(startDateVal)
          // .add(-5.5, 'hour')
          .format('YYYY-MM-DD HH:mm:ss');
        endDate = dayjs(endDateVal)
          // .add(-5.5, 'hour')
          .format('YYYY-MM-DD HH:mm:ss');
        startDateNew = new Date(startDateVal.getTime() + 0 * 60 * 60 * 1000);
        endDateNew = new Date(endDateVal.getTime() + 0 * 60 * 60 * 1000);
        startDateTime = dayjs(startDateVal).format('YYYY-MM-DD HH:mm:ss');
        endDateTime = dayjs(endDateVal).format('YYYY-MM-DD HH:mm:ss');
      } else {
        startDate = getDateTimeByTimeZone(e.newData.startDate, LOCAL_TIME_ZONE);
        endDate = getDateTimeByTimeZone(e.newData.endDate, LOCAL_TIME_ZONE);
        startDateNew = new Date(startDate);
        endDateNew = new Date(endDate);
        startDateTime = dayjs(startDateNew)
          .add(0, 'hour')
          .format('YYYY-MM-DD HH:mm:ss');
        endDateTime = dayjs(endDateNew)
          .add(0, 'hour')
          .format('YYYY-MM-DD HH:mm:ss');
      }

      const soureData = medicalSource.filter((item) => item.disabled);

      for (const exAppointment of soureData) {
        let newStartTime, newEndTime;
        const exStartDate = new Date(
          getDateTimeByTimeZone(exAppointment.startDate, LOCAL_TIME_ZONE)
        );
        const exEndDate = new Date(
          getDateTimeByTimeZone(exAppointment.endDate, LOCAL_TIME_ZONE)
        );
        if (startDateNew.getTime() == exEndDate.getTime()) {
          newStartTime = startDateNew.getTime() + 0.5 * 60 * 60 * 1000;
          newEndTime = endDateNew.getTime() - 0 * 60 * 60 * 1000;
        } else {
          newStartTime = startDateNew.getTime() + 0 * 60 * 60 * 1000;
          newEndTime = endDateNew.getTime() - 0.5 * 60 * 60 * 1000;
        }
        if (
          (e.newData?.startDate.getTime() != e.oldData?.startDate.getTime() &&
            newStartTime >= exStartDate.getTime() &&
            newStartTime <= exEndDate.getTime()) ||
          (newEndTime >= exStartDate.getTime() &&
            newEndTime <= exEndDate.getTime()) ||
          (newEndTime > exEndDate.getTime() &&
            newStartTime < exStartDate.getTime()) ||
          newEndTime - newStartTime > 18000000
        ) {
          e.cancel = true;
          notifyDisableDate();
          return;
        }
      }

      medHeaderRef.current?.instance.editCell(
        propsData.rowIndex,
        propsData.columnIndex + 1
      );
      medHeaderRef.current?.instance.cellValue(
        propsData.rowIndex,
        'medicalScheduled',
        startDate
      );
      medHeaderRef.current?.instance.cellValue(
        propsData.rowIndex,
        'medicalScheduledTo',
        endDate
      );
      medHeaderRef.current?.instance.saveEditData().then(() => {
        const medicalRowData = propsData.row.data;
        createCalendarEvent(medicalRowData, startDateTime, endDateTime);
      });

      setMedicalSource((prevData) =>
        prevData.map((item) =>
          !item.disabled
            ? {
                ...item,
                startDate: startDateNew,
                endDate: endDateNew,
              }
            : item
        )
      );

      closeDatePopup();
    };

    const currentDate = dayjs(propsData.value)
      // .add(-5.5, 'hour')
      .format('YYYY-MM-DD HH:mm:ss');

    return (
      <MedicalScheduler
        schedulerRef={schedulerRef}
        medicalSource={medicalSource}
        currentDate={new Date(currentDate)}
        timeZone={LOCAL_TIME_ZONE}
        onValueChanged={onValueChanged}
      />
    );
  };

  return (
    <div className="grid ">
      <Popup
        visible={showDatePopup.show}
        ref={showDateRef}
        dragEnabled={false}
        onHiding={closeDatePopup}
        contentRender={schedulerRender}
        width={350}
        height={650}
        showCloseButton={true}
      />

      <div className="col-12">
        <Toast ref={toast} position="top-right" />
        <div className="w-full m-2">
          <DxDataGrid
            dataSource={medicalReport}
            id="medicalGrid"
            ref={medHeaderRef}
            allowColumnReordering={true}
            rowAlternationEnabled={true}
            showBorders={true}
            allowColumnResizing={true}
            remoteOperations={false}
            onRowExpanding={onRowExpanding}
            noDataText="No medical record found"
            onInitialized={onInitialized}
            onInitNewRow={initNewRow}
            onRowInserted={onRowInserted}
            onCellPrepared={onCellPrepared}
            onEditorPreparing={onMedicalGridEditorPreparing}
            onRowRemoved={onRowRemoved}
            onRowUpdated={onRowUpdated}
            repaintChangesOnly
            key="medicalId"
            onCellClick={dateTimeEditorRender}
          >
            <Editing
              mode="cell"
              useIcons={true}
              allowAdding={true}
              allowUpdating={true}
              allowDeleting={true}
              selectTextOnEditStart={true}
            >
              {/* To prevent the delete confirmation window, assign an empty string to the confirmDeleteMessageoption. */}
              <Texts confirmDeleteMessage="" />
            </Editing>
            <Scrolling showScrollbar="always" />
            <FilterRow visible={true} />
            <Column type="buttons">
              <ButtonGrid icon="add" onClick={addNewMedicalDetail} />
              <ButtonGrid icon="trash" onClick={confirmDelete} />
            </Column>
            <Column
              dataField="medicalDescription"
              caption="Case Name"
              dataType="string"
              width={150}
              editorOptions={MedDescEditorOptions}
              cssClass="dataGrid-cell"
            />
            <Column
              dataField="medicalCaseNumber"
              caption="Case Number"
              dataType="string"
              editorOptions={MedCaseNoEditorOptions}
              cssClass="dataGrid-cell"
            />
            <Column
              dataField="medicalFacility"
              caption="Facility"
              editorOptions={MedFacilityEditorOptions}
              calculateCellValue={(e) =>
                calculateCellValue(e, 'medicalFacility')
              }
              cssClass="dataGrid-cell"
            >
              <Lookup
                dataSource={facilities}
                valueExpr="recId"
                displayExpr="compName"
              />
              <Template name="facilityItemRender" render={FacilitySelectBox} />
            </Column>
            <Column
              dataField="medicalSpecifier"
              caption="Doctor"
              editorOptions={MedDoctorEditorOptions}
              calculateCellValue={(e) =>
                calculateCellValue(e, 'medicalSpecifier')
              }
              cssClass="dataGrid-cell"
            >
              <Lookup
                dataSource={personCompanies}
                valueExpr="id"
                displayExpr="name"
              />
              <Template name="doctorItemRender" render={DoctorSelectBox} />
            </Column>
            <Column
              dataField="medicalOwner"
              caption="Sales Person"
              calculateCellValue={(e) => calculateCellValue(e, 'medicalOwner')}
              cssClass="dataGrid-cell"
            >
              <Lookup
                dataSource={salesPersons}
                valueExpr="userId"
                displayExpr="userName"
              />
            </Column>
            <Column
              dataField="medicalScheduled"
              caption="Scheduled Date"
              dataType="datetime"
              format={MedSchedulerFormat}
              setCellValue={setScheduledCellValue}
              // editCellRender={dateTimeEditorRender}
              cssClass="dataGrid-cell"
            />
            <Column
              dataField="medicalScheduledTo"
              caption="Scheduled To Date"
              dataType="datetime"
              format={MedSchedulerFormat}
              cssClass="dataGrid-cell"
              visible={false}
            />
            <Column
              dataField="medicalFollowUp"
              caption="Follow Up Date"
              dataType="date"
              format={medicalFollowUpFormat}
              editorOptions={MedDateEditorOptions}
              cssClass="dataGrid-cell"
            />

            <Column
              dataField="medicalCloseStatus"
              caption="Status"
              dataType="number"
              width={120}
              cssClass="dataGrid-cell"
            >
              <Lookup
                dataSource={medicalStatusArray}
                valueExpr="id"
                displayExpr="text"
              />
            </Column>
            {/* <Column dataField="medicalStage" caption="Stage" width={120}>
              <Lookup
                dataSource={stageList}
                valueExpr="recId"
                displayExpr="actName"
              />
            </Column> */}
            <Column
              dataField="medicalValue"
              caption="Grand Total"
              format={amountFormat}
              alignment="right"
              dataType="number"
              allowEditing={false}
              cssClass="font-bold dataGrid-cell"
            />
            <Column
              dataField="medicalEventId"
              caption="Event ID"
              dataType="number"
              cssClass="dataGrid-cell"
              visible={false}
            />

            <MasterDetail enabled={true} component={MedicalDetailGrid} />

            <Toolbar>
              <Item location="before">
                <Button
                  icon="add"
                  onClick={addNewRow}
                  type="default"
                  className="bg-primary"
                  text="Add New"
                />
              </Item>
            </Toolbar>
          </DxDataGrid>

          {deleteDialog && (
            <DeleteRecordDialog
              header="Confirm delete"
              message="Please confirm you wish to delete the medical report ?"
              visible={deleteDialog}
              onHide={onDeleteDialogHide}
              footer={reportDialogTemplate}
            />
          )}
        </div>
      </div>

      <style global jsx>{`
        .dx-datagrid-rowsview .dx-master-detail-row > .dx-master-detail-cell {
          padding: 10px;
        }
        .dx-master-detail-cell .dx-datagrid-borders > .dx-datagrid-rowsview {
          // margin-left: 30px;
        }
        .dx-master-detail-cell .dx-datagrid {
          // margin-left: 30px;
        }
        .dx-scheduler {
          position: fixed;
          // top: 30vh;
          z-index: 999;
        }
        .dx-scheduler-small .dx-scheduler-time-panel {
          width: 80px;
        }
        .dx-scheduler-time-panel-cell,
        .dx-scheduler-date-table-cell {
          height: 30px;
        }
        .dx-scheduler-small .dx-scheduler-time-panel-cell {
          padding-top: 15px;
        }
        .dx-scheduler-time-panel tr:first-child td:first-child {
          padding-top: 5px;
        }
        .dx-scheduler-header .dx-toolbar .dx-toolbar-item-content,
        .dx-scheduler-header .dx-toolbar .dx-toolbar-menu-container {
          padding: 9px 45px;
        }
        .dx-scheduler-date-table-scrollable .dx-scrollable-content {
          // height: 430px;
          // overflow-y: auto;
          // cursor: grab;
        }
        .dx-toolbar .dx-toolbar-menu-container,
        .dx-scheduler-appointment-tooltip-wrapper,
        .dx-scheduler-appointment-popup {
          display: none;
        }
        .quote-btn {
          font-size: 12px !important;
          border-radius: 2px;
        }
      `}</style>
    </div>
  );
};

export default Medical;
