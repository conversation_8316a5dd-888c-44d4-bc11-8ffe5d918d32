import { useMenu } from '@contexts/menu';
import DeleteRecordDialog from '@modules/expenses/components/expenses/dialogs/deleteRecordDialog';
import {
  getMedicalDetail,
  postMedicalDetail,
  updateMedicalDetail,
  deleteMedicalDetail,
  updateQuote,
} from '@services/api';
import { Button, Popup, Template } from 'devextreme-react';
import DxDataGrid, {
  Column,
  MasterDetail,
  Scrolling,
  Button as GridButton,
  Editing,
  Lookup,
} from 'devextreme-react/data-grid';
import CustomStore from 'devextreme/data/custom_store';
import { Toast } from 'primereact/toast';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
  amountFormat,
  DATE_FILTER_FORMAT,
  medicalStageArray,
} from 'src/constants';
import DataGrid from 'devextreme/ui/data_grid';
import {
  allowUpdateMedical,
  calculateMnfPrinciCellvalue,
  debounce,
  facilityColumns,
  handleErrors,
  handleYoxelErrors,
  manufacturers,
  MedDateEditorOptions,
  MedPrinciEditorOptions,
  onCellPrepared,
  onPOPoupShown,
  setMedCellValue,
} from './utils';
import MedicalLineItem from './medicalLineItem';
import StepButton from './components/StepButton';
import { useDefaults } from '@contexts/defaults';
import { useMedical } from '@contexts/medical';
import { getTodaysDateTz } from 'src/utils';
import SelectBoxItemRender from './components/SelectBoxItemRender';

import RenderPOPopup from './components/RenderPOPopup';
import { RenderSOPopup } from './components/RenderSOPopup';

const getCompletedStatus = (status, index) => {
  return status === index ? 'current' : status > index;
};

const rowCellRender = (itemData, onStageClick) => {
  const STAGES = medicalStageArray.map((stage, index) => {
    const completed = getCompletedStatus(itemData.data.medicalStage, index);
    const onClick = () => onStageClick(index, itemData.key);

    return { id: `a${index}`, text: stage.text, completed, onClick };
  });

  return (
    <div>
      {STAGES.map((item) => (
        <StepButton key={item.id} {...item} />
      ))}
    </div>
  );
};

const PartNoSelectBox = (data) => (
  <SelectBoxItemRender
    columnData={facilityColumns(data)}
    id={`principal${data.recId}`}
    title="Other Information"
    displayValue={data.compName}
  />
);

const calculateStageDisplayValue = ({ medicalStage }) =>
  medicalStageArray.find((stage) => stage.id == medicalStage).text;

const disableQuoteLink = (e) => e.row.data.medicalQuotationId == null;

const disablePOLink = (e) => e.row.data.medcialPurchaseOrderId == null;

const addQuoteLink = (e) => {
  const quoteLink = `/RepfabricCRM/opploop/quotes/NewQuote.xhtml?recId=${e.row.data.medicalQuotationId}`;
  window.open(quoteLink, '_blank');
};

const addPOLink = (e) => {
  const poLink = `/RepfabricCRM/opploop/po/PoDetails.xhtml?id=${e.row.data.medcialPurchaseOrderId}`;
  window.open(poLink, '_blank');
};

const MedicalDetail = ({
  masterData,
  medDetailRef,
  medHeaderRef,
  // onDetailRowUpdated,
  customPrinci,
}) => {
  const {
    defaults: { DATE_FORMAT, YOXEL_TOKEN },
  } = useDefaults();

  const { showPOPopup, setShowPOPopup, showSOPopup, setShowSOPopup } =
    useMedical();

  const lineItemRef = useRef(null);

  const toast = useRef(null);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [medData, setMedData] = useState([]);

  const { user } = useMenu();
  const { medicalId } = masterData;
  const medicalFollowUpFormat = DATE_FILTER_FORMAT[DATE_FORMAT];

  const medicalGrid = document.getElementById('medicalGrid');
  const medicalGridInstance = DataGrid.getInstance(medicalGrid);

  const addMedicalLineItem = (e) => {
    if (e.row.data.medicalPrincipal == 0) {
      return toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Please add ${customPrinci} before adding line item`,
        life: 10000,
      });
    }
    e.component.expandRow(e.row.key).then(() => {
      setTimeout(() => {
        const lineItemGridElement = document.getElementById(
          'lineItemGrid' + e.row.key
        );
        const lineItemGridInstance = DataGrid.getInstance(lineItemGridElement);
        lineItemGridInstance.addRow();
        lineItemGridInstance.saveEditData();
        medDetailRef.current?.instance.refresh();
      }, 500);
    });
  };

  const onDetailRowUpdated = () => medicalGridInstance.refresh(true);

  const refreshGrid = () => {
    medHeaderRef.current?.instance.refresh(true).then(() => {
      medDetailRef.current?.instance.refresh(true).then(() => {
        lineItemRef.current?.instance.refresh();
      });
    });
  };

  const onDetailEditorPreparing = useCallback(
    (e) => {
      if (e.dataField === 'medicalFollowUp' && e.parentType === 'dataRow') {
        e.editorOptions.max = masterData.medicalScheduled;
        e.editorOptions.min = getTodaysDateTz();
      }
    },
    [masterData]
  );

  const LineItemGrid = useCallback(({ data }) => {
    return (
      <MedicalLineItem
        detailData={data.data}
        masterData={masterData}
        lineItemRef={lineItemRef}
        refreshGrid={refreshGrid}
      />
    );
  }, []);

  const cellRender = (itemData) =>
    rowCellRender(itemData, debouncedStageClickHandler);

  const onStageClick = async (index, key) => {
    const medicalRowData = medicalGridInstance
      .getDataSource()
      .items()
      .find((d) => d.medicalId == medicalId);

    const rowIndex = medDetailRef.current.instance.getRowIndexByKey(key);
    const detailArray = await medicalDetail.load();
    const detailData = detailArray[rowIndex];
    const errorMessage = medicalStageArray.find(
      (item) => item.id === index
    ).message;

    if (index === 3 || index === 6) {
      if (
        medicalRowData.medicalSpecifier === 0 ||
        detailData?.medicalPrincipal === 0
      ) {
        const detailMessage = `Please fill Doctor and ${customPrinci} for ${errorMessage}`;

        return toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: detailMessage,
          life: 10000,
        });
      }

      if (index == 3) {
        setShowSOPopup({ ...showSOPopup, show: true, key });
      } else if (index == 6) {
        if (detailData?.medicalQuotationId != null) {
          setShowPOPopup({
            ...showPOPopup,
            key,
            show: true,
          });
        } else {
          const detailMessage = `Please send sales order for ${errorMessage}`;
          return toast.current.show({
            severity: 'error',
            summary: 'Error',
            detail: detailMessage,
            life: 10000,
          });
        }
      }
    } else {
      if (detailData?.medicalPrincipal === 0) {
        const detailMessage = `Please fill ${customPrinci} for ${errorMessage} `;

        return toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: detailMessage,
          life: 10000,
        });
      }

      medicalDetail.update(key, { medicalStage: index }).then(() =>
        medDetailRef.current?.instance.refresh(true).then(() => {
          medHeaderRef.current.instance.refresh();
        })
      );
    }
  };

  const closeSOPopup = () => setShowSOPopup(false);
  const closePOPopup = () => setShowPOPopup(false);

  const debouncedStageClickHandler = useCallback(debounce(onStageClick, 500));

  const medicalDetail = useMemo(
    () =>
      new CustomStore({
        key: 'recordId',

        load: () =>
          getMedicalDetail(medicalId)
            .then(handleErrors)
            .catch(() => []),
        update: async (key, values) => {
          const params = {
            recordId: key,
            updUser: user.userId,
            ...values,
          };
          return updateMedicalDetail(params)
            .then((response) => {
              if (response.status !== 200 && response.status !== 201) {
                throw Error(response.statusText);
              }

              const quoteParam = {
                'quote-number': response.data.medicalQuotationId,
                'principal-id': response.data.medicalPrincipal,
                medOwner: 1,
              };

              if (quoteParam['quote-number'] != null && params.medicalPrincipal)
                updateQuote(quoteParam, YOXEL_TOKEN)
                  .then(handleYoxelErrors)
                  .catch((e) => {
                    console.log(e);
                  });

              return response.data;
            })

            .catch((e) => {
              console.log(e);
            });
        },
        remove: async (key) => {
          return deleteMedicalDetail(key)
            .then(handleErrors)
            .catch((e) => {
              console.log(e);
            });
        },
        insert: async (values) => {
          return postMedicalDetail(values)
            .then(handleErrors)
            .catch((e) => {
              console.log(e);
            });
        },
      }),
    [medicalId]
  );

  const confirmDelete = (e) => {
    if (e.row) {
      setMedData(e.row.data);
      setDeleteDialog(true);
    }
  };

  const deleteRecords = () =>
    medicalDetail.remove(medData.recordId).then(() => {
      setDeleteDialog(false);
      refreshGrid();
      toast.current.show({
        severity: 'success',
        summary: 'Successful',
        detail: `Detail deleted successfully`,
        life: 3000,
      });
    });

  const onDeleteDialogHide = useCallback(() => {
    setDeleteDialog(false);
  }, []);

  const deleteFooterDialog = (
    <>
      <Button
        icon="pi pi-check"
        type="danger"
        text="Delete"
        className="bg-error mr-2"
        onClick={deleteRecords}
      />
      <Button
        icon="pi pi-times"
        type="default"
        text="Cancel"
        className="bg-yellow-600 mr-2"
        onClick={() => {
          setDeleteDialog(false);
        }}
      />
    </>
  );

  const renderPOComponent = () => <RenderPOPopup ref={toast} />;
  const renderSOComponent = () => <RenderSOPopup toast={toast} />;

  return (
    <>
      <Toast ref={toast} baseZIndex={2000} />
      <DxDataGrid
        dataSource={medicalDetail}
        ref={medDetailRef}
        showColumnHeaders={false}
        rowAlternationEnabled
        noDataText={`No ${customPrinci} detail found`}
        showBorders
        onRowUpdated={onDetailRowUpdated}
        onEditorPreparing={onDetailEditorPreparing}
        onCellPrepared={onCellPrepared}
        width="100%"
        repaintChangesOnly
        id="medicalDetailGrid"
      >
        <Scrolling showScrollbar="always" />
        <Editing
          mode="cell"
          useIcons={true}
          allowUpdating={allowUpdateMedical}
          allowDeleting={false}
          selectTextOnEditStart={true}
        />
        <Column type="buttons" alignment="center" width={100}>
          <GridButton icon="add" onClick={addMedicalLineItem} />
          <GridButton icon="trash" onClick={confirmDelete} />
        </Column>
        <Column
          dataField="medicalPrincipal"
          calculateCellValue={calculateMnfPrinciCellvalue}
          editorOptions={MedPrinciEditorOptions}
          width={150}
          cssClass="dataGrid-cell"
        >
          <Lookup
            dataSource={manufacturers}
            valueExpr="recId"
            displayExpr="compName"
          />
          <Template name="PrincipalItemRender" render={PartNoSelectBox} />
        </Column>
        <Column cellRender={cellRender} width={950} alignment="right" />
        <Column
          dataField="medicalFollowUp"
          caption="Follow Up Date"
          dataType="date"
          format={medicalFollowUpFormat}
          editorOptions={MedDateEditorOptions}
          width={100}
        />
        <Column
          dataField="medicalStage"
          calculateDisplayValue={calculateStageDisplayValue}
          allowEditing={false}
          alignment="center"
          width={150}
        />

        <Column
          dataField="medicalValue"
          format={amountFormat}
          alignment="right"
          dataType="number"
          setCellValue={setMedCellValue}
          allowEditing={false}
          width={100}
          cssClass="font-bold"
        />
        <Column type="buttons">
          <GridButton
            text="Quote"
            cssClass="bg-primary px-2 py-0 quote-btn"
            onClick={addQuoteLink}
            disabled={disableQuoteLink}
          />
          <GridButton
            text="PO"
            cssClass="bg-primary px-2 py-0 quote-btn"
            onClick={addPOLink}
            disabled={disablePOLink}
          />
        </Column>

        <MasterDetail enabled={true} component={LineItemGrid} />
      </DxDataGrid>

      {deleteDialog && (
        <DeleteRecordDialog
          header="Confirm delete"
          message={`Please confirm you wish to delete the ${customPrinci} detail?`}
          visible={deleteDialog}
          onHide={onDeleteDialogHide}
          footer={deleteFooterDialog}
        />
      )}

      <Popup
        visible={showSOPopup.show}
        dragEnabled={false}
        onHiding={closeSOPopup}
        contentComponent={renderSOComponent}
        showCloseButton={true}
      />

      <Popup
        visible={showPOPopup.show}
        height="auto"
        width={470}
        title="PO Received"
        dragEnabled={false}
        onHiding={closePOPopup}
        contentComponent={renderPOComponent}
        onShown={onPOPoupShown}
        showCloseButton={true}
      />
    </>
  );
};

export default MedicalDetail;
