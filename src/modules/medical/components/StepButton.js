import { useMemo } from 'react';
import { classLookup } from '../utils';

const { Button } = require('devextreme-react');

const StepButton = ({ text, completed = false, onClick, id }) => {
  const className = classLookup[completed] || 'bg-gray-100';
  const key = id[1];
  const disabeledState = false;
  const stepButtonAttr = useMemo(
    () => ({
      style: {
        marginLeft: '-18px',
        clipPath:
          'polygon(90% 0%, 100% 50%, 90% 100%, 5% 100%, 15% 50%, 5% 0%)',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        fontSize: '10px',
        textAlign: 'center',
        whiteSpace: 'nowrap',
        cursor: disabeledState ? 'not-allowed' : '',
        pointerEvents: disabeledState ? 'auto' : '',
      },
    }),
    [completed, key]
  );
  return (
    <Button
      width={130}
      height="23px"
      // add text if completed is false
      text={completed === true ? '' : text}
      // add icon if completed is true
      icon={completed === true ? 'check' : ''}
      hint={text}
      completed={completed}
      elementAttr={stepButtonAttr}
      type={completed ? 'success' : 'normal'}
      className={`stepperBtn line-height-1 font-xs ${className}`}
      stylingMode="contained"
      onClick={onClick}
    />
  );
};
export default StepButton;
