import DataSource from 'devextreme/data/data_source';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  createCustomStore,
  dataURLToBlob,
  facilities,
  handleErrors,
  handleYoxelErrors,
  manufacturers,
  onCellPrepared,
  personCompanies,
  salesPersons,
} from '../utils';
import DataGrid from 'devextreme/ui/data_grid';
import { useMedical } from '@contexts/medical';
import {
  getCompanyContact,
  getMedSignURL,
  getMedicalContact,
  getMedicalLineItem,
  getNewQuoteNumber,
  getPrimaryContact,
  postMedSignFile,
  postMedicalContact,
  postQuote,
  postQuoteLineItem,
  sendMail,
  updateMedicalLineItem,
} from '@services/api';
import { Button, CheckBox, Popup, ScrollView, TextBox } from 'devextreme-react';
import DxDataGrid, {
  Column,
  FilterRow,
  Scrolling,
  Selection,
  Lookup,
} from 'devextreme-react/data-grid';
import SignatureCanvas from 'react-signature-canvas';
import MailTemplate from './MailTemplate';
import { DATE_FILTER_FORMAT, amountFormat } from 'src/constants';
import { useDefaults } from '@contexts/defaults';
import { useMenu } from '@contexts/menu';
import {
  convertDateFormat,
  getTodayDatePlusN,
  getTodaysDtmPlusN,
} from 'src/utils';
import CustomStore from 'devextreme/data/custom_store';

export const RenderSOPopup = ({ toast }) => {
  const { showSOPopup, setShowSOPopup } = useMedical();
  const {
    defaults: { DATE_FORMAT, TIME_FORMAT, YOXEL_TOKEN },
  } = useDefaults();
  const { user } = useMenu();

  const [showSignature, setShowSignature] = useState(false);
  const [disableSignBtns, setDisableSignBtns] = useState(false);

  const [isValidate, setIsValidate] = useState({
    name: false,
  });
  const [showContact, setShowContact] = useState(false);
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [rows, setRows] = useState([]); // State variable to hold the rows

  const [saveDisable, setSaveDisable] = useState(false);
  const [mailPopup, setMailPopup] = useState({
    show: false,
    data: '',
  });

  const signCanvasRef = useRef(null);
  const companyContactRef = useRef(null);
  const primaryContactRef = useRef(null);
  const checkBoxRef = useRef(null);

  const medicalFollowUpFormat = DATE_FILTER_FORMAT[DATE_FORMAT];

  const setNoValidate = () => {
    setIsValidate({ name: false });
  };

  const onInputChange = useCallback((e) => {
    setNoValidate();
    setSignImage((prev) => ({
      ...prev,
      name: e,
    }));
  }, []);

  // Medical Detail grid
  const medicalDetailGridId = 'medicalDetailGrid';
  const medicalDetailGrid = document.getElementById(medicalDetailGridId);
  const medicalDetailInstance = DataGrid?.getInstance(medicalDetailGrid);

  // Medical master grid
  const medicalGridId = 'medicalGrid';
  const medicalGrid = document.getElementById(medicalGridId);
  const medicalInstance = DataGrid.getInstance(medicalGrid);

  const medicalDetailData = medicalDetailInstance
    .getDataSource()
    .items()
    .find((d) => d.recordId == showSOPopup.key);

  const medicalRowData = medicalInstance
    .getDataSource()
    .items()
    .find((d) => d.medicalId == medicalDetailData?.medicalId);

  const lineItemData = useMemo(
    () =>
      new CustomStore({
        key: 'recordId',
        loadMode: 'raw',
        load: () => {
          return getMedicalLineItem(medicalDetailData?.recordId)
            .then(handleErrors)
            .catch(() => []);
        },
        update: async (key, values) => {
          const params = {
            recordId: key,
            updatedUser: user.userId,
            ...values,
          };
          return updateMedicalLineItem(params)
            .then(async (response) => {
              if (response.status !== 200 && response.status !== 201) {
                throw Error(response.statusText);
              }
              return response.data;
            })

            .catch((e) => {
              console.log(e);
            });
        },
      }),
    [showSOPopup.show]
  );

  const [signImage, setSignImage] = useState({
    key: '',
    url: '',
    fetchUrl: false,
    name:
      medicalDetailData?.medicalSignedName !== ''
        ? medicalDetailData?.medicalSignedName
        : user.userName,
  });

  // Fetch data from API and update the state variable with the retrieved rows
  const fetchContact = async () => {
    try {
      const response = await getPrimaryContact(
        medicalRowData?.medicalFacility,
        medicalRowData?.medicalSpecifier
      );
      const data = await response?.data;
      if (data) {
        setRows(data);
        setSelectedContacts(data.map((item) => item.contId));
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    if (medicalRowData) fetchContact(); // Fetch data when the component is mounted
  }, [showSOPopup.key]);

  useEffect(() => {
    setNoValidate();
    const fetchSignUrl = async () => {
      if (medicalDetailData?.medicalSignedImg) {
        setSignImage((prev) => ({
          ...prev,
          key: medicalDetailData?.medicalSignedImg,
          fetchUrl: true,
          name: medicalDetailData?.medicalSignedName,
        }));
      }
    };
    fetchSignUrl();
  }, [medicalDetailData?.medicalSignedImg]);

  useEffect(() => {
    const fetchSignUrl = async () => {
      try {
        if (signImage.key) {
          const url = await (
            await getMedSignURL(encodeURIComponent(signImage.key))
          ).data;
          setSignImage((prev) => ({ ...prev, url, fetchUrl: false }));
        }
      } catch (e) {
        console.log(e);
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: `Signature url retrieve error`,
          life: 10000,
        });
      }
    };
    fetchSignUrl();
  }, [signImage.fetchUrl]);

  useEffect(() => {
    const fetchSelectedContacts = async () => {
      try {
        const data = await (
          await getMedicalContact(medicalDetailData.recordId)
        )?.data;
        if (data && data.length > 0) {
          const contactIds = data.map((d) => d.medicalContactId);
          setSelectedContacts(contactIds);
        }
      } catch (e) {
        console.log(e);
      }
    };

    if (showSOPopup.show) {
      fetchSelectedContacts();
    } else {
      setSelectedContacts([]);
    }
  }, [showSOPopup.show]);

  const onSelectedContactsChanged = ({ selectedRowKeys }) => {
    setSelectedContacts(selectedRowKeys);
  };

  // Function to add a new row to the DataGrid
  const addNewRow = () => {
    const newRows = companyContactRef.current?.instance.getSelectedRowsData();

    const updatedRows = [...rows];
    newRows.forEach((newRow) => {
      // Check if the newRow already exists in the rows array
      const isDuplicate = updatedRows.some(
        (row) => row.contId === newRow.contId
      );
      // If it doesn't exist, add the newRow to the updatedRows array
      if (!isDuplicate) {
        updatedRows.push(newRow);
      }
    });
    setRows(updatedRows);
    setSelectedContacts(updatedRows.map((item) => item.contId));
    closeContactPopup();
  };

  // Initialize the data source with the rows
  const primaryContact = new DataSource({
    store: {
      type: 'array',
      data: rows,
      key: 'contId',
    },
  });

  const companyContact = useCallback(
    createCustomStore(
      'contId',
      [medicalRowData?.medicalSpecifier, medicalDetailData?.medicalPrincipal],
      getCompanyContact
    ),
    [showContact]
  );

  const signCellRender = (data) => {
    if (data.rowIndex == 0) {
      return (
        <img
          src={signImage.url}
          style={{ maxWidth: '75%', maxHeight: '75%' }}
        />
      );
    }
  };

  const clearSign = () => {
    signCanvasRef.current?.clear();
  };
  const closeSignPopup = () => {
    signCanvasRef.current?.clear();
    setNoValidate();
    setShowSignature(false);
  };
  const showSignPopup = () => {
    setShowSignature(true);
  };
  const closeContactPopup = () => {
    setShowContact(false);
  };
  const showContactPopup = () => {
    setShowContact(true);
  };

  const timestamp = new Date(medicalRowData?.medicalScheduled);
  const scheduleDate = convertDateFormat(timestamp, DATE_FORMAT);
  const scheduleTime = convertDateFormat(timestamp, TIME_FORMAT);

  const showMailPopup = () => {
    const mainGrid = document.getElementById('caseGrid');
    const mainInstance = DataGrid.getInstance(mainGrid);

    const caseData = mainInstance.getDataSource().items()[0];

    const contactTitle = personCompanies
      .load()
      ?.resolveArgs[0].find(
        (person) => person.id == caseData?.medicalSpecifier
      )?.title;

    const doctorsLastName = personCompanies
      .load()
      ?.resolveArgs[0].find(
        (person) => person.id == caseData?.medicalSpecifier
      )?.lastName;

    const medScheduleTimestamp = new Date(caseData?.medicalScheduled);
    const medScheduleDate = convertDateFormat(
      medScheduleTimestamp,
      DATE_FORMAT
    );
    const medScheduleTime = convertDateFormat(
      medScheduleTimestamp,
      TIME_FORMAT
    );

    setMailPopup({
      show: true,
      data: {
        subject: `${medScheduleDate} ${doctorsLastName} ${medScheduleTime} ${caseData?.medicalDescription}`,
        content: `Please see the attached sales order from my case with ${contactTitle} ${doctorsLastName} on ${medScheduleDate} at ${medScheduleTime} and return a PO to Extremity Medical`,
        template: '',
      },
    });
  };

  const closeMailPopup = () => setMailPopup({ show: false, data: '' });

  const getSign = async () => {
    if (signImage.name == '') {
      setIsValidate({
        name: signImage.name == '' ? true : false,
      });
    } else {
      try {
        if (signImage.name !== checkedName) setCheckedName('');
        if (signCanvasRef.current.isEmpty()) {
          toast.current.show({
            severity: 'error',
            summary: 'Error',
            detail: `Please sign`,
            life: 3000,
          });
          return;
        }
        setDisableSignBtns(true);
        const FILE_NAME = `${medicalRowData.medicalDescription}${medicalDetailData.recordId}${user.userId}.png`;
        const signatureDataURL = signCanvasRef.current.toDataURL();
        const signatureBlob = dataURLToBlob(signatureDataURL);
        const formData = new FormData();
        formData.append('file', signatureBlob, FILE_NAME);
        const res = await postMedSignFile(formData);

        medicalDetailInstance
          .getDataSource()
          .store()
          .update(medicalDetailData.recordId, {
            medicalSignedId: user.userId,
            medicalSignedName: signImage.name
              ? signImage.name
              : medicalDetailData.medicalSignedName,
            medicalSignedImg: res.data.imageKey,
          })
          .then((response) => {
            medicalDetailInstance.refresh(true).then(() => {
              setSignImage((prev) => ({
                ...prev,
                key: res.data.imageKey,
                fetchUrl: true,
                name: response.medicalSignedName,
              }));
              toast.current.show({
                severity: 'success',
                summary: 'Successful',
                detail: `Signature uploaded successfully`,
                life: 3000,
              });
            });
          });
      } catch (e) {
        console.log(e);
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: `Please try after some time`,
          life: 10000,
        });
      } finally {
        setDisableSignBtns(false);
        setShowSignature(false);
      }
    }
  };

  const handleCancel = () => {
    setShowSOPopup({ show: false, key: '' });
    setRows([]);
    companyContactRef.current?.instance.deselectAll();
    setSignImage({ url: '', key: '', fetchUrl: false, name: '' });
  };

  // Handle Function

  const generateQuoteLineItem = async (quoteId) => {
    if (quoteId != null)
      await getMedicalLineItem(medicalDetailData?.recordId)
        .then((lineItemArray) => {
          lineItemArray.data.forEach(async (item) => {
            const lineItemParam = {
              'order-qty': item.medicalQuantityRequired,
              description: item.medicalPartDescription,
              'unit-price': item.medicalUnitPrice,
              'cust-price-flag': item.medicalCustomerPriceFlag,
              'ext-price': item.medicalExtendedPrice,
              uom: item.medicalUom,
              'uom-units': 1,
              multiplier: item.medicalMultiplier,
              'std-rate': item.medicalStandardRate,
              'part-num': item.medicalPartManufacturer,
              'principal-id': 0,
              medOwner: 1,
            };
            if (item.medicalQuoteDetailId == null) {
              const lineItem = await postQuoteLineItem(
                quoteId,
                lineItemParam,
                YOXEL_TOKEN
              )
                .then(handleYoxelErrors)
                .catch((e) => {
                  console.log(e);
                });

              if (lineItem) {
                return lineItemData
                  .update(item.recordId, {
                    medicalQuoteDetailId: lineItem.id,
                  })
                  .then(handleErrors)
                  .catch(() => {
                    throw Error('Network Error');
                  });
              }
            }
          });
        })
        .catch((error) => {
          console.error('Error refreshing data:', error);
        });
  };

  const generateQuoteMedical = async () => {
    const customerId = medicalRowData.medicalSpecifier;
    const distributorId = medicalRowData.medicalFacility;
    const principalId = medicalDetailData.medicalPrincipal;
    const personCompanyData = personCompanies.load().resolveArgs[0];
    const customerContactId = personCompanyData.find(
      (item) => item.id == medicalRowData.medicalSpecifier
    )?.unifiedId;

    const facilityResponse = await getPrimaryContact(
      medicalRowData?.medicalFacility,
      0
    );
    const facilityContactId = await facilityResponse?.data;

    const principalReposnse = await getPrimaryContact(
      medicalDetailData?.medicalPrincipal,
      0
    );
    const principalContactId = await principalReposnse?.data;

    const quoteData = await getNewQuoteNumber(
      customerId,
      principalId,
      distributorId,
      1,
      YOXEL_TOKEN
    )
      .then(handleErrors)
      .catch((e) => {
        console.log(e);
      });

    const quoteParam = {
      'quote-number': quoteData['quote-number'],
      'seq-no': quoteData['seq-no'],
      'principal-id': principalId,
      'customer-id': customerId,
      'distributor-id': distributorId,
      'quote-owner': medicalRowData.medicalOwner,
      'quote-date': convertDateFormat(getTodayDatePlusN(0), 'YYYY-MM-DD'),
      'customer-contact-id': customerContactId,
      'principal-contact-id': principalContactId[0]
        ? principalContactId[0].contId
        : 0,
      'distributor-contact-id': facilityContactId[0]
        ? facilityContactId[0].contId
        : 0,
      // #11795 - The quote recipient (sales order) will always be the distributor for medical cases.
      recipient: 'Distributor',
      program: medicalRowData.medicalDescription,
      'bid-buy': 'Bid',
      'follow-up': medicalRowData.medicalFollowUp,
      'quote-source': 'MEDICAL',
      'quote-email-flag': true,
      medOwner: 1,
    };

    return postQuote(quoteParam, YOXEL_TOKEN)
      .then(handleErrors)
      .catch((e) => {
        console.log(e);
      });
  };

  const saveMedicalContacts = async () => {
    if (selectedContacts.length > 0) {
      const params = {
        medicalContacts: selectedContacts.map((item) => ({
          medicalDetailId: medicalDetailData.recordId,
          medicalContactId: item,
        })),
      };

      try {
        await postMedicalContact(params);
        toast.current.show({
          severity: 'success',
          summary: 'Successful',
          detail: `Sales order saved successfully`,
          life: 3000,
        });
      } catch (error) {
        toast.current.show({
          severity: 'error',
          summary: 'Update Failed',
          detail: 'Please try after sometime',
          life: 3000,
        });
      }
    }
  };

  const sendQuoteMail = async () => {
    const selectedContact =
      primaryContactRef.current.instance.getSelectedRowsData();

    if (
      selectedContact.length > 0 &&
      medicalDetailData.medicalQuotationId != null
    ) {
      const param = {
        quotationId: medicalDetailData.medicalQuotationId,
        salesPersonId: medicalRowData.medicalOwner,
        salesPerson: salesPersons
          .load()
          .resolveArgs[0].find(
            (item) => item.userId == medicalRowData.medicalOwner
          )?.userName,
        subject: mailPopup.data.subject,
        body: mailPopup.data.content,
        templateId: mailPopup.data.template,
        signatureName: signImage.name,
        signatureUrl: signImage.url,
        caseNumber: medicalRowData.medicalCaseNumber,
        facility: facilities
          .load()
          .resolveArgs[0].find(
            (fac) => fac.recId == medicalRowData.medicalFacility
          )?.compName,
        doctor: personCompanies
          .load()
          .resolveArgs[0].find(
            (person) => person.id == medicalRowData.medicalSpecifier
          )?.name,
        contactTitle: personCompanies
          .load()
          .resolveArgs[0].find(
            (person) => person.id == medicalRowData.medicalSpecifier
          )?.title,
        doctorsLastName: personCompanies
          .load()
          .resolveArgs[0].find(
            (person) => person.id == medicalRowData.medicalSpecifier
          )?.lastName,
        dateOfSurgery: `${scheduleDate} ${scheduleTime}`,
        currentDateTime: getTodaysDtmPlusN(0),
        to: selectedContact
          .filter((item) => item.contEmail != '')
          .map((item) => item.contEmail),
      };

      try {
        await (
          await sendMail(param)
        ).data;
        toast.current.show({
          severity: 'success',
          summary: 'Successful',
          detail: `Mail sent successfully`,
          life: 3000,
        });
      } catch (err) {
        toast.current.show({
          severity: 'error',
          summary: 'Failed to send mail',
          detail: 'Please try after sometime',
          life: 3000,
        });
      }
    } else {
      toast.current.show({
        severity: 'error',
        summary: 'Failed to send mail',
        detail: 'Please try after sometime',
        life: 3000,
      });
    }
  };

  const saveMedicalSO = async () => {
    setSaveDisable(true);
    if (signImage.name !== checkedName) setCheckedName('');
    const quoteDetail =
      medicalDetailData.medicalQuotationId == null &&
      (await generateQuoteMedical());

    if (quoteDetail) {
      generateQuoteLineItem(quoteDetail.id);
      medicalDetailInstance
        .getDataSource()
        .store()
        .update(medicalDetailData.recordId, {
          medicalQuotationId: quoteDetail.id,
          medicalSignedId: user.userId,
          medicalSignedName: signImage.name
            ? signImage.name
            : medicalDetailData.medicalSignedName,
          medicalSignedImg: signImage.key,
        })
        .then((response) => {
          medicalDetailInstance.refresh();
          setSignImage((prev) => ({
            ...prev,
            name: response.medicalSignedName,
          }));
        });
    } else {
      generateQuoteLineItem(medicalDetailData.medicalQuotationId).then(() => {
        medicalDetailInstance
          .getDataSource()
          .store()
          .update(medicalDetailData.recordId, {
            medicalSignedName: signImage.name
              ? signImage.name
              : medicalDetailData.medicalSignedName,
          })
          .then(() => {
            medicalDetailInstance.refresh(true);
          });
      });
    }
    saveMedicalContacts();
  };

  const sendMedicalSO = async () => {
    sendQuoteMail().then(() => {
      medicalDetailData.medicalQuotationId != null &&
        medicalDetailData.medicalStage < 3 &&
        medicalDetailInstance
          .getDataSource()
          .store()
          .update(medicalDetailData.recordId, {
            medicalStage: 3,
          });
      handleCancel();
      medicalDetailInstance.refresh();
    });
  };

  // Component Render

  const createDialogFooter = (
    <>
      <Button
        type="success"
        text="Save"
        icon="pi pi-check"
        className="bg-success mr-2"
        onClick={saveMedicalSO}
        disabled={saveDisable}
      />
      <Button
        type="default"
        text="Send"
        icon="pi pi-check"
        className="bg-primary mr-2"
        onClick={showMailPopup}
        disabled={
          selectedContacts.length == 0 ||
          signImage.key == '' ||
          (medicalDetailData?.medicalQuotationId == null && !saveDisable)
        }
      />

      <Button
        type="danger"
        text="Cancel"
        icon="pi pi-times"
        className="bg-error mr-2"
        onClick={handleCancel}
      />
    </>
  );

  const renderSignPopup = () => {
    return (
      <>
        <div className="formgrid grid m-0">
          <div className="flex align-items-baseline field col w-100">
            <label className="mr-3 " htmlFor="medicalSignedName">
              Signed By
            </label>
            <div className="block">
              <TextBox
                value={signImage.name}
                onValueChanged={(e) => onInputChange(e.value)}
                maxLength={200}
                placeholder="Enter Signed By"
                required={true}
                autoResizeEnabled={true}
                className={isValidate.name ? 'border-red-500' : ''}
                name="medicalSignedName"
              />
              {isValidate.name ? (
                <div className="dx-item dx-validationsummary-item mt-1">
                  <div className="dx-item-content dx-validationsummary-item-content">
                    Signed By is required
                  </div>
                </div>
              ) : (
                <div className="mt-2"></div>
              )}
            </div>
          </div>
        </div>

        <SignatureCanvas
          penColor="black"
          throttle={8}
          canvasProps={{
            width: 430,
            height: 150,
            style: { border: '1px solid green', backgroundColor: '#f0f0f0' },
          }}
          ref={signCanvasRef}
        />
        <div className="flex justify-content-end">
          <Button
            type="success"
            text="Ok"
            className="bg-success mt-2 mr-2"
            onClick={getSign}
            disabled={disableSignBtns}
          />
          <Button
            type="danger"
            text="Clear"
            className="p-button-warning mt-2 mr-2"
            onClick={clearSign}
            disabled={disableSignBtns}
          />
        </div>
      </>
    );
  };

  const contactDialogFooter = (
    <>
      <Button
        type="default"
        text="Add"
        icon="pi pi-check"
        className="bg-primary mr-2"
        onClick={addNewRow}
        // disabled={selectedContacts.length == 0}
      />
      <Button
        type="danger"
        text="Cancel"
        icon="pi pi-times"
        className="bg-error mr-2"
        onClick={closeContactPopup}
      />
    </>
  );

  const [checkedName, setCheckedName] = useState(signImage.name);

  useEffect(() => {
    if (checkedName) setSignImage((prev) => ({ ...prev, name: checkedName }));
  }, [checkedName]);

  const handleCheckboxChange = (rowData) => {
    if (!checkedName) setCheckedName(rowData);
    else setCheckedName('');
    if (saveDisable) setSaveDisable(false);
  };

  const renderSignCell = (cellData) => {
    return (
      <CheckBox
        ref={checkBoxRef}
        value={cellData.data.contName == checkedName}
        onValueChanged={() => handleCheckboxChange(cellData.data.contName)}
      />
    );
  };

  const renderContactPopup = () => {
    return (
      <>
        <ScrollView width="100%" height="100%">
          <DxDataGrid
            dataSource={companyContact}
            ref={companyContactRef}
            onCellPrepared={onCellPrepared}
            noDataText={`No contact found`}
            columnAutoWidth={false}
            width="99%"
            showBorders
          >
            <FilterRow visible={true} />
            <Scrolling showScrollbar="always" />
            <Column dataField="contName" caption="Contact Name" width="25%" />
            <Column dataField="compName" caption="Company Name" width="40%" />
            <Column dataField="contEmail" caption="Email" width="25%" />
            <Column dataField="contFax" caption="Fax" width="20%" />
            <Column
              caption="Signatory"
              cellRender={renderSignCell}
              width="15%"
              alignment="center"
            />
            <Selection
              mode="multiple"
              width={300}
              selectAllMode="page"
              // deferred={true}
              showCheckBoxesMode="always"
            />
          </DxDataGrid>
        </ScrollView>
        <div className="p-dialog-footer popup text-right">
          {contactDialogFooter}
        </div>
      </>
    );
  };

  return (
    <>
      <ScrollView width="100%" height="100%">
        <div className="mt-2 mb-6 pr-3">
          <div className="flex justify-content-between">
            <h3>Case Details</h3>
            <Button
              type="default"
              text="Sign"
              // icon="pi pi-check"
              className="bg-primary mb-3"
              onClick={showSignPopup}
              // disabled={medicalDetailData?.medicalStage >= 3}
            />
          </div>
          <DxDataGrid
            dataSource={[medicalRowData]}
            onCellPrepared={onCellPrepared}
            showBorders
            id="caseGrid"
          >
            <Column
              dataField="medicalDescription"
              caption="Case Name"
              dataType="string"
              width={150}
              cssClass="dataGrid-cell"
            />
            <Column
              dataField="medicalFacility"
              caption="Facility"
              cssClass="dataGrid-cell"
            >
              <Lookup
                dataSource={facilities}
                valueExpr="recId"
                displayExpr="compName"
              />
            </Column>
            <Column
              dataField="medicalSpecifier"
              caption="Doctor"
              cssClass="dataGrid-cell"
            >
              <Lookup
                dataSource={personCompanies}
                valueExpr="id"
                displayExpr="name"
              />
            </Column>

            <Column
              dataField="medicalFollowUp"
              caption="Follow Up Date"
              dataType="date"
              format={medicalFollowUpFormat}
            />
          </DxDataGrid>
          <div className="flex justify-content-between mt-3">
            <h3>Sales Order</h3>
          </div>

          <DxDataGrid
            dataSource={[medicalDetailData]}
            showColumnHeaders={false}
            onCellPrepared={onCellPrepared}
            showColumnLines={false}
            id="caseDetailGrid"
          >
            <Column dataField="medicalPrincipal" width="50%">
              <Lookup
                dataSource={manufacturers}
                valueExpr="recId"
                displayExpr="compName"
              />
            </Column>
            <Column
              dataField="medicalValue"
              format={amountFormat}
              dataType="number"
              alignment="right"
              allowEditing={false}
              width={100}
              cssClass="font-bold"
            />
          </DxDataGrid>

          <DxDataGrid
            dataSource={lineItemData}
            id="medSignatureGrid"
            onCellPrepared={onCellPrepared}
            showBorders
            // noDataText={`No ${customPrinci} line item found`}
          >
            <Column
              dataField="medicalPartManufacturer"
              caption="Part No"
              cssClass="dataGrid-cell"
            />
            <Column
              dataField="medicalPartDescription"
              caption="Description"
              cssClass="dataGrid-cell"
            />
            <Column
              dataField="medicalQuantityRequired"
              caption="Required Quantity"
              dataType="number"
            />
            <Column
              caption="Signature"
              disabled={false}
              alignment="center"
              cellRender={signCellRender}
            />
          </DxDataGrid>

          <div className="flex justify-content-between mt-3">
            <h3>Communicate</h3>
            <div>
              <Button
                type="default"
                text="Add Contacts"
                // icon="pi pi-check"
                className="bg-primary mx-2"
                onClick={showContactPopup}
                // disabled={medicalDetailData?.medicalQuotationId == null}
              />
            </div>
          </div>
          <DxDataGrid
            dataSource={primaryContact}
            selectedRowKeys={selectedContacts}
            onSelectionChanged={onSelectedContactsChanged}
            // selectedRowKeys={medContactData.map((i) => i.medicalContactId)}
            ref={primaryContactRef}
            onCellPrepared={onCellPrepared}
            showBorders
            noDataText={`No contact found`}
          >
            <Column dataField="contName" caption="Contact Name" />
            <Column dataField="compName" caption="Company Name" />
            <Column dataField="contEmail" caption="Email" />
            <Column dataField="contFax" caption="Fax" />
            <Column
              caption="Signatory"
              cellRender={renderSignCell}
              width="15%"
              alignment="center"
            />
            <Selection
              mode="multiple"
              width={300}
              selectAllMode="page"
              // deferred={true}
              showCheckBoxesMode="always"
            />
          </DxDataGrid>
        </div>

        <Popup
          visible={showSignature}
          height={325}
          width={460}
          showCloseButton={!disableSignBtns}
          onHiding={closeSignPopup}
          dragEnabled={false}
          title="Sign"
          contentRender={renderSignPopup}
        />

        <Popup
          visible={showContact}
          height={500}
          width="75%"
          // showCloseButton={!disableSignBtns}
          onHiding={closeContactPopup}
          dragEnabled={false}
          title="Add Contacts"
          contentRender={renderContactPopup}
          showCloseButton={true}
        />

        <MailTemplate
          showPopup={mailPopup.show}
          hidePopup={closeMailPopup}
          mailData={mailPopup}
          setMailData={setMailPopup}
          sendMedicalSO={sendMedicalSO}
        />
      </ScrollView>

      <div className="p-dialog-footer popup text-right">
        {createDialogFooter}
      </div>
      <style global jsx>
        {`
          .dx-datagrid > .dx-datagrid-headers {
            position: -webkit-sticky;
            position: sticky;
            background-color: #fff;
            z-index: 1;
            top: 0;
          }
        `}
      </style>
    </>
  );
};
