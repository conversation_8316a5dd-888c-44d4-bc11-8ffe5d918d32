import { Tooltip } from 'devextreme-react';
const SelectBoxItemRender = ({ columnData, id, title, displayValue }) => {
  return (
    <div>
      <div id={id}>{displayValue}</div>

      <Tooltip
        target={`#${id}`}
        position="right"
        showEvent="mouseenter"
        hideEvent="mouseleave"
        hideOnOutsideClick={false}
        wrapperAttr={{ class: 'bg-class' }}
      >
        <table className="tooltip-table">
          <tr>
            <th colSpan="2">{title}</th>
          </tr>
          <tbody>
            {columnData.map((col, index) => (
              <tr key={index}>
                <td>{col.title}:</td>
                <td>{col.value}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </Tooltip>
      <style global jsx>{`
        .bg-class .dx-overlay-content {
          background: #dfe2e6;
          margin: 0;
          padding: 0;
        }
        .tooltip-table {
          width: 100%;
          text-align: left;
        }
        .tooltip-table th,
        .tooltip-table td {
          font-size: 14px;
          padding: 2px;
        }
        .tooltip-table th {
          font-size: 15px;
        }
      `}</style>
    </div>
  );
};

export default SelectBoxItemRender;
