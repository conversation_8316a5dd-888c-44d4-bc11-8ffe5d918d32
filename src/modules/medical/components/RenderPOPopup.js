import React, { forwardRef, useEffect, useMemo, useState } from 'react';
import DataGrid from 'devextreme/ui/data_grid';
import dxFrom from 'devextreme/ui/form';
import { useDefaults } from '@contexts/defaults';
import {
  amountFormat,
  DATE_FILTER_FORMAT,
  TIME_FILTER_FORMAT,
} from 'src/constants';
import Form, {
  ButtonItem,
  GroupItem,
  Item,
  Label,
  RequiredRule,
  SimpleItem,
} from 'devextreme-react/form';
import { useMedical } from '@contexts/medical';
import { medPOPopupPartNoEditorOptions, personCompanies } from '../utils';
import {
  convertDateFormat,
  getDateFormatISOFormat,
  getTodayDatePlusN,
  getTodaysDate,
} from 'src/utils';
import {
  getMedicalLineItem,
  getMedicalPrinciCommRate,
  getMedLinePartNo,
  getMedPOSend,
  linkPOQuote,
  postMedPOSend,
  putMedPOSend,
  updateMedicalLineItem,
} from '@services/api';
import { useMenu } from '@contexts/menu';

async function handleApiCalls(lineItems, poData) {
  const promises = lineItems.map(async (obj) => {
    try {
      // const transformedObj = transformPOData(obj);
      // const _updatedParam = { ...transformedObj, ...updatedParams };
      // const firstApiResponse = await postPOLineItem(_updatedParam, YOXEL_TOKEN);
      // const firstApiData = firstApiResponse.data;
      const params = {
        recordId: obj.recordId,
        medicalPurchaseOrderDetailId: poData['line-items'].find(
          (item) => item['part-num'] == obj.medicalPartManufacturer
        )?.id,
      };
      return await updateMedicalLineItem(params).data;
    } catch (error) {
      console.error(`Error for element with ID ${obj}:, ${error.message}`);
      return null;
    }
  });

  try {
    await Promise.all(promises);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

const RenderPOPopup = forwardRef((props, ref) => {
  const { showPOPopup, setShowPOPopup } = useMedical();
  const {
    defaults: { DATE_FORMAT, TIME_FORMAT, YOXEL_TOKEN },
  } = useDefaults();
  const { user } = useMenu();

  const [data, setData] = useState({
    commRate: '',
    lineItems: [],
    productFamily: '',
    poData: null,
    invoiceData: '',
  });
  const [saveDisable, setSaveDisable] = useState(false);
  const medicalFollowUpFormat = DATE_FILTER_FORMAT[DATE_FORMAT];
  const MedSchedulerFormat = `${medicalFollowUpFormat} ${TIME_FILTER_FORMAT[TIME_FORMAT]}`;

  const isInvoiceDisabled = useMemo(
    () => !!data.invoiceData,
    [data.invoiceData]
  );
  const dateEditorOptions = useMemo(
    () => ({
      type: 'date',
      displayFormat: medicalFollowUpFormat,
      // dateSerializationFormat: medicalFollowUpFormat,
      useMaskBehavior: true,
    }),
    [medicalFollowUpFormat]
  );
  const invoiceDateEditorOptions = useMemo(
    () => ({
      type: 'date',
      displayFormat: medicalFollowUpFormat,
      disabled: isInvoiceDisabled,
      // dateSerializationFormat: medicalFollowUpFormat,
      useMaskBehavior: true,
    }),
    [medicalFollowUpFormat, isInvoiceDisabled]
  );
  // Medical Detail grid
  const medicalDetailGridId = 'medicalDetailGrid';
  const medicalDetailGrid = document.getElementById(medicalDetailGridId);
  const medicalDetailInstance = DataGrid?.getInstance(medicalDetailGrid);

  const medicalDetailData = medicalDetailInstance
    .getDataSource()
    .items()
    .find((d) => d.recordId == showPOPopup.key);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [commRate, lineItemsRes, productFamilyRes, poData] =
          await Promise.all([
            getMedicalPrinciCommRate(medicalDetailData?.medicalPrincipal),
            getMedicalLineItem(showPOPopup.key),
            getMedLinePartNo(medicalDetailData?.medicalPrincipal),
            medicalDetailData?.medcialPurchaseOrderId
              ? getMedPOSend(
                  medicalDetailData?.medcialPurchaseOrderId,
                  1,
                  YOXEL_TOKEN
                )
              : Promise.resolve(null),
          ]);

        const lineItems = lineItemsRes?.data || [];
        const productFamily =
          productFamilyRes && productFamilyRes.data.length > 0
            ? productFamilyRes.data[0].prodFamily
            : '';
        const invoiceData = poData?.data
          ? poData?.data['po-timeline']?.find(
              (item) => item['po-status'] == 'Shipped'
            )
          : '';
        setData({
          ...data,
          commRate: commRate.data,
          lineItems,
          productFamily,
          poData: poData?.data ?? null,
          invoiceData,
        });
      } catch (e) {
        console.log(e);
      }
    };
    if (showPOPopup.show) {
      fetchData();
    }
  }, [showPOPopup.show]);
  if (!showPOPopup.show || !showPOPopup.key) {
    return null;
  }

  // Medical master grid
  const medicalGridId = 'medicalGrid';
  const medicalGrid = document.getElementById(medicalGridId);
  const medicalInstance = DataGrid.getInstance(medicalGrid);

  const medicalDetailRowIndex = medicalDetailInstance.getRowIndexByKey(
    showPOPopup.key
  );
  const prinicipalNameInCell = medicalDetailInstance.getCellElement(
    medicalDetailRowIndex,
    'medicalPrincipal'
  )?.innerText;

  const medicalRowIndex = medicalInstance.getRowIndexByKey(
    medicalDetailData?.medicalId
  );
  const doctorNameInCell = medicalInstance.getCellElement(
    medicalRowIndex,
    'medicalSpecifier'
  )?.innerText;
  const facilityNameInCell = medicalInstance.getCellElement(
    medicalRowIndex,
    'medicalFacility'
  )?.innerText;
  const { medicalSpecifier, medicalFacility, medicalDescription } =
    medicalInstance
      .getDataSource()
      .items()
      .find((d) => d.medicalId == medicalDetailData?.medicalId);

  const salesTeamId = personCompanies
    .load()
    ?.resolveArgs[0].find(
      (person) => person.id == medicalSpecifier
    )?.salesTeamId;

  const initialValues = {
    'po-number': data.poData ? data.poData['po-number'] : '',
    date: data.poData
      ? data.poData.date
      : convertDateFormat(getTodayDatePlusN(0), 'YYYY-MM-DD'),
    'invoice-number': data.invoiceData ? data.invoiceData['po-ref-number'] : '',
    'invoice-date': data.invoiceData
      ? data.invoiceData['po-ref-date']
      : convertDateFormat(getTodayDatePlusN(0), 'YYYY-MM-DD'),
    poReviewDate: medicalDetailData?.medicalFollowUp,
    poAmount: +medicalDetailData?.medicalValue,
    'sales-team-id': salesTeamId,
    'user-id': user.userName,
    dateTimeStamp: new Date(),
  };

  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      setSaveDisable(true);
      const form = document.getElementById('medPOForm');
      const formInstance = dxFrom.getInstance(form);
      const formData = formInstance.option('formData');
      const param = {
        'po-number': formData['po-number'],
        date: formData.date,
        'quote-number': '',
        'quote-date': null,
        'sales-team-id': salesTeamId,
        'customer-id': medicalSpecifier,
        'customer-name': doctorNameInCell,
        'principal-id': medicalDetailData?.medicalPrincipal,
        'principal-name': prinicipalNameInCell,
        'distributor-id': medicalFacility,
        'distributor-name': facilityNameInCell,
        program: medicalDescription,
        'secondary-customer-id': 0,
        'close-status-name': formData['invoice-number']
          ? 'Fully Shipped'
          : 'Ordered',
        'total-price': formData.poAmount,
        'commission-rate': data.commRate,
        'commission-projected': (formData.poAmount * data.commRate) / 100,
        'so-number': '',
        'so-date': null,
        'invoice-number': formData['invoice-number'],
        'invoice-date': formData['invoice-date'],
        'medical-po': 1,
        medOwner: 1,
        'line-items': data.lineItems
          .filter((lineItem) => lineItem.medicalPartManufacturer != null)
          .map((lineItem) => ({
            'part-num': lineItem.medicalPartManufacturer,
            description: lineItem.medicalPartDescription,
            'product-family': data.productFamily,
            'order-qty': +lineItem.medicalQuantityRequired,
            'std-rate': +lineItem.medicalStandardRate,
            multiplier: +lineItem.medicalMultiplier,
            uom: lineItem.medicalUom,
            'uom-units': 1,
            'unit-price': +lineItem.medicalUnitPrice,
            'total-price': +lineItem.medicalExtendedPrice,

            'shipping-status-name': formData['invoice-number']
              ? 'Shipped'
              : 'Not Shipped',
            'invc-date': null,
            'comm-rate': data.commRate,
            'comm-projected': (formData.poAmount * data.commRate) / 100,
            'plan-qty': +lineItem.medicalQuantityRequired,
            'shipping-qty': formData['invoice-number']
              ? +lineItem.medicalQuantityRequired
              : 0,
            'plan-date': convertDateFormat(getTodayDatePlusN(0), 'YYYY-MM-DD'),
            'shipping-date': formData['invoice-number']
              ? convertDateFormat(getTodayDatePlusN(0), 'YYYY-MM-DD')
              : null,
            'created-time': getDateFormatISOFormat(getTodaysDate()),
            'last-modified-time': getDateFormatISOFormat(getTodaysDate()),
          })),
      };
      if (data.poData) {
        const _param = {
          'po-number': formData['po-number'],
          date: formData.date,
          'close-status-name': formData['invoice-number']
            ? 'Fully Shipped'
            : 'Ordered',
          'invoice-number': formData['invoice-number'],
          'invoice-date': formData['invoice-date'],
          'medical-po': 1,
          'total-price': formData.poAmount,
          'commission-rate': data.commRate,
          'commission-projected': (formData.poAmount * data.commRate) / 100,
          medOwner: 1,
        };
        await putMedPOSend(data.poData.id, _param, YOXEL_TOKEN);
        medicalDetailInstance.getDataSource().store().update(showPOPopup.key, {
          medicalStage: 6,
        });
        ref.current.show({
          severity: 'success',
          summary: 'Successful',
          detail: 'PO received updated successfully',
          life: 3000,
        });
        medicalDetailInstance.refresh();
      } else {
        await postMedPOSend(param, YOXEL_TOKEN)
          .then(async (res) => {
            const poData = res.data;
            const lineItems = data.lineItems.filter(
              (lineItem) => lineItem.medicalPartManufacturer != null
            );
            if (lineItems.length > 0) {
              await handleApiCalls(lineItems, poData);
            }

            const linkParam = {
              'po-id': poData.id,
              'quote-id': medicalDetailData.medicalQuotationId,
              medOwner: 1,
            };
            await linkPOQuote(linkParam, YOXEL_TOKEN);

            medicalDetailInstance
              .getDataSource()
              .store()
              .update(showPOPopup.key, {
                medicalStage: 6,
                medcialPurchaseOrderId: poData.id,
              });

            ref.current.show({
              severity: 'success',
              summary: 'Successful',
              detail: 'PO received saved successfully',
              life: 3000,
            });
            medicalDetailInstance.refresh();
          })
          .catch((er) => {
            console.log(er);
            ref.current.show({
              severity: 'error',
              summary: 'PO Save Failed',
              detail: 'Please try after sometime',
              life: 10000,
            });
          });
      }
    } catch (err) {
      console.log(err);
    } finally {
      setShowPOPopup({ ...showPOPopup, show: false, key: '' });
    }
  };

  const handleCancel = () => {
    setShowPOPopup({ show: false, key: '' });
  };

  return (
    <form className="flex-1 m-2 mb-6" onSubmit={handleSubmit}>
      <Form id="medPOForm" formData={initialValues}>
        <GroupItem
          cssClass="text-center"
          caption={`MFG ${prinicipalNameInCell || ''}`}
        >
          <SimpleItem
            dataField="po-number"
            editorType="dxTextBox"
            editorOptions={medPOPopupPartNoEditorOptions}
          >
            <RequiredRule message="PO No is required" />
            <Label text="PO No" alignment="left" />
          </SimpleItem>
          <SimpleItem
            dataField="date"
            editorType="dxDateBox"
            editorOptions={dateEditorOptions}
          >
            <RequiredRule message="PO Date is required" />
            <Label text="PO Date" alignment="left" />
          </SimpleItem>
          <SimpleItem
            dataField="poReviewDate"
            editorType="dxDateBox"
            editorOptions={{
              disabled: true,
              displayFormat: medicalFollowUpFormat,
            }}
          >
            <Label text="PO Review Date" alignment="left" />
          </SimpleItem>
          <SimpleItem
            dataField="poAmount"
            editorType="dxNumberBox"
            editorOptions={{
              disabled: true,
              format: amountFormat,
            }}
          >
            <Label text="PO Amount" alignment="left" />
          </SimpleItem>
          <SimpleItem
            dataField="user-id"
            editorType="dxTextBox"
            editorOptions={{
              disabled: true,
            }}
          >
            <Label text="User ID" alignment="left" />
          </SimpleItem>
          <SimpleItem
            dataField="dateTimeStamp"
            editorType="dxDateBox"
            editorOptions={{
              disabled: true,
              type: 'datetime',
              displayFormat: MedSchedulerFormat,
            }}
          >
            <Label text="Date Time Stamp" alignment="left" />
          </SimpleItem>
          <SimpleItem
            dataField="invoice-number"
            editorType="dxTextBox"
            editorOptions={{ disabled: isInvoiceDisabled }}
          >
            <Label text="Invoice No" alignment="left" />
          </SimpleItem>
          <SimpleItem
            dataField="invoice-date"
            editorType="dxDateBox"
            editorOptions={invoiceDateEditorOptions}
          >
            <Label text="Invoice Date" alignment="left" />
          </SimpleItem>
        </GroupItem>
        <GroupItem colCount={4} cssClass="p-dialog-footer popup text-right">
          <Item itemType="empty" colSpan={2} />
          <ButtonItem
            horizontalAlignment="right"
            colSpan={1}
            buttonOptions={{
              text: 'Save',
              type: 'success',
              useSubmitBehavior: true,
              disabled: saveDisable,
            }}
            cssClass="p-0"
          />
          <ButtonItem
            horizontalAlignment="right"
            colSpan={1}
            buttonOptions={{
              text: 'Cancel',
              type: 'danger',
              onClick: handleCancel,
            }}
            cssClass="p-0"
          />
        </GroupItem>
      </Form>
    </form>
  );
});

export default RenderPOPopup;
