import { Scheduler } from 'devextreme-react';
import { convertDateFormat } from 'src/utils';

const onAppointmentRendered = (e) => {
  const width = e.element.querySelector(
    '.dx-scheduler-date-table-cell'
  ).clientWidth;
  e.appointmentElement.style.width = `${width}px`;
};

const customizeDateNavigatorText = (e) => {
  const formattedDate = convertDateFormat(e.startDate, 'D MMM YYYY - ddd');
  return `${formattedDate}`;
};

const newDate = new Date();

const MedicalScheduler = ({
  schedulerRef,
  medicalSource,
  currentDate,
  timeZone,
  onValueChanged,
}) => {
  return (
    <Scheduler
      ref={schedulerRef}
      timeZone={timeZone}
      id="scheduler"
      dataSource={medicalSource}
      currentDate={currentDate}
      defaultCurrentDate={newDate}
      defaultCurrentView="day"
      startDayHour={5}
      showCurrentTimeIndicator={false}
      min={newDate}
      height={600}
      showAllDayPanel={false}
      customizeDateNavigatorText={customizeDateNavigatorText}
      onAppointmentRendered={onAppointmentRendered}
      onCellClick={onValueChanged}
      onAppointmentUpdating={onValueChanged}
      editing={true}
      adaptivityEnabled={false}
    />
  );
};

export default MedicalScheduler;
