import React, { useState } from 'react';
import DataGrid, {
  Column,
  FilterRow,
  Paging,
  Scrolling,
  Selection,
} from 'devextreme-react/data-grid';
import DropDownBox from 'devextreme-react/drop-down-box';
import TextBox from 'devextreme/ui/text_box';
const dropDownOptions = { width: 500 };
function gridOpened(e) {
  setTimeout(function () {
    TextBox.getInstance(
      document.querySelector('.filter-row-focus .dx-textbox')
    ).focus();
  }, 0);
}
function dataGridCellPrepared(e) {
  if (e.rowType === 'filter' && e.columnIndex === 0) {
    e.cellElement.classList.add('filter-row-focus');
  }
}
const DropDownBoxComponent = (props) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([props.data.value]);
  const [isDropDownOpened, setIsDropDownOpened] = useState(false);

  const boxOptionChanged = (e) => {
    if (e.name === 'opened') {
      setIsDropDownOpened(e.value);
    }
  };
  const contentRender = () => {
    return (
      <DataGrid
        dataSource={props.data.column.lookup.dataSource}
        remoteOperations={true}
        height={250}
        selectedRowKeys={selectedRowKeys[0] != 0 && selectedRowKeys}
        hoverStateEnabled={true}
        onSelectionChanged={onSelectionChanged}
        focusedRowEnabled={true}
        defaultFocusedRowKey={selectedRowKeys[0]}
        onCellPrepared={dataGridCellPrepared}
      >
        <FilterRow visible={true} />
        {props.columns.map((col) => (
          <Column key={col.dataField} {...col} />
        ))}
        <Paging enabled={true} defaultPageSize={10} />
        <Scrolling mode="virtual" />
        <Selection mode="single" />
      </DataGrid>
    );
  };

  const onSelectionChanged = (selectionChangedArgs) => {
    setSelectedRowKeys(selectionChangedArgs.selectedRowKeys);
    setIsDropDownOpened(false);
    props.data.setValue(selectionChangedArgs.selectedRowKeys[0]);
  };

  return (
    <DropDownBox
      onOptionChanged={boxOptionChanged}
      opened={isDropDownOpened}
      dropDownOptions={dropDownOptions}
      dataSource={props.data.column.lookup.dataSource}
      value={selectedRowKeys[0]}
      displayExpr={props.name}
      valueExpr={props.id}
      contentRender={contentRender}
      onOpened={gridOpened}
    />
  );
};

export default DropDownBoxComponent;
