import { getPdfTemplate } from '@services/api';
import { useCallback, useEffect, useState } from 'react';

const {
  Popup,
  TextArea,
  TextBox,
  Button,
  SelectBox,
} = require('devextreme-react');

const MailTemplate = ({
  showPopup,
  hidePopup,
  mailData,
  setMailData,
  sendMedicalSO,
}) => {
  const [templateList, setTemplateList] = useState([]);
  const [disableSend, setDisableSend] = useState(false);
  const [isValidate, setIsValidate] = useState({
    template: false,
  });

  useEffect(() => {
    const fetchTemplate = async () => {
      const pdfTemplate = await (await getPdfTemplate()).data;
      setTemplateList(
        pdfTemplate.filter((item) => item.salesOrder == 1 || item.medical == 1)
      );
    };
    if (showPopup) fetchTemplate();
  }, [showPopup]);

  const setNoValidate = () => {
    setIsValidate({ template: false });
  };

  useEffect(() => {
    setNoValidate();
    setDisableSend(false);
  }, [showPopup]);

  const onInputChange = useCallback((e, name) => {
    setNoValidate();
    setMailData((prevData) => ({
      ...prevData,
      data: { ...prevData.data, [name]: e },
    }));
  }, []);

  const clickSendMail = () => {
    if (mailData.data.template == '') {
      setIsValidate({
        template: mailData.data.template == '' ? true : false,
      });
    } else {
      setDisableSend(true);
      sendMedicalSO();
    }
  };

  const contactDialogFooter = (
    <>
      <Button
        type="default"
        text="Send Mail"
        icon="pi pi-check"
        className="bg-primary mr-2"
        onClick={clickSendMail}
        disabled={disableSend}
      />
      <Button
        type="danger"
        text="Cancel"
        icon="pi pi-times"
        className="bg-error mr-2"
        onClick={hidePopup}
      />
    </>
  );

  const renderPopup = () => {
    return (
      <>
        <div className="mt-2 mb-7">
          <div className="formgrid grid m-0">
            <div className="field col">
              <label htmlFor="subject">Subject</label>
              <br />
              <TextBox
                value={mailData.data.subject}
                // ref={}
                onValueChanged={(e) => onInputChange(e.value, 'subject')}
                // onKeyUp={() => {
                //   setIsValidate(false);
                // }}
                maxLength={200}
                placeholder="Enter Subject"
                required={true}
                minHeight="60px"
                autoResizeEnabled={true}
                // className={isValidate ? 'border-red-500' : ''}
                name="subject"
                // valueChangeEvent="keyup"
              />
            </div>
          </div>
          <div className="formgrid grid m-0">
            <div className="field col">
              <label htmlFor="content">Content</label>
              <br />
              <TextArea
                value={mailData.data.content}
                // ref={}
                onValueChanged={(e) => onInputChange(e.value, 'content')}
                // onKeyUp={() => {
                //   setIsValidate(false);
                // }}
                maxLength={200}
                placeholder="Enter Content"
                required={true}
                minHeight="80px"
                autoResizeEnabled={true}
                // className={isValidate ? 'border-red-500' : ''}
                name="content"
                // valueChangeEvent="keyup"
              />
            </div>
          </div>
          <div className="formgrid grid m-0">
            <div className="field col">
              <label htmlFor="template">Sales order template</label>
              <SelectBox
                placeholder="Select template"
                dataSource={templateList}
                displayExpr="templateName"
                value={mailData.data.template}
                valueExpr="templateId"
                searchEnabled={true}
                searchMode="contains"
                searchExpr="templateName"
                searchTimeout={200}
                minSearchLength={0}
                showDataBeforeSearch={true}
                onValueChanged={(e) => onInputChange(e.value, 'template')}
                focusStateEnabled={false}
                name="pdfTemplate"
                className={isValidate.template ? 'mt-1 border-red-500' : 'mt-1'}
              />
              {isValidate.template && (
                <div className="dx-item dx-validationsummary-item mt-2">
                  <div className="dx-item-content dx-validationsummary-item-content">
                    Please select template
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="p-dialog-footer popup text-right">
            {contactDialogFooter}
          </div>
        </div>
      </>
    );
  };

  return (
    <Popup
      visible={showPopup}
      height="auto"
      width={500}
      title="Send mail format"
      dragEnabled={false}
      onHiding={hidePopup}
      contentComponent={renderPopup}
      showCloseButton={true}
      // onShown={onPOPoupShown}
    />
  );
};

export default MailTemplate;
