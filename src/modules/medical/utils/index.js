import {
  divisionPrincipals,
  getFacilities,
  getPersonCompanies,
  getSalesPersons,
} from '@services/api';
import CustomStore from 'devextreme/data/custom_store';
import Form1 from 'devextreme/ui/form';
import {
  convertDateFormat,
  getTodaysDateTz,
  getTodaysDtmPlusN,
  isDay1SameOrBeforeDay2,
} from 'src/utils';

export function handleErrors(response) {
  if (response.status !== 200 && response.status !== 201) {
    throw Error(response.statusText);
  }
  return response.data;
}

export function handleYoxelErrors(response) {
  if (response.status !== 200 && response.status !== 201) {
    throw Error(response.status['status-text']);
  }
  return response.data;
}

export function createCustomStore(key, params, loadFunction) {
  return new CustomStore({
    key,
    loadMode: 'raw',
    load: () => {
      return loadFunction(...params)
        .then(handleErrors)
        .catch(() => {
          return [];
        });
    },
  });
}

export function debounce(func, delay) {
  let timer;

  return function (...args) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
}

export const onRowExpanding = (e) => e.component.collapseAll(-1);

// ---------------------------------------------
//        Medical Master Datagrid Specific
// ---------------------------------------------

export const MedDescEditorOptions = { maxLength: 120 };
export const MedCaseNoEditorOptions = { maxLength: 40 };
export const MedDateEditorOptions = {
  useMaskBehavior: true,
  // dateSerializationFormat: 'yyyy-MM-dd',
};
export const MedFacilityEditorOptions = {
  itemTemplate: 'facilityItemRender',
  minSearchLength: 3,
  showDropDownButton: false,
  openOnFieldClick: false,
};
export const MedDoctorEditorOptions = {
  itemTemplate: 'doctorItemRender',
  minSearchLength: 3,
  showDropDownButton: false,
  openOnFieldClick: false,
};

export const facilities = createCustomStore('recId', [], getFacilities);
export const personCompanies = createCustomStore('id', [], getPersonCompanies);
export const salesPersons = createCustomStore('userId', [], getSalesPersons);
export const manufacturers = createCustomStore('recId', [], divisionPrincipals);

export const facilityColumns = (data) => [
  { title: 'Comp Name', value: data?.compName },
  { title: 'City', value: data?.city },
  { title: 'State', value: data?.state },
];
export const doctorColumns = (data) => [
  { title: 'Name', value: data?.name },
  { title: 'City', value: data?.city },
  { title: 'State', value: data?.state },
];
export const onCellPrepared = (e) => {
  switch (e.rowType) {
    case 'header':
      e.cellElement.classList.add('text-center');
      break;
    case 'data':
      if (!e.column.allowEditing) {
        e.cellElement.style.pointerEvents = 'auto';
        e.cellElement.style.cursor = 'not-allowed';
      }
      // This is only for Line item grid which is present
      // in stage 4 popup for vertically aligning signature
      // Note: 3 - for column signature if columns rearranged
      // this needs to be changed
      if (e.element.id == 'medSignatureGrid') {
        if (e.rowType !== 'data' || e.columnIndex != 3) {
          return;
        }
        const rowCount = e.component.getVisibleRows().length;
        if (e.rowIndex % rowCount === 0) {
          e.cellElement.rowSpan = rowCount;
        } else {
          e.cellElement.style.display = 'none';
        }
      }
      if (e.element.id == 'medicalGrid')
        if (e.column.dataField === 'medicalScheduled') {
          const btnClass = e.cellElement.className;
          e.cellElement.className =
            e.data.count > 0
              ? `${btnClass} dx-state-disabled`
              : `${btnClass} dx-state-enabled`;
          e.watch(
            function () {
              return e.data.count;
            },
            function () {
              e.cellElement.className =
                e.data.count > 0
                  ? `${btnClass} dx-state-disabled`
                  : `${btnClass} dx-state-enabled`;
            }
          );
        }
  }
};

export const onMedicalGridEditorPreparing = (e) => {
  if (e.dataField === 'medicalFollowUp' && e.parentType === 'dataRow') {
    e.editorOptions.max = e.row.data.medicalScheduled;
    e.editorOptions.min = getTodaysDateTz();
  }
  if (e.dataField === 'medicalScheduled' && e.parentType === 'dataRow') {
    e.editorOptions.min = getTodaysDtmPlusN(0);
  }
  if (e.parentType === 'dataRow') {
    e.editorOptions.disabled =
      e.dataField !== 'medicalCloseStatus' && e.row?.data.count > 0;
  }
};
export const onRowInserted = (e) => {
  const instance = e.component;
  instance.navigateToRow(e.data.medicalId).then(() => {
    const index = instance.getRowIndexByKey(e.data.medicalId);
    instance.editCell(index, 'medicalDescription');
  });
};

export const setScheduledCellValue = (newData, value, currentRowData) => {
  if (
    isDay1SameOrBeforeDay2(
      value,
      convertDateFormat(currentRowData.medicalFollowUp, 'YYYY-MM-DD')
    )
  ) {
    newData.medicalFollowUp = convertDateFormat(value, 'YYYY-MM-DD');
  }
  newData.medicalScheduled = value;
};

export const allowUpdateMedical = (e) => e.row?.data.count < 1;

// ---------------------------------------------
//        Medical Detail Datagrid Specific
// ---------------------------------------------
export const medPOPopupPartNoEditorOptions = { maxLength: 60 };
export const MedPrinciEditorOptions = {
  itemTemplate: 'PrincipalItemRender',
  minSearchLength: 3,
  showDropDownButton: false,
  openOnFieldClick: false,
};

export const classLookup = {
  true: 'bg-green-400',
  current: 'bg-primary-600',
  false: 'bg-gray-100',
};

export const calculateMnfPrinciCellvalue = (e) =>
  e.medicalPrincipal === 0 ? null : e.medicalPrincipal;

export const setMedCellValue = (newData, value, _) =>
  (newData.medicalValue = +value);

export const dataURLToBlob = (dataURL) => {
  const arr = dataURL.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
};
export const onPOPoupShown = (e) => {
  const element = document.getElementById('medPOForm');
  const instance = Form1.getInstance(element);
  instance.getEditor('po-number').focus();
};
// ---------------------------------------------
//        Medical Line Item Nested Detail Datagrid Specific
// ---------------------------------------------
const onCustomItemCreating = (args) => {
  if (!args.text) {
    args.customItem = null;
    return;
  }
  const newItem = {
    princiPartNo: args.text.trim(),
  };
  args.customItem = newItem;
};

export const PartNoEditorOptions = {
  itemTemplate: 'SelectBoxItemRender',
  minSearchLength: 3,
  searchExpr: ['prodPartNoDesc'],
  showDropDownButton: false,
  openOnFieldClick: false,
  acceptCustomValue: true,
  onCustomItemCreating: onCustomItemCreating,
};

export function calculateMultiplier(customerPrice, medicalStandardRate) {
  const price = parseFloat(customerPrice || 0);
  const rate = parseFloat(medicalStandardRate) || 0;

  return price !== 0 && rate !== 0 ? +(price / rate).toFixed(6) : 1;
}

export function calculateExtRate(customerPrice, QntReq, uomUnits) {
  const price = parseFloat(customerPrice || 0);
  const qntReq = parseFloat(QntReq || 0);
  const units = parseFloat(uomUnits || 0);

  return qntReq !== 0 && units !== 0
    ? +((price * qntReq) / units).toFixed(6)
    : 0;
}
