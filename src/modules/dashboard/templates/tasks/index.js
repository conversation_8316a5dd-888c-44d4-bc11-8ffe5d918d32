import { useState, useEffect, forwardRef } from 'react';
import { Rating } from 'primereact/rating';
import { CheckBox } from 'devextreme-react/check-box';

import styles from './index.module.css';
import { convertDateFormat } from 'src/utils';
import SingleCardContainer from '../../single-card-container';
import { useDefaults } from '@contexts/defaults';
import {
  getDashboardTaskCardDetails,
  putDashboardTaskDone,
} from '@services/api';
const TaskCard = forwardRef(
  ({ refreshTask, onRefreshTask, customName }, ref) => {
    const {
      defaults: { EXT_BASE_PATH, DATE_FORMAT },
    } = useDefaults();
    const [loading, setLoading] = useState(false);
    const [tasks, setTasks] = useState([]);
    const [checkedState, setCheckedState] = useState([]);
    const [isDisableTaskCheck, setIsDisableTaskCheck] = useState(false);
    useEffect(() => {
      const fetchData = async () => {
        setLoading(true);
        /**
         * Tasks
         * No Param should be sent to API
         */

        const ta = await (await getDashboardTaskCardDetails()).data;
        setLoading(false);
        setTasks(ta);
        setCheckedState(new Array(ta.length).fill(false));
      };
      if (refreshTask === true) {
        onRefreshTask('');
      }
      fetchData();
    }, [refreshTask]);
    /*
  -----------------
  Fields to display
  -----------------
   * Task Date
   * Task Priority
   * Task Title - Clickable link - on clicks take you to the task page with task dialog rendered /organizer/tasks/Task.xhtml?tid=[task-id]
   * Linked AJ Title - clickable link - on clicks take you to the AJ screen -  Journal/JournalEntry.xhtml?id=[aj-id] 
          Here [aj-id] is activity journal header id
   * [checkbox] - checking will mark this as done
   */
    const onTaskCompValChanged = (id) => {
      setIsDisableTaskCheck(true);
      const taskDone = async () => {
        const taskDoneByID = await putDashboardTaskDone(id);
        if (taskDoneByID.status === 200) {
          //Remove task based on id
          setTasks(tasks.filter((task) => task.id != id));
          ref.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Task status changed',
            life: 3000,
          });
          setIsDisableTaskCheck(false);
        }
      };
      taskDone();
    };

    return (
      <>
        {loading && (
          <SingleCardContainer>{`Loading ${customName}`}</SingleCardContainer>
        )}
        {!loading && tasks.length === 0 && (
          <SingleCardContainer>No Tasks</SingleCardContainer>
        )}
        {!loading &&
          tasks.map((d, index) => {
            const { id, priority, title, ajTitle, taskDate: date } = d;
            const BASE_PATH = EXT_BASE_PATH ?? '/';
            const taskTitleLink = `/${BASE_PATH}/organizer/tasks/Task.xhtml?tid=${id}`;
            const linkedTitleLink = `/${BASE_PATH}/Journal/JournalEntry.xhtml?id=${id}`;
            return (
              <SingleCardContainer key={id}>
                <table className="w-full">
                  <tbody>
                    <tr className={styles.dashboard__task}>
                      <td className={styles.dashboard__task_date}>
                        {convertDateFormat(date, DATE_FORMAT)}
                      </td>
                      <td className={styles.dashboard__task_priority}>
                        <Rating
                          value={priority}
                          cancel={false}
                          title="Priority"
                          className={styles.dashboard__priority_star}
                          onIcon={
                            <span
                              className="p-rating-icon pi pi-star-fill text-yellow-500"
                              alt="custom-image-active"
                              width="25px"
                              height="25px"
                            />
                          }
                          offIcon={
                            <span
                              className="p-rating-icon pi pi-star-fill text-200 "
                              alt="custom-image-active"
                              width="25px"
                              height="25px"
                            />
                          }
                        />
                      </td>
                      <td className={styles.dashboard__task_title}>
                        <a
                          href={taskTitleLink}
                          className="text-blue-400"
                          target="_blank"
                        >
                          {title}
                        </a>
                      </td>
                      <td className={styles.dashboard__task_lkdAj_title}>
                        <a
                          className="text-blue-400"
                          href={linkedTitleLink}
                          target="_blank"
                        >
                          {ajTitle}
                        </a>
                      </td>
                      <td className={styles.dashboard__task_checkbox}>
                        <CheckBox
                          value={checkedState[index]}
                          onValueChanged={() => onTaskCompValChanged(id)}
                          disabled={isDisableTaskCheck}
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </SingleCardContainer>
            );
          })}
      </>
    );
  }
);

export default TaskCard;
