import { convertDateFormat, getTodayDatePlusN, getTodaysDate } from 'src/utils';
import SingleCardContainer from '../../single-card-container';
import { useDefaults } from '@contexts/defaults';
import styles from './index.module.css';
import { forwardRef, useEffect, useState } from 'react';
import { getDasboardJobsCardDetails } from '@services/api';
const startDate = getTodaysDate().format('MM-DD-YYYY');
let toDate = getTodayDatePlusN(1).format('MM-DD-YYYY');
const JobCard = forwardRef(({ followUp, customName }, ref) => {
  const {
    defaults: { EXT_BASE_PATH, DATE_FORMAT },
  } = useDefaults();
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      /**
       * Jobs
       * Todays and Tomorrows Date(MM-DD-YYYY)
       */
      if (followUp) {
        toDate = getTodayDatePlusN(7).format('MM-DD-YYYY');
      }
      const jo = await (
        await getDasboardJobsCardDetails(startDate, toDate)
      ).data;
      setLoading(false);
      if (followUp && jo.length == 0) {
        ref.current.show({
          severity: 'warn',
          summary: 'Alert',
          detail: 'No records',
          life: 3000,
        });
      }
      setJobs(jo);
    };
    fetchData();
  }, [followUp]);
  /*
  -----------------
  Fields to display
  -----------------
   * Follow Up Date
  * Principal/Customer - Topic -clickable link- on click /opploop/quotes/NewQuote.xhtml?recId=[rec-id]
   * Email Text
   */

  return (
    <>
      {loading && (
        <SingleCardContainer>{`Loading ${customName}`}</SingleCardContainer>
      )}
      {!loading &&
        jobs.map((d) => {
          const {
            jobId: id,
            jobFollowUpDate: date,
            jobDescr: title,
            jobSpecifierName: specifier,
            jobActivitiesName: stageName,
            jobValue: value,
          } = d;
          const BASE_PATH = EXT_BASE_PATH ?? '/';
          // Note: topic needs to be sent from api
          const jobTitleLink = `/${BASE_PATH}/opploop/jobs/JobsView.xhtml?id=${id}`;
          return (
            <SingleCardContainer key={id}>
              <table className={styles.dashboard__job}>
                <tbody>
                  <tr>
                    <td
                      style={{
                        width: '90px',
                      }}
                    >
                      {convertDateFormat(date, DATE_FORMAT)}
                    </td>
                    <td>
                      <a
                        className="text-blue-400"
                        href={jobTitleLink}
                        target="_blank"
                      >
                        {title}
                      </a>
                    </td>
                  </tr>
                  <tr>
                    <td
                      style={{
                        width: '90px',
                      }}
                    >
                      Specifier:
                    </td>
                    <td>{specifier}</td>
                  </tr>
                  <tr>
                    <td
                      style={{
                        width: '90px',
                      }}
                    >
                      Stage:
                    </td>
                    <td>{stageName}</td>
                  </tr>
                  <tr>
                    <td
                      style={{
                        width: '90px',
                      }}
                    >
                      Value:
                    </td>
                    <td>{value}</td>
                  </tr>
                </tbody>
              </table>
            </SingleCardContainer>
          );
        })}
    </>
  );
});

export default JobCard;
