import { useState, useEffect, forwardRef } from 'react';

import styles from './index.module.css';
import {
  addDateTimeByTZ,
  convertDateTimeFormat,
  getDateTimeByTZ,
} from 'src/utils';
import SingleCardContainer from '../../single-card-container';
import { useDefaults } from '@contexts/defaults';
import { getDashboardEventCardDetails } from '@services/api';

const EVENT_TYPE = ['Business', 'Personal'];
const EventCard = forwardRef(({ followUp }, ref) => {
  const {
    defaults: { EXT_BASE_PATH, TIME_ZONE, DATE_FORMAT, TIME_FORMAT },
  } = useDefaults();

  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (TIME_ZONE) {
      const todaysDateTime = getDateTimeByTZ(TIME_ZONE);
      let tomorDateTime = addDateTimeByTZ(TIME_ZONE, 2);
      const fetchData = async () => {
        setLoading(true);
        /**
         *  Events
         * Todays Date and todaysDate +2 (MM-DD-YYYY HH:mm:ss)(Timezone)
         */
        if (followUp) {
          tomorDateTime = addDateTimeByTZ(TIME_ZONE, 7);
        }

        const ev = await (
          await getDashboardEventCardDetails(todaysDateTime, tomorDateTime)
        ).data;
        setLoading(false);
        if (followUp && ev.length == 0) {
          ref.current.show({
            severity: 'warn',
            summary: 'Alert',
            detail: 'No records',
            life: 3000,
          });
        }
        setEvents(ev);
      };
      fetchData();
    }
  }, [followUp, TIME_ZONE]);
  /*
  -----------------
  Fields to display
  -----------------
* Event Start Date
* Event Title - Clickable link - on clicks take you to the calendar page - https://{instance}.repfabric.com/RepfabricCRM/organizer/Organizer.xhtml?event=[event-id]
* Linked AJ Title - Clickable link - on clicks take you to the  https://{instance}.repfabric.com/RepfabricCRM//Journal/JournalEntry.xhtml?id=[event-id]
--- Type: [Event Type: Business/Personal]
    --- Location: [Event Location]
 * Button: Show follow ups for next 7 days
    -- Here Event type is 1 for Business and 2 for Personal
   */

  return (
    <>
      {loading && <SingleCardContainer>Loading Events</SingleCardContainer>}
      {!loading && events.length === 0 && (
        <SingleCardContainer>No Events</SingleCardContainer>
      )}
      {!loading &&
        events.map((d) => {
          const { eventId: id, type, location, startDate, title } = d;
          const BASE_PATH = EXT_BASE_PATH ?? '/';
          const eventTitleLink = `/${BASE_PATH}/organizer/Organizer.xhtml?event=${id}`;
          return (
            <SingleCardContainer key={id}>
              <div className={styles.dashboard__event}>
                <ul className={styles.dashboard__event_ul}>
                  <li>
                    <div className="inline-flex">
                      <div className="ml-0">
                        Type: <label>{EVENT_TYPE[parseInt(type) - 1]}</label>
                      </div>
                      <div className="ml-2">
                        Location:
                        <label className={styles.dashboard__event_ul_list}>
                          {location}
                        </label>
                      </div>
                    </div>
                  </li>
                </ul>

                <table>
                  <tbody>
                    <tr className={styles.dashboard__event_table}>
                      <td className={styles.dashboard__event_table_date}>
                        {convertDateTimeFormat(
                          startDate,
                          DATE_FORMAT,
                          TIME_FORMAT
                        )}
                      </td>
                      <td className={styles.dashboard__event_table_title}>
                        <a
                          className="text-blue-400"
                          href={eventTitleLink}
                          target="_blank"
                        >
                          {title}
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </SingleCardContainer>
          );
        })}
    </>
  );
});

export default EventCard;
