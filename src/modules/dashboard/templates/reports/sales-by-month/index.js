import { forwardRef, useCallback, useEffect, useRef, useState } from 'react';

import { Button, SelectBox } from 'devextreme-react';

import BarChart from '@components/charts/bar';
import GridBox from '@components/dropdownTagBox/GridBox';
import {
  getDashboardRptSalesByMth,
  getSalesTeam,
  getUserReportFilter,
  postUserReportFilter,
} from '@services/api';
import { getCustomValBasedOnLabel, getTodaysDate } from 'src/utils';
import styles from './index.module.css';
import { useMenu } from '@contexts/menu';
import { useDefaults } from '@contexts/defaults';
const currentYear = parseInt(
  getTodaysDate().format('MM-DD-YYYY').split('-')[2]
);
const years = [currentYear, currentYear - 1, currentYear - 2];
const selectionFilter = ['SELECTED', '=', 1];
const ReportChart = forwardRef((props, ref) => {
  const {
    user: { userId },
  } = useMenu();
  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();
  const chartRef = useRef(null);
  chartRef?.current?.instance.option(
    'loadingIndicator.text',
    'Loading Sales By Month...'
  );
  chartRef?.current?.instance.option('loadingIndicator.font.size', '20');
  const customSalesTeam = getCustomValBasedOnLabel(CUSTOM_LABELS, [
    'IDS_SALES_TEAM',
  ]).toString();
  const SALES_TEAM_COLUMNS = [
    {
      dataField: 'SMAN_NAME',
      caption: customSalesTeam,
      filterOperations: ['contains'],
    },
  ];
  const [reports, setReports] = useState(null);

  const [salesTeamList, setSalesTeamList] = useState([]);
  const [selectedSalesTeam, setSelectedSalesTeam] = useState([]);
  const [isReady, setIsReady] = useState(false);
  const [param, setParam] = useState({
    year: years[0],
    firstTitle: years[0],
    secondTitle: years[0] - 1,
  });
  useEffect(() => {
    chartRef?.current?.instance.showLoadingIndicator();
    const fetchReports = async () => {
      const salesTeamIds = selectedSalesTeam
        .map((team) => team.SMAN_ID)
        .toString();
      const reportListByYear = await (
        await getDashboardRptSalesByMth(param.year, salesTeamIds)
      ).data.map((report) => {
        return {
          ...report,
          total: parseInt(report.total),
          prevTotal: parseInt(report.prevTotal),
        };
      });
      if (reportListByYear.length) {
        setReports(reportListByYear);
      }
    };
    fetchReports();
  }, [param, selectedSalesTeam]);
  useEffect(() => {
    const fetchSalesTeam = async () => {
      const getSelectedUserReportFilters = await (
        await getUserReportFilter(userId, 'INDEX_SALES_BY_MONTH')
      ).data;
      const salesteam = await (await getSalesTeam()).data;
      if (getSelectedUserReportFilters.length > 0) {
        const getPreSelectedSalesTeam = getSelectedUserReportFilters
          .find((userRptFilter) => userRptFilter.rptFilterId === 'SALESTEAMS')
          .rptFilterValue.split(',')
          .map(Number);
        const modiFiedSalesTeamList = salesteam.map((salesTeam) => {
          if (getPreSelectedSalesTeam.includes(salesTeam.SMAN_ID)) {
            return { ...salesTeam, SELECTED: 1 };
          } else {
            return { ...salesTeam, SELECTED: 0 };
          }
        });
        setSalesTeamList(modiFiedSalesTeamList);
        setSelectedSalesTeam(
          modiFiedSalesTeamList.filter((salesTeam) => salesTeam.SELECTED === 1)
        );
      } else {
        setSalesTeamList(salesteam);
      }
      setIsReady(true);
    };
    fetchSalesTeam();
  }, []);
  useEffect(() => {
    if (isReady) {
      const fetchReports = async () => {
        const salesTeamIds = selectedSalesTeam
          .map((team) => team.SMAN_ID)
          .toString();
        const reportListByYear = await (
          await getDashboardRptSalesByMth(param.year, salesTeamIds)
        ).data.map((reportNew) => {
          return {
            ...reportNew,
            total: parseInt(reportNew.total),
            prevTotal: parseInt(reportNew.prevTotal),
          };
        });
        setReports(reportListByYear);
      };

      fetchReports();
    }
  }, [param, selectedSalesTeam, isReady]);
  function handleYearChange(e) {
    setParam((prev) => ({
      ...prev,
      year: e.value,
      firstTitle: e.value,
      secondTitle: e.value - 1,
    }));
  }
  const handleSaveFilter = useCallback(async () => {
    const rptFilterValue = selectedSalesTeam
      .map((salesTeam) => salesTeam.SMAN_ID)
      .toString();
    const reportParam = [
      {
        rptUserId: userId,
        rptId: 'INDEX_SALES_BY_MONTH',
        rptFilterId: 'SALESTEAMS',
        rptFilterValue,
        rptFilterFlag: 0,
      },
    ];
    try {
      const SAVE_USER_REPORT_FILTER = await postUserReportFilter(reportParam);
      if (SAVE_USER_REPORT_FILTER.status === 200) {
        ref.current.show({
          severity: 'success',
          summary: 'Saved',
          detail: 'Filter saved successfully',
          life: 3000,
        });
      }
    } catch (error) {
      throw new Error('Failed: Save Filter');
    }
  }, [selectedSalesTeam, param]);
  const renderChart = () => {
    return (
      <div className="grid w-full h-full">
        <div className={styles.dashboard__report_input_container}>
          <div className="col-2 p-0">
            <div className="w-9">
              <strong>Year</strong>
              <SelectBox
                dataSource={years}
                onValueChanged={handleYearChange}
                value={param.year}
              />
            </div>
          </div>
          <div
            className={`${styles.dashboard__report_input_sales} col-6 w-5 p-0`}
          >
            <strong>{customSalesTeam}</strong>
            <GridBox
              gridDataSource={salesTeamList}
              gridColumns={SALES_TEAM_COLUMNS}
              boxName={customSalesTeam}
              gridBoxValue={selectedSalesTeam}
              setGridBoxValue={setSelectedSalesTeam}
              dataKey="SMAN_ID"
              dataText="SMAN_NAME"
              defaultSelectionFilter={selectionFilter}
            />
          </div>
          <div className="col h-5rem flex align-items-center justify-content-end">
            <Button
              type="success"
              icon="save"
              hint="Save Filter"
              elementAttr={{
                id: 'dashboard__sales_by_month_save_btn',
              }}
              onClick={handleSaveFilter}
            />
          </div>
        </div>
        <div
          className={`${styles.dashboard__report_sales_chart} col-12 border-1 border-dotted`}
        >
          <BarChart
            data={reports}
            firstVal="total"
            arg="month"
            secondVal="prevTotal"
            firstTitle={param.firstTitle}
            secondTitle={param.secondTitle}
            ref={chartRef}
          />
        </div>
      </div>
    );
  };

  return renderChart();
});

export default ReportChart;
