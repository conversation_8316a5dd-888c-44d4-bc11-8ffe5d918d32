import { Button, DataGrid } from 'devextreme-react';
import HeaderFooterLayout from '@layout/page-header-footer';
import { useLayoutEffect, useMemo, useRef } from 'react';
import { dashboardGoalsRefresh, getDashboardGoals } from '@services/api';
import { useMenu } from '@contexts/menu';
import { FilterRow, LoadPanel } from 'devextreme-react/data-grid';
import CustomStore from 'devextreme/data/custom_store';
import { getTodaysDate } from 'src/utils';
const scrollingOptions = { showScrollbar: 'always', useNative: true };
function convertToIntegerIfDecimal(floatValue) {
  if (floatValue % 1 === 0 && floatValue.toString().includes('.00')) {
    return parseInt(floatValue);
  } else {
    return floatValue;
  }
}
function removeSymbols(inputString) {
  // Replace both $ and % symbols with an empty string
  return inputString.replace(/[$,%]/g, '');
}
const applyComparison = (value1, symbol, value2) => {
  switch (symbol) {
    case '>':
      return value1 > value2;
    case '>=':
      return value1 >= value2;
    case '==':
      return value1 == value2;
    case '<':
      return value1 < value2;
    case '<=':
      return value1 <= value2;
    default:
      return false;
  }
};

const onCellPrepared = (e) => {
  if (e.rowType === 'data') {
    const COLUMN_INDEX = 4;
    const orientationChecks = ['>', '>=', '==', '<', '<='];
    const totalColoumnCount = e.component.columnCount();
    if (e.columnIndex >= COLUMN_INDEX) {
      for (let i = COLUMN_INDEX; i < totalColoumnCount; i++) {
        if (e.column.dataField === e.component.columnOption(i, 'dataField')) {
          if (
            e.data.goalOrientation >= 0 &&
            e.data.goalOrientation < orientationChecks.length
          ) {
            e.cellElement.style.color = 'red';
            const comparisonSymbol = orientationChecks[e.data.goalOrientation];
            const columnDataFieldValue = removeSymbols(
              e.data[e.component.columnOption(i, 'dataField')] ?? ''
            );
            let condition = applyComparison(
              parseFloat(columnDataFieldValue) || 0,
              comparisonSymbol,
              parseFloat(removeSymbols(e.row.data.goal ?? '')) || 0
            );
            // Note: As per JSF team instruc, 5 and 6 goal opportunities display
            // as black (normally)
            if (condition) {
              e.cellElement.style.color = 'green';
            } else {
              e.cellElement.style.color = 'red';
            }
          } else {
            e.cellElement.style.color = 'black';
          }
        }
      }
    }
  }
};
const customizeGoalGridColumns = (col) => {
  if (col.length) {
    for (const [index, element] of Object.entries(col)) {
      // First 4 columns are fixed
      // if (Number(index) < 4) {
      //   element.fixed = true;
      // }

      element.alignment = 'center';
      element.dataType = 'string';
    }

    //Parent Goal
    col[1].caption = 'Parent Goal';
    // Goal Orientation(Hidden in UI but required value for color)
    col[2].visible = false;
    // Goal
    col[3].width = 130;
    //Total
    col[4].width = 130;
  }
};
const getRestField = (arr) => {
  let total = 0.0;
  const formatter = new Intl.NumberFormat('en-US');
  return arr.map((g) => {
    const SUM = parseFloat([g.goalResult ?? 0]);
    total += SUM;
    return {
      goal:
        g.goalUOM == 'PERCENTAGE'
          ? `${convertToIntegerIfDecimal(
              formatter.format(parseFloat([g.goalBaseGoal] ?? 0).toFixed(2))
            )}%`
          : g.goalUOM == 'CURRENCY'
          ? '$' +
            formatter.format(
              convertToIntegerIfDecimal(Math.round([g.goalBaseGoal] ?? 0))
            )
          : formatter.format(
              convertToIntegerIfDecimal(Math.round([g.goalBaseGoal] ?? 0))
            ),
      total:
        g.goalUOM == 'PERCENTAGE'
          ? `${formatter.format(
              convertToIntegerIfDecimal(parseFloat(total ?? 0).toFixed(2))
            )}%`
          : g.goalUOM == 'CURRENCY'
          ? '$' +
            formatter.format(convertToIntegerIfDecimal(Math.round(total ?? 0)))
          : formatter.format(convertToIntegerIfDecimal(Math.round(total ?? 0))),
      [g.goalFrequency]:
        g.goalUOM == 'PERCENTAGE'
          ? formatter.format(
              convertToIntegerIfDecimal(
                parseFloat(g.goalResult ?? 0).toFixed(2)
              )
            )
          : formatter.format(
              convertToIntegerIfDecimal(Math.round(g.goalResult ?? 0))
            ),
    };
  });
};
const GoalsGrid = ({ goalPeriod }) => {
  const refreshBtnAttr = {
    id: `${goalPeriod === 3 ? 'montlhly' : 'weekly'}GoalReCalcBtn`,
    class: 'refreshBtn',
    style: 'position: fixed; top: 14px; left: 150px',
  };
  const {
    user: { userId },
  } = useMenu();
  const goalGridRef = useRef(null);
  useLayoutEffect(() => {
    if (goalGridRef?.current) {
      goalGridRef.current?.instance.updateDimensions();
    }
  });
  const goals = useMemo(
    () =>
      new CustomStore({
        load: async () => {
          const GOALLIST = await (
            await getDashboardGoals(goalPeriod, userId)
          ).data;
          const modifiedArr = GOALLIST.map((a) => {
            return { ...a, goalIdKey: `e${a.goalId}` };
          });
          const GOALS = modifiedArr.reduce((acc, cur) => {
            acc[cur.goalIdKey] = [...(acc[cur.goalIdKey] || []), cur];
            return acc;
          }, {});

          return Object.values(GOALS).map((key) => {
            const { goalName, goalParent, goalOrientation } = key.find(
              (goal) => goal.goalId
            );
            const id = {
              goalName,
              goalParent,
              goalOrientation,
            };
            return Object.assign({}, id, ...getRestField(key));
          });
        },
      }),
    []
  );

  const refreshGrid = () => {
    const refreshGoals = async () => {
      try {
        const REFRESH_GOALS = await dashboardGoalsRefresh(
          goalPeriod,
          userId,
          getTodaysDate().format('MM-DD-YYYY')
        );
        if (REFRESH_GOALS.status === 200) {
          goalGridRef.current.instance.refresh(true);
        }
      } catch (error) {}
    };
    refreshGoals();
  };
  return (
    <>
      <Button
        icon="refresh"
        onClick={refreshGrid}
        hint="Recalculate"
        type="default"
        elementAttr={refreshBtnAttr}
      />

      <DataGrid
        id="gridContainer"
        dataSource={goals}
        allowColumnResizing={true}
        columnAutoWidth={true}
        showBorders={true}
        rowAlternationEnabled={true}
        scrolling={scrollingOptions}
        onCellPrepared={onCellPrepared}
        customizeColumns={customizeGoalGridColumns}
        ref={goalGridRef}
        noDataText={`No ${goalPeriod === 3 ? 'Monthly' : 'Weekly'} Goal Found`}
      >
        <FilterRow visible={true} />
        <LoadPanel
          text={`Loading ${goalPeriod === 3 ? 'Monthly' : 'Weekly'} Goal...`}
          shading={true}
        />
      </DataGrid>
      <style global jsx>{`
        .refreshBtn .dx-icon {
          width: 14px;
          height: 12px;
          background-size: 14px 14px;
          font-size: 14px;
          line-height: 12px;
        }
      `}</style>
    </>
  );
};
GoalsGrid.layout = HeaderFooterLayout;
GoalsGrid.defaultProps = {
  //goalPeriod = 3 for month 4 for week
  goalPeriod: 4,
};
export default GoalsGrid;
