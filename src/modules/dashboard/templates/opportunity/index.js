import {
  convertDateFormat,
  getCustomValBasedOnLabel,
  getTodayDatePlusN,
  getTodaysDate,
} from 'src/utils';
import SingleCardContainer from '../../single-card-container';
import { useDefaults } from '@contexts/defaults';
import styles from './index.module.css';
import { forwardRef, useEffect, useState } from 'react';
import { getDashboardOpprCardDetails } from '@services/api';
import DashboardOppLinkTemplate from './link-template';
const startDate = getTodaysDate().format('MM-DD-YYYY');
let toDate = getTodayDatePlusN(1).format('MM-DD-YYYY');
const OppCard = forwardRef(
  ({ customUserParams, customName, followUp, onChangeTitle }, ref) => {
    const {
      defaults: { EXT_BASE_PATH, CUSTOM_LABELS, DATE_FORMAT },
    } = useDefaults();
    const [customLabel] = getCustomValBasedOnLabel(CUSTOM_LABELS, [
      'IDS_SALES_TEAM',
    ]);
    const [opportunities, setOpportunities] = useState([]);
    const [opType, setOpType] = useState(0);
    const [loading, setLoading] = useState(false);
    useEffect(() => {
      const fetchData = async () => {
        setLoading(true);
        /**
         * Opportunities
         * Todays Date and Tomorrows Date(MM-DD-YYYY)
         */
        if (followUp) {
          toDate = getTodayDatePlusN(7).format('MM-DD-YYYY');
        }

        onChangeTitle('IDS_OPPS', opType, customLabel);
        const op = await (
          await getDashboardOpprCardDetails(startDate, toDate, opType)
        ).data;
        setLoading(false);
        if (followUp && op.length == 0) {
          ref.current.show({
            severity: 'warn',
            summary: 'Alert',
            detail: 'No records',
            life: 3000,
          });
        }
        setOpportunities(op);
      };
      fetchData();
    }, [followUp, opType]);
    const DISABLE_SALES_TEAM_BTN =
      customUserParams.find(
        (param) => param.userParamId == 'SHOW_SMAN_OPP_FOLLOWUPS'
      )?.userParamStatus === 1;
    const [NO_FLLUP_MSG, STAGE_NAME, NEXT_STEP_NAME] = getCustomValBasedOnLabel(
      CUSTOM_LABELS,
      ['IDS_FOLLOW_UPS', 'IDS_ACTIVITY', 'IDS_NEXT_STEP']
    );
    /*
  -----------------
  Fields to display
  -----------------
   * Follow Up Date
  * Customer / Principal- CustomerProgram -clickable link- on click /opploop/quotes/NewQuote.xhtml?recId=[rec-id]
   * Email Text
   */

    return (
      <>
        <DashboardOppLinkTemplate
          disableSales={DISABLE_SALES_TEAM_BTN}
          opType={opType}
          customName={customName}
          setOpType={setOpType}
        />
        {loading && (
          <SingleCardContainer>{`Loading ${customName}`}</SingleCardContainer>
        )}
        {!loading && opportunities.length === 0 && (
          <SingleCardContainer>{`No ${NO_FLLUP_MSG}`}</SingleCardContainer>
        )}
        {!loading &&
          opportunities.map((d) => {
            const {
              oppId: id,
              oppFollowUp: FllUpDate,
              customer: Customer,
              principal: Principal,
              oppCustProgram: CustProgram,
              oppActivity: stage,
              oppStatus: status,
              oppNextStep: nextStep,
            } = d;
            const BASE_PATH = EXT_BASE_PATH ?? '/';
            const oppTitle = `${Customer} / ${Principal} - ${CustProgram}`;
            const oppTitleLink = `/${BASE_PATH}/opploop/opportunity/OpportunityView.xhtml?opid=${id}`;
            return (
              <SingleCardContainer key={id}>
                <div className={styles.dashboard__opp}>
                  <div className="col-2">
                    {convertDateFormat(FllUpDate, DATE_FORMAT)}
                  </div>

                  <div className="col-10">
                    <a
                      className="text-blue-400"
                      href={oppTitleLink}
                      target="_blank"
                    >
                      {oppTitle}
                    </a>
                  </div>

                  <ul
                    style={{
                      listStylePosition: 'inside',
                      paddingLeft: '1rem',
                    }}
                  >
                    <li>
                      {STAGE_NAME}.: <label className="pr-2">{stage}</label>
                      Status: <label className="pr-2">{status}</label>
                      {NEXT_STEP_NAME}.: <label>{nextStep}</label>
                    </li>
                  </ul>
                </div>
              </SingleCardContainer>
            );
          })}
      </>
    );
  }
);

export default OppCard;
