import { useDefaults } from '@contexts/defaults';
import { getCustomValBasedOnLabel } from 'src/utils';

const DashboardOppLinkTemplate = ({
  disableSales,
  opType,
  customName,
  setOpType,
}) => {
  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();
  const [salesTeamCustom] = getCustomValBasedOnLabel(CUSTOM_LABELS, [
    'IDS_SALES_TEAM',
  ]);
  return (
    <div className={`${disableSales ? 'grid' : 'hidden'}`}>
      <div className="col-4 col-offset-8 flex justify-content-between">
        <a
          className={`cursor-pointer ${opType == 0 ? '' : 'text-blue-400'}`}
          onClick={() => setOpType(0)}
        >
          My {customName}
        </a>
        <a
          className={`cursor-pointer ${opType == 1 ? '' : 'text-blue-400'}`}
          onClick={() => setOpType(1)}
        >
          {salesTeamCustom}
        </a>
      </div>
    </div>
  );
};

export default DashboardOppLinkTemplate;
