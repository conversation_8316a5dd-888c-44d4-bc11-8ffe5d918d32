import {
  convertDateFormat,
  getCustomValBasedOnLabel,
  getTodayDatePlusN,
  getTodaysDate,
} from 'src/utils';
import SingleCardContainer from '../../single-card-container';
import { useDefaults } from '@contexts/defaults';
import styles from './index.module.css';
import { useEffect, useState } from 'react';
import { getDashboardQuotesCardDetails } from '@services/api';
const startDate = getTodaysDate().format('MM-DD-YYYY');
const toDate = getTodayDatePlusN(1).format('MM-DD-YYYY');
const QuoteCard = ({ onChangeTitle }) => {
  const {
    defaults: { EXT_BASE_PATH, CUSTOM_LABELS, DATE_FORMAT },
  } = useDefaults();
  const [quotes, setQuotes] = useState([]);
  const [type, setType] = useState(true);
  useEffect(() => {
    const fetchData = async () => {
      /**
       * Quotes
       * Todays and Tomorrows Date(MM-DD-YYYY)
       */

      const qt = await (
        await getDashboardQuotesCardDetails(startDate, toDate)
      ).data;
      setQuotes(qt);
    };
    fetchData();
  }, []);

  /*
  -----------------
  Fields to display
  -----------------
   * Follow Up Date
  * Principal/Customer - Topic -clickable link- on click /opploop/quotes/NewQuote.xhtml?recId=[rec-id]
   * Email Text
   * Please Note: There is no empty message to display
   */
  const [customLabel] = getCustomValBasedOnLabel(CUSTOM_LABELS, [
    'IDS_SALES_TEAM',
  ]);
  const showQt = (myQt) => {
    const qtType = myQt ? 1 : 0;
    const fetchQt = async () => {
      const qt = await getDashboardQuotesCardDetails(startDate, toDate, qtType);
      if (qt.status === 200) {
        setType(!type);
        onChangeTitle('MY_QTS', qtType, customLabel);
        setQuotes(qt.data);
      }
    };
    fetchQt();
  };
  return (
    <>
      <div className="grid">
        <div className="col-3 col-offset-9 flex justify-content-end">
          <a
            className="cursor-pointer text-blue-400"
            onClick={() => showQt(type)}
          >
            {type ? customLabel : `My Quotes`}
          </a>
        </div>
      </div>

      {quotes.map((d) => {
        const {
          id,
          quotFollowUpDate: date,
          quotDate: qtDate,
          principal,
          customer,
          quotNumber,
          quotValue,
          quotCustProgram,
        } = d;
        const BASE_PATH = EXT_BASE_PATH ?? '/';
        const quoteTitle = `${
          principal ?? ''
        } / ${customer} - ${quotCustProgram}`;
        const quoteTitleLink = `/${BASE_PATH}/opploop/quotes/NewQuote.xhtml?recId=${id}`;
        return (
          <SingleCardContainer key={id}>
            <div className={styles.dashboard__qt}>
              <div className="col-4">
                {convertDateFormat(date, DATE_FORMAT)}
              </div>

              <div className="col-8">
                <a
                  className="text-blue-400"
                  href={quoteTitleLink}
                  target="_blank"
                >
                  {quoteTitle}
                </a>
              </div>

              <div className="col-4">Quote No.: {quotNumber}</div>
              <div className="col-4">
                Quote Date: {convertDateFormat(qtDate, DATE_FORMAT)}
              </div>
              <div className="col-4"> Quote Value.: {quotValue}</div>
            </div>
          </SingleCardContainer>
        );
      })}
    </>
  );
};

export default QuoteCard;
