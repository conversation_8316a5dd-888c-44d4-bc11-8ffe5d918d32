import { useDefaults } from '@contexts/defaults';
import {
  convertDateFormat,
  getTodayDateMinusN,
  getTodaysDate,
} from 'src/utils';
import styles from './index.module.css';
import SingleCardContainer from '../../single-card-container';
import { useEffect, useState } from 'react';
import { getDasboardPOCardDetails } from '@services/api';

const POCard = () => {
  const {
    defaults: { EXT_BASE_PATH, DATE_FORMAT },
  } = useDefaults();
  const [po, setPo] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      /**
       * Purchase Order
       * from date =Yesterday, todate=today(MM-DD-YYYY)
       */
      const yesterdaysDate = getTodayDateMinusN(1).format('MM-DD-YYYY');
      const startDate = getTodaysDate().format('MM-DD-YYYY');
      const poList = await (
        await getDasboardPOCardDetails(yesterdaysDate, startDate)
      ).data;
      setPo(poList);
    };
    fetchData();
  }, []);
  /*
  -----------------
  Fields to display
  -----------------
   * PO Date
   * Princiipal Name -Customer Name - clickable Link - on clicks take you to po page (target="_self")
    -/opploop/po/PoDetails.xhtml?id=[po-id]
    * PO No
    * Total Amt
    * Please Note: There is no empty message to display
   */

  return po.map((d) => {
    const {
      poId: id,
      poDate: date,
      poPrinci: Principal,
      poCustName: Customer,
      poProgram: topic,
      poNumber: PONum,
      poTotalPrice: TotalPrice,
    } = d;
    const BASE_PATH = EXT_BASE_PATH ?? '/';
    const poTitle = `${Principal}/${Customer}-${topic}`;
    const poLink = `/${BASE_PATH}/opploop/po/PoDetails.xhtml?id=${id}`;
    return (
      <SingleCardContainer key={id}>
        <div className={styles.dashboard__po}>
          <div className="col-2">{convertDateFormat(date, DATE_FORMAT)}</div>
          <div className="col-10">
            <a className="text-blue-400" href={poLink} target="_blank">
              {poTitle}
            </a>
          </div>
          <div className="col-2">PO No.:</div>
          <div className="col-2">{PONum}</div>
          <div className="col-2">PO Date:</div>
          <div className="col-2">{convertDateFormat(date, DATE_FORMAT)}</div>
          <div className="col-2">Total Amt.:</div>
          <div className="col-2">{TotalPrice}</div>
        </div>
      </SingleCardContainer>
    );
  });
};

export default POCard;
