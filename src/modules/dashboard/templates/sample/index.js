import {
  convertDateFormat,
  getCustomValBasedOnLabel,
  getTodayDatePlusN,
  getTodaysDate,
} from 'src/utils';
import SingleCardContainer from '../../single-card-container';
import { useDefaults } from '@contexts/defaults';
import styles from './index.module.css';
import { useEffect, useState } from 'react';
import { getDashboardSamplesCardDetails } from '@services/api';

const startDate = getTodaysDate().format('MM-DD-YYYY');
const toDate = getTodayDatePlusN(1).format('MM-DD-YYYY');

const SampleCard = ({ onChangeTitle }) => {
  const {
    defaults: { EXT_BASE_PATH, CUSTOM_LABELS, DATE_FORMAT },
  } = useDefaults();
  const [samples, setSamples] = useState([]);
  const [type, setType] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      const response = await (
        await getDashboardSamplesCardDetails(startDate, toDate)
      ).data;
      setSamples(response);
    };
    fetchData();
  }, []);

  const [customLabel, sampleLabel, sampleNumLabel, reqDateLabel] =
    getCustomValBasedOnLabel(CUSTOM_LABELS, [
      'IDS_SALES_TEAM',
      'IDS_SAMPLES',
      'IDS_SAMP_NUM',
      'IDS_REQ_DATE',
    ]);

  const showSamples = (mySamples) => {
    const sampleType = mySamples ? 1 : 0;
    const fetchSamples = async () => {
      const response = await getDashboardSamplesCardDetails(
        startDate,
        toDate,
        sampleType
      );
      if (response.status === 200) {
        setType(!type);
        onChangeTitle('IDS_SAMPLES', sampleType, customLabel);
        setSamples(response.data);
      }
    };
    fetchSamples();
  };

  return (
    <>
      <div className="grid">
        <div className="col-3 col-offset-9 flex justify-content-end">
          <a
            className="cursor-pointer text-blue-400"
            onClick={() => showSamples(type)}
          >
            {type ? customLabel : `My ${sampleLabel}`}
          </a>
        </div>
      </div>

      {samples.map((d) => {
        const {
          id,
          sampleFollowUpDate: date,
          sampleOrdDate: sampleDate,
          principal,
          customer,
          sampleOrderNumber,
          sampleShipCost,
          sampleCustProgram,
        } = d;
        const BASE_PATH = EXT_BASE_PATH ?? '/';
        const sampleTitle = `${
          principal ?? ''
        } / ${customer} - ${sampleCustProgram}`;
        const sampleTitleLink = `/${BASE_PATH}/opploop/samples/Sample.xhtml?recId=${id}`;

        return (
          <SingleCardContainer key={id}>
            <div className={styles.dashboard__sp}>
              <div className="col-4">
                {convertDateFormat(date, DATE_FORMAT)}
              </div>

              <div className="col-8">
                <a
                  className="text-blue-400"
                  href={sampleTitleLink}
                  target="_blank"
                >
                  {sampleTitle}
                </a>
              </div>

              <div className="col-4">
                {sampleNumLabel}: {sampleOrderNumber}
              </div>
              <div className="col-4">
                {reqDateLabel}: {convertDateFormat(sampleDate, DATE_FORMAT)}
              </div>
              <div className="col-4">Shipping Cost: {sampleShipCost}</div>
            </div>
          </SingleCardContainer>
        );
      })}
    </>
  );
};

export default SampleCard;
