import { useDefaults } from '@contexts/defaults';
import SingleCardContainer from '@modules/dashboard/single-card-container';
import { Button } from 'devextreme-react';
import { convertDateFormat, getCustomValBasedOnLabel } from 'src/utils';
import styles from '../index.module.css';
const DashboardAjTemplate = ({ data, onCloseFllBtnClicked }) => {
  const {
    defaults: { EXT_BASE_PATH, CUSTOM_LABELS, DATE_FORMAT },
  } = useDefaults();
  const noFollowUpMsg = getCustomValBasedOnLabel(CUSTOM_LABELS, [
    'IDS_FOLLOW_UPS',
  ]);

  return data.map((d) => {
    const { acjdRecId: id, acjdFollowup: date, acjhTitle, principalName } = d;
    const BASE_PATH = EXT_BASE_PATH ?? '/';
    const ajTitle = `${acjhTitle} - ${principalName}`;
    const ajTitleLink = `/${BASE_PATH}/Journal/JournalEntry.xhtml?id=${d.acjdHeaderId}`;
    return (
      <SingleCardContainer key={id}>
        <div className={styles.dashboard__aj}>
          <label className={styles.dashboard__aj_label_title}>
            {convertDateFormat(date, DATE_FORMAT)}
          </label>

          <a className="text-blue-400" href={ajTitleLink} target="_blank">
            {ajTitle}
          </a>
          <div className={styles.dashboard__aj_del_btn}>
            <Button
              icon="close"
              type="danger"
              hint={`Clear ${noFollowUpMsg} `}
              //     type =0 : Card
              //       type=1 :popup

              onClick={() => onCloseFllBtnClicked(id)}
            />
          </div>
          <div className={styles.dashboard__aj_body}>
            <label className={styles.dashboard__aj_body_princ}>
              {d.acjhCompanyName}
            </label>

            <label className={styles.dashboard__aj_body_cmt}>
              {d.acjdComment}
            </label>
          </div>
        </div>
      </SingleCardContainer>
    );
  });
};

export default DashboardAjTemplate;
