import Modal from '@components/modal';
import { useDefaults } from '@contexts/defaults';
import SingleCardContainer from '@modules/dashboard/single-card-container';
import { delDashboardAJ, getDashboardAJCardDetails } from '@services/api';
import { useEffect, useState } from 'react';
import {
  getCustomValBasedOnLabel,
  getTodayDatePlusN,
  getTodaysDate,
} from 'src/utils';
import DashboardAjTemplate from '../aj-template';

const startDate = getTodaysDate().format('MM-DD-YYYY');
const toDate = getTodayDatePlusN(1).format('MM-DD-YYYY');
const DashboardShowAllAJ = ({ setVisibleShowAll }) => {
  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();
  const [popupLoading, setPopupLoading] = useState(false);
  const [popupAjs, setPopupAjs] = useState([]);
  const [asc, setAsc] = useState(true);
  const [noFollowUpMsg, ajCustomName] = getCustomValBasedOnLabel(
    CUSTOM_LABELS,
    ['IDS_FOLLOW_UPS', 'IDS_ACT_JOURNALS']
  );
  const onSortBtnClicked = () => {
    setAsc(!asc);
  };
  useEffect(() => {
    const fetchAj = async () => {
      setPopupLoading(true);
      const sortType = asc ? 'ASC' : 'DESC';
      const aj = await (
        await getDashboardAJCardDetails(startDate, toDate, 1, sortType, 1)
      ).data;
      setPopupLoading(false);
      setPopupAjs(aj);
    };
    fetchAj();
  }, [asc]);
  const onClosePopupFllBtnClicked = (id) => {
    const taskDone = async () => {
      await delDashboardAJ(id).then(() => {
        setPopupAjs(popupAjs.filter((aj) => aj.acjdRecId != id));
      });
    };
    taskDone();
  };
  return (
    <Modal
      setOpen={setVisibleShowAll}
      title={`${ajCustomName} ${noFollowUpMsg}`}
      autoFocus={false}
      isExpandCollapse
      isSortReq
      onSortBtn={onSortBtnClicked}
      isSortDisabled={popupLoading}
      sortHintTitle={`Sort ${noFollowUpMsg}`}
    >
      <div
        style={{
          paddingLeft: '30px',
        }}
      >
        {popupLoading && (
          <SingleCardContainer>
            {`Loading ${ajCustomName} ${noFollowUpMsg}.....`}
          </SingleCardContainer>
        )}
        {!popupLoading && popupAjs.length === 0 && (
          <SingleCardContainer>No {noFollowUpMsg}</SingleCardContainer>
        )}
        {!popupLoading && popupAjs.length > 0 && (
          <DashboardAjTemplate
            data={popupAjs}
            onCloseFllBtnClicked={onClosePopupFllBtnClicked}
          />
        )}
      </div>
    </Modal>
  );
};

export default DashboardShowAllAJ;
