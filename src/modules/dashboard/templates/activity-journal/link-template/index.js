import { useDefaults } from '@contexts/defaults';
import { getCustomValBasedOnLabel } from 'src/utils';

const DashboardAjLinkTemplate = ({
  disablleSales,
  ajType,
  customName,
  setAjType,
  showModal,
}) => {
  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();
  const [salesTeamCustom] = getCustomValBasedOnLabel(CUSTOM_LABELS, [
    'IDS_SALES_TEAM',
  ]);
  return (
    <div className="grid">
      <div
        className={`${
          disablleSales
            ? 'col-6 col-offset-6 justify-content-between'
            : 'col-3 col-offset-9 justify-content-end'
        } flex pt-0`}
      >
        {disablleSales && (
          <>
            <a
              className={`cursor-pointer ${ajType == 0 ? '' : 'text-blue-400'}`}
              onClick={() => setAjType(0)}
            >
              {`My ${customName}`}
            </a>

            <a
              className={`cursor-pointer ${ajType == 1 ? '' : 'text-blue-400'}`}
              onClick={() => setAjType(1)}
            >
              {salesTeamCustom}
            </a>
          </>
        )}
        <a className="cursor-pointer text-blue-400" onClick={showModal}>
          Show All
        </a>
      </div>
    </div>
  );
};

export default DashboardAjLinkTemplate;
