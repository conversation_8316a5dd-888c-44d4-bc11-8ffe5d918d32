import { useState, useEffect, forwardRef } from 'react';

import SingleCardContainer from '../../single-card-container';
import { useDefaults } from '@contexts/defaults';
import { delDashboardAJ, getDashboardAJCardDetails } from '@services/api';
import {
  getCustomValBasedOnLabel,
  getTodayDatePlusN,
  getTodaysDate,
} from 'src/utils';
import DashboardAjTemplate from './aj-template';
import DashboardShowAllAJ from './show-all-popup';
import DashboardAjLinkTemplate from './link-template';

const startDate = getTodaysDate().format('MM-DD-YYYY');
let toDate = getTodayDatePlusN(1).format('MM-DD-YYYY');
const AJCard = forwardRef(
  ({ customUserParams, followUp, onChangeTitle, customName }, ref) => {
    const {
      defaults: { CUSTOM_LABELS },
    } = useDefaults();
    const [customLabel] = getCustomValBasedOnLabel(CUSTOM_LABELS, [
      'IDS_SALES_TEAM',
    ]);
    const [ajs, setAjs] = useState([]);

    const [ajType, setAjType] = useState(0);

    const [visibleShowAll, setVisibleShowAll] = useState(false);
    useEffect(() => {
      const fetchData = async () => {
        /**
         * Activity Journal
         * Todays Date and tomorrows Date (MM-DD-YYYY)
         */

        onChangeTitle('IDS_ACT_JOURNALS', ajType, customLabel);
        toDate = followUp
          ? getTodayDatePlusN(7).format('MM-DD-YYYY')
          : getTodayDatePlusN(1).format('MM-DD-YYYY');
        const aj = await (
          await getDashboardAJCardDetails(startDate, toDate, ajType, 'DESC', 0)
        ).data;
        if (followUp && aj.length == 0) {
          ref.current.show({
            severity: 'warn',
            summary: 'Alert',
            detail: 'No records',
            life: 3000,
          });
        }
        setAjs(aj);
      };
      fetchData();
    }, [ajType, followUp]);

    const DISABLE_SALES_TEAM_BTN =
      customUserParams.find(
        (param) => param.userParamId == 'SHOW_SMAN_AJ_FOLLOWUPS'
      )?.userParamStatus === 1;

    const showModal = () => {
      setVisibleShowAll(true);
    };

    const noFollowUpMsg = getCustomValBasedOnLabel(CUSTOM_LABELS, [
      'IDS_FOLLOW_UPS',
    ]);

    const onCloseFllBtnClicked = (id) => {
      const taskDone = async () => {
        await delDashboardAJ(id).then(() => {
          setAjs(ajs.filter((aj) => aj.acjdRecId != id));
        });
      };
      taskDone();
    };

    /*
  -----------------
  Fields to display
  -----------------
* Activity Journal Follow Up Date
* Activity Journal Header Title - Principal Name
* Company Name
* Comments
   */
    const ShowNoFolowUpMessage = () => {
      return (
        ajs.length === 0 && (
          <SingleCardContainer>No {noFollowUpMsg}</SingleCardContainer>
        )
      );
    };
    return (
      <>
        <DashboardAjLinkTemplate
          disablleSales={DISABLE_SALES_TEAM_BTN}
          ajType={ajType}
          customName={customName}
          setAjType={setAjType}
          showModal={showModal}
        />
        <ShowNoFolowUpMessage />
        <DashboardAjTemplate
          data={ajs}
          onCloseFllBtnClicked={onCloseFllBtnClicked}
          type={0}
        />

        {visibleShowAll && (
          <DashboardShowAllAJ setVisibleShowAll={setVisibleShowAll} />
        )}
      </>
    );
  }
);

export default AJCard;
