import { Button, Text<PERSON>rea } from 'devextreme-react';

import { convertDateTimeFormat, getTodaysDate } from 'src/utils';
import SingleCardContainer from '../../single-card-container';
import styles from './index.module.css';
import { useDefaults } from '@contexts/defaults';
import { useEffect, useState, useRef, forwardRef } from 'react';
import {
  getDashboardMsgCardDetails,
  postDasboardMsgReply,
  postDashboardMsgTodo,
  putDashboardMsgMarkAsRead,
} from '@services/api';
const removeHtmlTags = (data) => data.replace(/<\/?.+?>/gi, '');
const msgToDoBtnId = { id: 'dashboard__msg_todo' };
const msgreplybtnId = { id: 'dashboard__msg_reply' };
const msgReplyBtnId = { id: 'dashboard__msg_read' };
const MsgCard = forwardRef(({ onRefreshTask }, ref) => {
  const {
    defaults: { USERS, TIME_FORMAT, DATE_FORMAT },
  } = useDefaults();
  const textBoxRef = useRef(null);
  const [msgs, setMsgs] = useState([]);
  const [msgMode, setMsgMode] = useState([]);
  useEffect(() => {
    const fetchData = async () => {
      /**
       * Messages
       */
      const ms = await (await getDashboardMsgCardDetails()).data;
      const msObj = ms.map((msg) => ({
        [msg.id]: { disabled: false, text: '' },
      }));
      if (msObj.length > 0) {
        setMsgMode(Object.assign(...msObj));
      }

      setMsgs(ms);
    };
    fetchData();
  }, []);

  /*
  -----------------
  Fields to display
  -----------------
   * Msg Date
  * Sender Name (colored blue)
   * Email Text
   * Please Note: There is some field AJ here
   * but JSF people told to comment for now
   */

  const onMsgTodo = (msg) => {
    // remove HTML tags before sending to API
    const description = removeHtmlTags(msg);
    const msgTodo = async () => {
      const messageTodo = await postDashboardMsgTodo({
        description,
        taskDate: getTodaysDate().format('MM-DD-YYYY'),
      });
      if (messageTodo.status == 200) {
        onRefreshTask(true);
      }
    };
    msgTodo();
  };
  const onMsgReply = (messageId, msgId, messSenderId) => {
    if (msgMode[msgId].text) {
      const param = {
        messageId,
        messText: removeHtmlTags(msgMode[msgId].text),
        messSenderId,
        id: msgId,
      };
      const msgReply = async () => {
        await postDasboardMsgReply(param).then((response) => {
          const { data } = response.data;
          const { id: newId, messDate } = data[0];
          const getMsgById = msgs.find((msg) => msg.id === msgId);
          const newMsgs = [
            ...msgs.filter((msg) => msg.id != msgId),
            {
              ...getMsgById,
              messText: param.messText,
              id: newId,
              messDate: messDate,
            },
          ];
          setMsgMode((prev) => ({
            ...prev,
            [newId]: { disabled: false, text: '' },
          }));

          setMsgs(newMsgs);

          ref.current.show({
            severity: 'success',
            summary: 'Success',
            detail: `Message successfully sent`,
            life: 3000,
          });
        });
      };
      msgReply();
    }
  };
  const onMsgReplyClicked = (id) => {
    setMsgMode((prev) => ({ ...prev, [id]: { ...prev[id], disabled: true } }));
  };
  const onCancelBtnClicked = (id) => {
    setMsgMode((prev) => ({ ...prev, [id]: { ...prev[id], disabled: false } }));
  };
  const onMsgReplyChanged = (e, id) => {
    if (e.value) {
      setMsgMode((prev) => ({ ...prev, [id]: { ...prev[id], text: e.value } }));
    }
  };
  /* Note: After clicking on Mark as Read
  Msgs will be removed based on id
  */
  const onMsgMarkAsRead = (id) => {
    const msgReply = async () => {
      const msgAsRead = await putDashboardMsgMarkAsRead(id);
      if (msgAsRead.status === 200) {
        setMsgs((prev) => prev.filter((msg) => msg.id != id));
      }
    };
    msgReply();
  };
  const onTextAreaInitialized = (e) => {
    setTimeout(() => {
      e.component.focus();
    }, 0);
  };

  return (
    <>
      {msgs.length === 0 && (
        <SingleCardContainer>No Messages</SingleCardContainer>
      )}
      {msgs.map((d) => {
        const {
          id,
          messDate: date,
          messText: msg,
          messSenderId,
          messageId,
        } = d;
        const senderName = USERS.find(
          (user) => user.USER_ID === d.messSenderId
        )?.USER_NAME;
        return (
          <SingleCardContainer key={id}>
            <table className="w-full pl-5">
              <tbody>
                <tr className={styles.dashboard__msg}>
                  <td className="col-3">
                    {convertDateTimeFormat(date, DATE_FORMAT, TIME_FORMAT)}
                  </td>
                  <td className="col-5 text-primary">{senderName}</td>
                  <td className="col-3  flex justify-content-between">
                    <Button
                      icon="pi pi-star"
                      className={styles.dashboard__msg_btn}
                      type="default"
                      hint="Mark as Todo"
                      onClick={() => onMsgTodo(msg)}
                      elementAttr={msgToDoBtnId}
                    />
                    <Button
                      icon="pi pi-reply"
                      className={styles.dashboard__msg_btn}
                      type="default"
                      hint="Reply"
                      onClick={() => onMsgReplyClicked(id)}
                      disabled={msgMode[id].disabled}
                      elementAttr={msgreplybtnId}
                    />
                    <Button
                      icon="check"
                      className={styles.dashboard__msg_btn}
                      type="default"
                      hint="Mark as read"
                      onClick={() => onMsgMarkAsRead(id)}
                      elementAttr={msgReplyBtnId}
                    />
                  </td>
                  <td className="col-8 pl-5">
                    <div dangerouslySetInnerHTML={{ __html: msg }} />
                    {msgMode[id].disabled && (
                      <>
                        <TextArea
                          height={50}
                          ref={textBoxRef}
                          focusStateEnabled
                          value={msgMode[id.text]}
                          onValueChanged={(e) => onMsgReplyChanged(e, id)}
                          onInitialized={onTextAreaInitialized}
                        />
                        <div className="flex justify-content-between">
                          <Button
                            text="Submit"
                            type="success"
                            onClick={() =>
                              onMsgReply(messageId, id, messSenderId)
                            }
                          />
                          <Button
                            text="Cancel"
                            type="danger"
                            onClick={() => onCancelBtnClicked(id)}
                          />
                        </div>
                      </>
                    )}
                  </td>
                </tr>
              </tbody>
            </table>
          </SingleCardContainer>
        );
      })}
    </>
  );
});

export default MsgCard;
