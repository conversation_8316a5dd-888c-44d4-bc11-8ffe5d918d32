import { useMemo } from 'react';
import { Button } from 'devextreme-react/button';
const ReportSettingsFooter = ({
  popupTitle,
  editMode,
  onCreateUpdateBtnClick,
  type,
  onCancelBtnClicked,
}) => {
  const buttonText = useMemo(() => {
    return popupTitle;
  }, [editMode]);

  return (
    <div className="flex justify-content-between">
      <Button
        id="button"
        text={buttonText}
        type="success"
        useSubmitBehavior={true}
        onClick={onCreateUpdateBtnClick}
        disabled={!type}
        elementAttr={{ id: 'dashboard__popup_success_btn', class: 'w-5' }}
      />
      <Button
        id="button"
        text="Cancel"
        type="danger"
        onClick={onCancelBtnClicked}
        elementAttr={{ id: 'dashboard__popup_cancel_btn', class: 'w-5' }}
      />
    </div>
  );
};
export default ReportSettingsFooter;
