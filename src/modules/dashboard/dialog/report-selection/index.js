import { useMemo } from 'react';

import { SelectBox } from 'devextreme-react/select-box';
import { TextBox } from 'devextreme-react';

import { getCustomValBasedOnLabel } from 'src/utils';
import { useDefaults } from '@contexts/defaults';
const selectBoxAttr = { id: 'dashboard__settings_selectBox' };
const generateReportParam = (param) => {
  switch (param.value.includes(',')) {
    case true:
      return (
        <SelectBox
          dataSource={param.value.split(',')}
          value={param.value.split(',')[0]}
        />
      );
    case false:
      return <TextBox value={param.value} />;
  }
};
const onSelectBoxInitialized = (e) => {
  setTimeout(() => {
    e.component.focus();
  }, 0);
};
const groupedReportItems = (labels, report, reportIDs) => {
  let _report = {};
  if (reportIDs.includes(report.reportId)) {
    _report = { disabled: true };
  }
  if (report.customLabelId) {
    const customLabel = getCustomValBasedOnLabel(labels, [
      report.customLabelId,
    ]);
    if (customLabel.toString()) {
      return {
        ...report,
        name: customLabel.toString(),
        ..._report,
      };
    }
  }
  return { ...report, ..._report };
};
const ReportSettingDialog = ({
  dataList,
  userReports,
  onReportTypeValueChanged,
  editMode,
  reportType,
}) => {
  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();
  const reportIDs = userReports.map((rep) => rep.reportId);
  const popupDashboardRptItems = useMemo(
    () =>
      Object.keys(dataList).map((dashboardReportType) => ({
        key:
          dashboardReportType.toLowerCase() == 'dashboard'
            ? 'Follow Up Widget'
            : 'Chart/Report',
        items: dataList[dashboardReportType].map((report) =>
          groupedReportItems(CUSTOM_LABELS, report, reportIDs)
        ),
      })),
    []
  );
  const { type } = reportType;
  const selectBoxMaxHeight = { maxHeight: `${window.innerHeight / 3}px` };
  return (
    <form className="relative w-full">
      <div className="dx-fieldset mx-0">
        <div className="dx-field">
          <div className="dx-field-label font-bold w-6 text-color">
            Select Report Type
          </div>
          <div className="dx-field-value w-6">
            <SelectBox
              dataSource={popupDashboardRptItems}
              displayExpr="name"
              grouped={true}
              searchEnabled={true}
              placeholder="Select Report Type"
              onValueChanged={onReportTypeValueChanged}
              visible={!editMode}
              value={type}
              onInitialized={onSelectBoxInitialized}
              dropDownOptions={selectBoxMaxHeight}
              elementAttr={selectBoxAttr}
            />
            {type && (
              <TextBox
                defaultValue={
                  type.customLabelId?.includes('IDS')
                    ? getCustomValBasedOnLabel(CUSTOM_LABELS, [
                        type.customLabelId,
                      ])
                    : type.name
                }
                disabled={true}
                visible={editMode}
              />
            )}
          </div>
        </div>

        {/*
        Commented for time being
        <div className="dx-field">
          <div className="font-bold">Report Parameters</div>
        </div>

        {settings.params?.map((param, i) => (
          <div key={i} className="dx-field">
            <div className="dx-field-label">{param.name}</div>
            <div className="dx-field-value">{generateReportParam(param)}</div>
          </div>
        ))} */}
      </div>
    </form>
  );
};

export default ReportSettingDialog;
