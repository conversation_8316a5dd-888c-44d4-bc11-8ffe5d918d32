/** React and Next Js imports */
import { forwardRef, useState } from 'react';

/**Other External imports */
import RGL, { WidthProvider } from 'react-grid-layout';
const ReactGridLayout = WidthProvider(RGL);
import { DropDown<PERSON><PERSON>on, <PERSON><PERSON> } from 'devextreme-react';

import styles from './index.module.css';
import TaskCard from '../templates/tasks';
import EventCard from '../templates/events';
import AJCard from '../templates/activity-journal';
import { getCustomValBasedOnLabel } from 'src/utils';
import POCard from '../templates/purchase-order';
import MsgCard from '../templates/messages';
import QuoteCard from '../templates/quotes';
import OppCard from '../templates/opportunity';
import JobCard from '../templates/jobs';
import { useDefaults } from '@contexts/defaults';
import ReportChart from '../templates/reports/sales-by-month';
import GoalsGrid from '../templates/reports/goals';
import { CARD_SETTINGS_OPTIONS, SHOW_FOLLOW_BTN_CARD } from 'src/constants';
import SampleCard from '../templates/sample';

const CardLayout = forwardRef((props, ref) => {
  const [customTitle, setCustomTitle] = useState({
    MY_QTS: null,
    IDS_SAMPLES: null,
    IDS_OPPS: null,
    IDS_ACT_JOURNALS: null,
    IDS_SALES_TEAM: null,
  });
  const [refreshTask, setRefreshTask] = useState(false);
  const [followUp, setFollowUp] = useState(
    Object.assign(...SHOW_FOLLOW_BTN_CARD.map((card) => ({ [card]: false })))
  );
  const {
    defaults: { CUSTOM_LABELS },
  } = useDefaults();
  const onRefreshTask = (state) => {
    setRefreshTask(state);
  };
  const renderTable = (l, customUserParams, customName, flwUp) => {
    switch (l) {
      case 'IDS_TASKS':
        return (
          <TaskCard
            ref={ref}
            refreshTask={refreshTask}
            onRefreshTask={onRefreshTask}
            customName={customName}
          />
        );
      case 'EVENTS':
        return <EventCard followUp={flwUp} ref={ref} />;
      case 'IDS_ACT_JOURNALS':
        return (
          <AJCard
            customUserParams={customUserParams}
            followUp={flwUp}
            customName={customName}
            onChangeTitle={onChangeTitle}
            ref={ref}
          />
        );
      case 'IDS_PURCHASE_ORDERS':
        return <POCard />;
      case 'MSG':
        return <MsgCard ref={ref} onRefreshTask={onRefreshTask} />;
      case 'MY_QTS':
        return <QuoteCard onChangeTitle={onChangeTitle} />;
      case 'IDS_SAMPLES':
        return <SampleCard onChangeTitle={onChangeTitle} />;
      case 'IDS_OPPS':
        return (
          <OppCard
            customUserParams={customUserParams}
            customName={customName}
            followUp={flwUp}
            onChangeTitle={onChangeTitle}
            ref={ref}
          />
        );
      case 'IDS_JOBS':
        return <JobCard followUp={flwUp} customName={customName} ref={ref} />;
    }
  };
  const renderReportCard = (labelId) => {
    switch (labelId) {
      case 'SALES_BY_MONTH':
        return <ReportChart ref={ref} />;
      case 'MONTHLY_GOAL':
        return <GoalsGrid goalPeriod={3} />;
      case 'WEEKLY_GOAL':
        return <GoalsGrid goalPeriod={4} />;
    }
  };
  const onChangeTitle = (id, type, value) => {
    setCustomTitle((prev) => ({
      ...prev,
      [id]: type == 0 ? null : value,
    }));
  };

  const getCardTitle = (customLabelId, cardTitle) => {
    switch (customLabelId) {
      case 'IDS_JOBS':
        return `My ${cardTitle}`;
      case 'MY_QTS':
        return customTitle.MY_QTS ?? cardTitle;
      case 'IDS_SAMPLES':
        return !customTitle.IDS_SAMPLES
          ? `My ${cardTitle}`
          : `${customTitle.IDS_SAMPLES}`;
      case 'IDS_OPPS':
        return !customTitle.IDS_OPPS
          ? `My ${cardTitle}`
          : `${customTitle.IDS_OPPS} ${cardTitle}`;
      case 'IDS_ACT_JOURNALS':
        return !customTitle.IDS_ACT_JOURNALS
          ? `My ${cardTitle}`
          : `${customTitle.IDS_ACT_JOURNALS} ${cardTitle}`;
      default:
        return cardTitle;
    }
  };
  const showFollowUp = (labelId) => {
    setFollowUp((prev) => ({ ...prev, [labelId]: true }));
  };

  const dashboardReports = Object.values(props.reportList).flat();
  function generateDOM() {
    if (dashboardReports.length > 0) {
      return props.layout?.map((l, index) => {
        const selectedReport = props.data.find(
          (report) => report.id === parseInt(l.i)
        );
        const reportType = dashboardReports.find(
          (report) => report.reportId == selectedReport.reportId
        );
        const { name, type, customLabelId } = reportType;
        const labelId = customLabelId ?? name;
        const customLabel = getCustomValBasedOnLabel(CUSTOM_LABELS, [
          labelId,
        ]).toString();
        const cardTitle =
          props.data[index].reportCustom ?? props.data[index].report;
        const showFollowUpId = {
          id: `showFollUpBtn${
            labelId.toLowerCase().split('_')[labelId.split('_').length - 1]
          }`,
        };
        return (
          <div key={l.i} data-grid={l}>
            <div className={styles.dashboard__card_title}>
              <div className="flex align-items-center pb-1">
                <span
                  className="dx-icon-dragvertical dx-icon-custom-style cursor-move"
                  title="Drag card"
                />

                <span className="text font-bold text-lg py-2">
                  {getCardTitle(customLabelId, cardTitle)}
                </span>
              </div>
              <DropDownButton
                stylingMode="text"
                showArrowIcon={false}
                icon="more"
                keyExpr="id"
                displayExpr="name"
                dropDownOptions={{ width: '8rem' }}
                items={CARD_SETTINGS_OPTIONS}
                onItemClick={(e) => props.onCardSettingsClick(e, l)}
              />
            </div>
            {type == 'Dashboard' && (
              <div className={styles.dashboard__card_body}>
                {renderTable(
                  customLabelId,
                  props.customUserParams,
                  customLabel,
                  followUp[labelId]
                )}
                <div className={styles.dashboard__card_btn_body}>
                  <Button
                    text="Show follow ups for next 7 days"
                    hint="Show follow ups for next 7 days"
                    type="default"
                    visible={SHOW_FOLLOW_BTN_CARD.includes(labelId)}
                    onClick={() => showFollowUp(labelId)}
                    disabled={followUp[labelId]}
                    elementAttr={showFollowUpId}
                  />
                </div>
              </div>
            )}
            {type == 'Report' && (
              <div className={styles.dashboard__report}>
                {renderReportCard(customLabelId)}
              </div>
            )}
            <style jsx>
              {`
                .dx-icon-custom-style {
                  font-size: 26px;
                }
              `}
            </style>
          </div>
        );
      });
    }
  }

  return (
    <div ref={props.cardRef}>
      <ReactGridLayout
        cols={12}
        rowHeight={70}
        className="layout"
        onLayoutChange={props.onLayoutChange}
        layout={props.layout}
        draggableHandle=".dx-icon-custom-style"
      >
        {generateDOM()}
      </ReactGridLayout>
    </div>
  );
});

export default CardLayout;
