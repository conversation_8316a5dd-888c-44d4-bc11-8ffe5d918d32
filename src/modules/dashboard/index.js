import { useEffect, useState, useRef } from 'react';
import { flushSync } from 'react-dom';

import { Button } from 'devextreme-react/button';
import { Toast } from 'primereact/toast';

import HeaderFooterLayout from '@layout/page-header-footer';
import DeleteRecordDialogTemplate from '@modules/expenses/components/expenses/dialogs/deleteRecordDialog';
import {
  getAllDashboardReports,
  getDashboardReportParamById,
  postUserReports,
  deleteUserReports,
  defaultUserReports,
  getUserReports,
  postUserReportParams,
  getDashboardCustomUserParam,
} from '@services/api';
import CardLayout from './card-layout';
import ReportSettingDialog from './dialog/report-selection';
import ReportSettingsFooter from './dialog/report-selection/footer';
import SmallModal from '@components/small-modal';

const CARD_MAX_LIMIT = 13;

const Dashboard = () => {
  /**
   * Note: Except for PO(yesterday and today), rest dates are today and tomorrow
   * Convert client date time to utc formatted date and time
   */

  const toastRef = useRef(null);
  const cardRef = useRef(null);
  const [dashboardReports, setDashboardReports] = useState([]);
  const [customUserParams, setCustomUserParams] = useState([]);
  const [userReports, setUserReports] = useState([]);
  const [layoutParam, setLayoutParam] = useState({
    layout: [],
    cols: 12,
  });
  const [visibleModal, setVisibleModal] = useState(false);

  const [reportSettings, setReportSettings] = useState({
    role: null,
    type: null,
    settings: [],
  });
  const [editMode, setEditMode] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [selectedCard, setSelectedCard] = useState(0);

  const popupTitle = editMode ? 'Edit' : 'Add';
  const generateUserParam = (TYPE, LAYOUTS) => {
    const { type, reportId } = TYPE;

    const minHeightReport = type.toLowerCase() === 'report' ? 4 : 2;
    return {
      layout: [
        {
          id: '',
          reportId,
          dimensions: {
            i: LAYOUTS.length + 1,
            x: (userReports.length * 6) % 12,
            y: Number.MAX_VALUE,
            w: 6,
            h: minHeightReport,
            minH: minHeightReport,
            minW: 6,
            isBounded: true,
          },
        },
      ],
    };
  };
  const addNewCard = async () => {
    if (layoutParam.layout.length >= CARD_MAX_LIMIT) {
      toastRef.current.show({
        severity: 'warn',
        summary: 'Card Limit Exceeded',
        detail: `Card Limit is ${CARD_MAX_LIMIT}`,
        life: 3000,
      });
    } else {
      const param = generateUserParam(reportSettings.type, layoutParam.layout);
      await postUserReports(param).then(async (res) => {
        await postUserReportParams({
          userReportId: res.data.cardId,
          params: reportSettings.settings.params,
        }).then(() => {
          const newId = res.data.cardId;
          const newLayout = { ...param.layout[0].dimensions, i: newId };
          flushSync(() => {
            setUserReports([
              ...userReports,
              {
                id: newId,
                report: reportSettings.type.name,
                reportName: reportSettings.type.name,
                dimensions: newLayout,
                reportId: reportSettings.type.reportId,
              },
            ]);
            setLayoutParam((prev) => ({
              ...prev,
              layout: [...prev.layout, newLayout],
            }));
          });
          cardRef.current.children[0].lastElementChild.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'start',
          });
          setReportSettings({
            type: null,
            settings: [],
            role: null,
          });
        });
      });
    }
  };
  function onCreateUpdateBtnClick() {
    if (!editMode) {
      addNewCard();
    }
    setVisibleModal(false);
  }
  const onAddCardClick = () => {
    setVisibleModal(true);
  };
  useEffect(() => {
    const fetchData = async () => {
      try {
        /**
         * Please Note: For Grouping
         * Data pattern should be
         * [{key:"Group 1", items: [{}]}]
         */
        const dashboardReportList = await (await getAllDashboardReports()).data;
        setDashboardReports(dashboardReportList);
        const DEFAULT_USER_REPORTS = await defaultUserReports();
        if (DEFAULT_USER_REPORTS.status == 200) {
          const reports = await (await getUserReports()).data.data;
          setUserReports(reports);
          setLayoutParam((prev) => ({
            ...prev,
            layout: reports?.map((report) => report.dimensions),
          }));
          /**
           * Dashboard Custom User Params
           */
          const userParams = await (await getDashboardCustomUserParam()).data;
          setCustomUserParams(userParams);
        }
      } catch (error) {
        throw new error(error);
      }
    };
    fetchData();
  }, []);
  useEffect(() => {
    /**
     * Get Report Parameters in popup based on report type
     */
    if (reportSettings.type) {
      async function fetchSettings() {
        const settings = await (
          await getDashboardReportParamById(reportSettings.type.id)
        ).data;
        setReportSettings((prev) => ({
          ...prev,
          settings,
        }));
      }
      fetchSettings();
    }
  }, [reportSettings.type]);
  useEffect(() => {
    /**
     * reset mode and popup state after close of Popup
     */
    if (!visibleModal) {
      setEditMode(false);
      setReportSettings({
        type: null,
        settings: [],
        role: null,
      });
    }
  }, [visibleModal]);

  const onDeleteDialogHide = () => {
    setDeleteDialog(false);
  };
  const onDeleteBtnClicked = () => {
    setDeleteDialog(false);
    const deleteCard = async () => {
      await deleteUserReports(selectedCard).then(() => {
        setUserReports((prev) =>
          prev.filter((report) => report.id != selectedCard)
        );
        setLayoutParam((prev) => ({
          ...prev,
          layout: prev.layout.filter(
            (layout) => parseInt(layout.i) !== selectedCard
          ),
        }));
      });
    };
    deleteCard();
  };
  const deleteFooterDialogTemplate = (
    <>
      <Button
        icon="pi pi-check"
        type="danger"
        text="Delete"
        className="bg-error mr-2"
        onClick={onDeleteBtnClicked}
        elementAttr={{ id: 'dashboard__delete_success' }}
      />
      <Button
        icon="pi pi-times"
        type="default"
        text="No"
        className="bg-yellow-600 mr-2"
        onClick={onDeleteDialogHide}
        elementAttr={{ id: 'dashboard__delete_cancel' }}
      />
    </>
  );

  const onReportTypeValueChanged = (e) => {
    setReportSettings((prev) => ({
      ...prev,
      type: e.value,
    }));
  };
  const onCancelBtnClicked = () => {
    setVisibleModal(false);
  };

  const onCardSettingsClick = (e, i) => {
    const cardId = Number(i.i);
    setSelectedCard(cardId);
    const { name } = e.itemData;
    switch (name) {
      case 'Remove':
        setDeleteDialog(true);
        break;
      case 'Settings':
        setEditMode(true);
        const { reportId } = userReports.find((report) => report.id === cardId);
        const values = Object.values(dashboardReports).flat();
        const obj = values.find((d) => d.reportId == reportId);
        setReportSettings({
          ...reportSettings,
          type: obj,
        });
        setVisibleModal(true);
        break;
    }
  };
  function onLayoutChange(layout) {
    setLayoutParam((prev) => ({ ...prev, layout }));

    const param = userReports.map((element) => {
      return {
        ...element,
        dimensions: layout.find((card) => card.i == element.id),
      };
    });
    (async () => {
      const finalParam = { layout: param };
      await postUserReports(finalParam);
    })();
  }
  return (
    <>
      <Toast ref={toastRef} />
      <div
        className="absolute bottom-10 text-primary cursor-pointer bg-primary"
        style={{
          top: '120px',
          right: '65px',
          padding: '4px',
        }}
      >
        <strong
          title="Dashboard Settings"
          id="dashboard__settings_btn"
          onClick={onAddCardClick}
          // Note: Settings visibility is neglected
          // visible={user.isAdmin === 1}
        >
          Dashboard Settings
        </strong>
      </div>

      <div className="px-2 mb-6">
        <CardLayout
          onLayoutChange={onLayoutChange}
          data={userReports}
          reportList={dashboardReports}
          customUserParams={customUserParams}
          layout={layoutParam.layout}
          onCardSettingsClick={onCardSettingsClick}
          cardRef={cardRef}
          ref={toastRef}
        />
      </div>
      {visibleModal && (
        <SmallModal
          setOpen={setVisibleModal}
          title="Dashboard Content Settings"
          autoFocus={false}
          footer={
            <ReportSettingsFooter
              popupTitle={popupTitle}
              editMode={editMode}
              onCreateUpdateBtnClick={onCreateUpdateBtnClick}
              type={reportSettings.type}
              onCancelBtnClicked={onCancelBtnClicked}
            />
          }
        >
          <ReportSettingDialog
            dataList={dashboardReports}
            userReports={userReports}
            onReportTypeValueChanged={onReportTypeValueChanged}
            editMode={editMode}
            reportType={reportSettings}
          />
        </SmallModal>
      )}
      {deleteDialog && (
        <DeleteRecordDialogTemplate
          header="Confirm delete"
          message="Are you sure to delete Dashboard Card ?"
          data="dsd"
          visible={deleteDialog}
          onHide={onDeleteDialogHide}
          footer={deleteFooterDialogTemplate}
        />
      )}
    </>
  );
};

Dashboard.layout = HeaderFooterLayout;
export default Dashboard;
