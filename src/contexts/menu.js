import { createContext, useState, useContext, useEffect } from 'react';
const MenuContext = createContext({});
import { useAuth } from './auth';
import { useDefaults } from './defaults';
import { getUserSubMenu } from '@services/api';

export const MenuProvider = ({ children }) => {
  const { userMenus, user } = useAuth();
  const {
    defaults: { EXT_BASE_PATH },
  } = useDefaults();
  const [menus, setMenus] = useState({
    flatMenus: [],
    sideMenus: [],
    userLastSid: user.userLastSid,
  });
  const getFlatMenuUrl = (url) => {
    const concatUrl = url.startsWith('/') ? url : `/${url}`;
    return `/${EXT_BASE_PATH}${concatUrl}`;
  };
  const getSideMenuUrl = (url) => {
    const concatenatedMenu = `/${EXT_BASE_PATH}${url}`;
    const checkForDotUrl = url.startsWith('..')
      ? `/${EXT_BASE_PATH}/${url}`
      : url;
    return `${url.startsWith('/') ? concatenatedMenu : checkForDotUrl}`;
  };
  useEffect(() => {
    const fetchData = async () => {
      try {
        const getAllSideMenus = await (await getUserSubMenu()).data;
        const flatMenus = [
          ...userMenus
            .filter((item) => item.isFlatMenu === 1)
            .sort((c, d) => c.sequence - d.sequence)
            .map(({ url, id, menu }) => {
              return {
                label: menu,
                url: getFlatMenuUrl(url),
                className: 'text-sm px-2',
                id,
              };
            }),
        ];

        const sideMenus = getAllSideMenus.map((itemMenu) => {
          const makeMenu = ({ menu, url, subMenu }) => {
            const item = {
              label: menu,
              url: getSideMenuUrl(url),
              className: 'p-0',
            };
            if (subMenu?.length) {
              item.items = subMenu.map((menuItem) => {
                return makeMenu(menuItem);
              });
            }
            return item;
          };
          return makeMenu(itemMenu);
        });

        setMenus((previousState) => ({
          ...previousState,
          flatMenus,
          sideMenus,
        }));
      } catch (error) {
        console.log('Side Menu Fetch Failed');
      }
    };
    fetchData();
  }, []);
  const updateFlatMenus = (flatMenus) => {
    setMenus((previousState) => ({
      ...previousState,
      flatMenus,
    }));
  };
  const updateUserLastSid = (id) => {
    setMenus((previousState) => ({ ...previousState, userLastSid: id }));
  };
  return (
    <MenuContext.Provider
      value={{
        menus,
        user,
        updateFlatMenus,
        updateUserLastSid,
      }}
    >
      {children}
    </MenuContext.Provider>
  );
};
export const useMenu = () => useContext(MenuContext);
