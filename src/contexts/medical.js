import { getQuoteUOM } from '@services/api';
import { createContext, useState, useContext, useEffect, useMemo } from 'react';
const MedicalContext = createContext({});

export const MedicalProvider = ({ children }) => {
  const [showPOPopup, setShowPOPopup] = useState({
    show: false,
    key: '',
  });
  const [showSOPopup, setShowSOPopup] = useState({
    show: false,
    key: '',
  });

  const [UOMunit, setUOMunit] = useState(0);
  const [quoteUOMunit, setQuoteUOMunit] = useState(0);
  useEffect(() => {
    const fetch = async () => {
      try {
        const POUOM = await (
          await getQuoteUOM('PO_DTL.PO_UOM')
        ).data.map((obj) => {
          // Checking if the object has the "uomUnits" key
          if (obj.hasOwnProperty('uomUnits')) {
            // Accessing the "uomUnits" value if it exists
            return obj.uomUnits || 0; // If "uomUnits" is null or undefined, pass 0
          } else {
            return 0; // If "uomUnits" key is not present, pass 0
          }
        });
        const quoteUOM = await (
          await getQuoteUOM()
        ).data.map((item) => {
          // Checking if the object has the "uomUnits" key
          if (item.hasOwnProperty('uomUnits')) {
            // Accessing the "uomUnits" value if it exists
            return item.uomUnits || 0; // If "uomUnits" is null or undefined, pass 0
          } else {
            return 0; // If "uomUnits" key is not present, pass 0
          }
        });
        setUOMunit(POUOM[0] || 0);
        setQuoteUOMunit(quoteUOM[0] || 0);
      } catch (e) {
        console.log(e);
      }
    };
    fetch();
  }, []);
  const contextValue = useMemo(
    () => ({
      showPOPopup,
      showSOPopup,
      setShowPOPopup,
      setShowSOPopup,
      UOMunit,
      quoteUOMunit,
    }),
    [showPOPopup, showSOPopup, UOMunit, quoteUOMunit]
  );
  return (
    <MedicalContext.Provider value={contextValue}>
      {children}
    </MedicalContext.Provider>
  );
};
export const useMedical = () => useContext(MedicalContext);
