import { createContext, useState, useContext, useEffect } from 'react';
const DefaultContext = createContext({});
import {
  getNameAddrData,
  getAllUsers,
  getAllCustomLabels,
  getMenuEmailError,
  getVersion,
  getFooter<PERSON>magePath,
  getYoxelToken,
} from '@services/api';
import { DATE_FORMATS, TIME_FORMATS } from 'src/constants';
import { getTodaysDate } from 'src/utils';
export const DefaultProvider = ({ children }) => {
  const [defaults, setDefaults] = useState({
    NAME: '',
    ADDRESS: '',
    EXT_BASE_PATH: 'RepfabricCRM',
    USERS: [],
    CUSTOM_LABELS: [],
    TIME_ZONE: '',
    DATE_FORMAT: '',
    TIME_FORMAT: '',
    EMAIL_ERR_MSG: '',
    VERSION: '',
    INSTANCE_ID: '',
    INSTANCE_NAME: '',
    BRAND_TYPE: '',
    YOXEL_TOKEN: '',
    LOCAL_TIME_ZONE: '',
  });

  useEffect(() => {
    const fetchDefaults = async () => {
      const nameAddrdata = await (await getNameAddrData()).data;
      // PARAMS_SUBS_NAME and PARAM_SUBS_ADDRESS are present in Response Array of object
      const {
        PARAM_SUBS_NAME,
        PARAM_SUBS_ADDRESS,
        PARAM_TIMEZONE_ID,
        PARAM_DATE_FORMAT,
        PARAM_TIME_FORMAT,
        PARAM_SUBS_ID,
        CLIENT,
        PARAM_DEF_QUOT_RECIPIENT,
        localTimezone,
      } = nameAddrdata.find((data) => 'PARAM_SUBS_NAME' in data);
      const USERS = await (await getAllUsers()).data;
      const CUSTOM_LABELS = await (await getAllCustomLabels()).data;
      const emailErrorMsg = await (
        await getMenuEmailError(getTodaysDate().format('MM-DD-YYYY'))
      ).data;
      const VERSION = await (await getVersion()).data;
      const BRAND_TYPE = await (await getFooterImagePath()).data;
      const YOXEL_TOKEN = await (await getYoxelToken()).data[0]?.token;
      setDefaults({
        ...defaults,
        NAME: PARAM_SUBS_NAME,
        ADDRESS: PARAM_SUBS_ADDRESS,
        TIME_ZONE: PARAM_TIMEZONE_ID,
        DATE_FORMAT: DATE_FORMATS[PARAM_DATE_FORMAT],
        TIME_FORMAT: TIME_FORMATS[PARAM_TIME_FORMAT],
        USERS,
        CUSTOM_LABELS,
        EMAIL_ERR_MSG: emailErrorMsg,
        VERSION,
        INSTANCE_ID: PARAM_SUBS_ID,
        INSTANCE_NAME: CLIENT,
        BRAND_TYPE,
        YOXEL_TOKEN,
        PARAM_DEF_QUOT_RECIPIENT,
        LOCAL_TIME_ZONE: localTimezone,
      });
    };
    fetchDefaults();
  }, []);

  return (
    <DefaultContext.Provider value={{ defaults }}>
      {children}
    </DefaultContext.Provider>
  );
};
export const useDefaults = () => useContext(DefaultContext);
