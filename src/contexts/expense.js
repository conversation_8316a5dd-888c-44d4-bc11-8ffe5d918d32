import { createContext, useState, useContext, useEffect } from 'react';
const ExpenseContext = createContext({});
import { ExpenseService } from '@services/expenseService';
import {
  getDashboardCustomUserParam,
  getExpTaxVisibility,
  getParamConfigRate,
} from '@services/api';

export const ExpenseProvider = ({ children }) => {
  const [defaults, setDefaults] = useState({
    reportDefaultType: '',
    expenseDefaultType: '',
    category: '',
    milageRate: '',
    taxColumnVisibible: false,
    expenseNonReimbursableParam: 0,
  });
  const expenseService = new ExpenseService();
  useEffect(() => {
    const fetchDefaults = async () => {
      try {
        const [
          expenseReportRes,
          expenseRes,
          categoryRes,
          rateRes,
          taxVisibilityRes,
          customUserParams,
        ] = await Promise.all([
          expenseService.getFilter('expenseReport'),
          expenseService.getFilter('expense'),
          expenseService.getExpenseCategory(),
          getParamConfigRate(),
          getExpTaxVisibility(),
          getDashboardCustomUserParam(),
        ]);
        setDefaults((_defaults) => ({
          ..._defaults,
          reportDefaultType:
            expenseReportRes.data !== null
              ? expenseReportRes.data.filterData
              : 'all',
          expenseDefaultType:
            expenseRes.data !== null ? expenseRes.data.filterData : 'all',
          category: categoryRes.data.find(
            (item) => item.CATEGORY_DEFAULT_FLAG === 1
          ),
          milageRate: rateRes.data[0].value,
          taxColumnVisibible: taxVisibilityRes.data,
          expenseNonReimbursableParam: customUserParams.data.find(
            (param) => param.userParamId === 'EXPENSE_NON_REIMBURSABLE'
          ),
        }));
      } catch (error) {
        console.log('There is some error');
      }
    };

    fetchDefaults();
  }, []);
  function onValueChange(field, value) {
    setDefaults((_defaults) => ({
      ..._defaults,
      [field]: value,
    }));
  }
  return (
    <ExpenseContext.Provider value={{ defaults, onValueChange }}>
      {children}
    </ExpenseContext.Provider>
  );
};
export const useExpense = () => useContext(ExpenseContext);
