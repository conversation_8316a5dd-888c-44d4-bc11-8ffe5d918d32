import { delete_cookie } from 'sfcookies';
import { AuthService } from '@services/auth.service';
import { createContext, useContext, useEffect, useReducer } from 'react';
import Loading from '@components/loading';
import { getAllMenus } from '@services/api';
const cookieKey = 'namedOFCookie';
const authService = new AuthService();
const StateContext = createContext({
  isAuthenticated: false,
  user: null,
  loading: true,
});
const DispatchContext = createContext(null);
const reducer = (state, { type, payload }) => {
  switch (type) {
    case 'LOGIN':
      return {
        ...state,
        isAuthenticated: true,
        user: payload.userName,
        userMenus: payload.userMenus,
      };
    case 'LOGOUT':
      delete_cookie(cookieKey);
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        userMenus: [],
      };
    case 'POPULATE':
      return {
        ...state,
        user: {
          ...state.user,
          ...payload,
        },
      };
    case 'STOP_LOADING':
      return {
        ...state,
        loading: false,
      };
    default:
      throw new Error(`Unknown action type: ${type}`);
  }
};
export const AuthProvider = ({ children }) => {
  const [state, defaultDispatch] = useReducer(reducer, {
    user: null,
    isAuthenticated: false,
    loading: true,
    userMenus: [],
    logout,
  });
  const dispatch = (type, payload) => defaultDispatch({ type, payload });
  async function logout() {
    await authService.logout().then(() => {
      dispatch('LOGOUT');
      window.location.href = '/RepfabricCRM/Login.xhtml';
    });
  }
  useEffect(() => {
    const loadUser = async () => {
      try {
        const data = await (await getAllMenus()).data;
        dispatch('LOGIN', data);
      } catch (err) {
        dispatch('LOGOUT');
      } finally {
        dispatch('STOP_LOADING');
      }
    };
    loadUser();
    // eslint-disable-next-line
  }, []);
  return (
    <StateContext.Provider value={state}>
      <DispatchContext.Provider value={dispatch}>
        {children}
      </DispatchContext.Provider>
    </StateContext.Provider>
  );
};
export const useAuth = () => useContext(StateContext);
export const useAuthDispatch = () => useContext(DispatchContext);
export const ProtectRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  useEffect(() => {
    if (!loading && !isAuthenticated) {
      window.location.href = '/RepfabricCRM/Login.xhtml';
    }
  }, [loading, isAuthenticated]);
  if (loading) {
    return <Loading />;
  }
  return isAuthenticated && children;
};
