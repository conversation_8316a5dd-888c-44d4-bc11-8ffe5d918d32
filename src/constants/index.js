import { lmsSSO } from '@services/api';

export const PAGE_HEADER_TITLE = {
  expenses: 'Expenses',
  quotes: 'Quotes',
  'division-report': 'Division Report',
  'design-registrations': 'Design Registrations',
  'x-reports': 'X-Reports',
  'quote-report': 'Quote Report',
  medical: 'Medical Case Management',
  404: 'Page Not found',
  'sync-process': 'Sync Process',
};

const tutorial = [
  {
    path: '/',
    url: 'https://learning.repfabric.com/whats-new-in-repfabric-2/?autologin_code=U6dfTsNLSjw7CgfWdVTyMLt9ROnfjiWn',
  },
  {
    path: '/quotes',
    url: 'https://learning.repfabric.com/category/quotebuilder/?autologin_code=U6dfTsNLSjw7CgfWdVTyMLt9ROnfjiWn',
  },
];

export const LOGO =
  '/RepfabricCRM/images/dynamic/repfabfiles/logo.png?pfdrid_c=true';

export const BANNER_IMAGE = '/RepfabricCRM/resources/images/hedder_bg.png';

const lmsSSOMethod = async (path) => {
  const filterTag = subTagArray.find((item) => item.name === path.slice(1));
  const subTag = filterTag ? filterTag.tag : '';
  try {
    const LMSSSO = await lmsSSO(subTag);
    if (LMSSSO.status === 200) {
      window.open(LMSSSO.data);
    }
  } catch (error) {
    console.error(error.message);
  }
};

export const helpDropdownItems = (path) => [
  // {
  //   label: 'Tutorials',
  //   value: '10',
  //   link: tutorial.find((item) => item.path.toLowerCase() == path.toLowerCase())
  //     ?.url,
  //   target: '_blank',
  // },
  {
    label: 'Learning',
    value: 'lmsso',
    onClick: () => lmsSSOMethod(path),
  },

  {
    label: `Daily Help Session\nRSVP`,
    value: '50',
    link: 'https://outlook.office365.com/owa/calendar/<EMAIL>/bookings/s/PSCNIU4wBU2y4SC0lMxIvg2',
    target: '_blank',
  },
  {
    label: 'Bean Counter Q & A',
    value: '20',
    link: 'https://outlook.office365.com/owa/calendar/<EMAIL>/bookings/s/kRSCzku1f0mUmBdxQWkHzw2',
    target: '_blank',
  },
  {
    label: 'Support',
    value: '20',
    id: 'myCustomTrigger',
    link: '#',
  },
];

export const createDropDownItems = [
  {
    label: 'Contact',
    value: 'contact',
    link: '/RepfabricCRM/opploop/contacts/ContactDetails.xhtml?id=0',
    target: '_self',
  },
  {
    label: 'Company',
    value: 'company',
    link: '/RepfabricCRM/opploop/companies/CompanyDetails.xhtml?id=0',
    target: '_self',
  },
  {
    label: 'Opportunity',
    value: 'opportunity',
    link: '/RepfabricCRM/opploop/opportunity/OpportunityView.xhtml',
    target: '_self',
  },
  {
    label: 'Activity Journal',
    value: 'activity_journal',
    link: '/RepfabricCRM/Journal/JournalEntry.xhtml',
    target: '_self',
  },
  {
    label: 'Quote',
    value: 'quote',
    link: '/RepfabricCRM/opploop/quotes/NewQuote.xhtml',
    target: '_self',
  },
  {
    label: 'Email',
    value: 'email',
    link: '/email/rf_email?fn=compose&amp;autocheckemail=1&amp;reqid=2255111504',
    target: '_blank',
  },
];

export const quarterList = [
  { id: 0, name: 'All' },
  { id: 1, name: 1 },
  { id: 2, name: 2 },
  { id: 3, name: 3 },
  { id: 4, name: 4 },
];

export const monthList = [
  { id: 0, name: 'All', shortName: 'All' },
  { id: 1, name: 'January', shortName: 'Jan' },
  { id: 2, name: 'February', shortName: 'Feb' },
  { id: 3, name: 'March', shortName: 'Mar' },
  { id: 4, name: 'April', shortName: 'Apr' },
  { id: 5, name: 'May', shortName: 'May' },
  { id: 6, name: 'June', shortName: 'Jun' },
  { id: 7, name: 'July', shortName: 'Jul' },
  { id: 8, name: 'August', shortName: 'Aug' },
  { id: 9, name: 'September', shortName: 'Sep' },
  { id: 10, name: 'October', shortName: 'Oct' },
  { id: 11, name: 'November', shortName: 'Nov' },
  { id: 12, name: 'December', shortName: 'Dec' },
];

export const typeList = [
  { label: 'All', value: 'all' },
  // { label: 'My Expenses', value: 'myExpenses' },
  { label: 'Self', value: 'self' },
  { label: 'Other', value: 'other' },
];

export const amountFormat = {
  type: 'currency',
  currency: 'USD',
  useGrouping: true,
  minimumSignificantDigits: 3,
  precision: 2,
};

export const statusArray = [
  { text: 'OPEN', type: 'default', color: '' },
  { text: 'SUBMITTED', type: 'default', color: 'bg-primary' },
  { text: 'APPROVED', type: 'success', color: 'bg-success' },
  { text: 'REJECTED', type: 'danger', color: 'bg-error' },
  { text: 'PAID', type: 'success', color: 'bg-success' },
];

export const reimbursableStatus = [
  {
    id: 1,
    name: 'Yes',
  },
  {
    id: 0,
    name: 'No',
  },
];

export const summaryFormat = { style: 'currency', currency: 'USD' };

export const TOAST_CONTENT_PROPERTIES = [
  {
    id: 1,
    type: 'success',
    className: 'p-toast-message-success',
    icon: 'pi-check',
  },
  {
    id: 2,
    type: 'info',
    className: 'p-toast-message-info',
    icon: 'pi-info-circle',
  },
  {
    id: 3,
    type: 'warn',
    className: 'p-toast-message-warn',
    icon: 'pi-exclamation-triangle',
  },
  {
    id: 4,
    type: 'error',
    className: 'p-toast-message-error',
    icon: 'pi-times',
  },
];

export const DATE_FORMATS = {
  1: 'MM-DD-YYYY',
  2: 'DD-MM-YYYY',
  3: 'YYYY-MM-DD',
};

export const TIME_FORMATS = {
  1: 'hh:mm A',
  2: 'HH:mm',
};

export const DATE_FILTER_FORMAT = {
  'MM-DD-YYYY': 'MM-dd-yyyy',
  'DD-MM-YYYY': 'dd-MM-yyyy',
  'YYYY-MM-DD': 'yyyy-MM-dd',
};

export const TIME_FILTER_FORMAT = {
  'hh:mm A': 'hh:mm a',
  'HH:mm': 'H:mm',
};

export const CALENDAR_FORMAT = {
  'MM-DD-YYYY': 'mm-dd-yy',
  'DD-MM-YYYY': 'dd-mm-yy',
  'YYYY-MM-DD': 'yy-mm-dd',
};

/**
 * Please Note: Task card btn for show followup is removed temporary as
 * JSF team doesnt have a valid answer for this button in Tasks card
 * const SHOW_FOLLOW_BTN_CARD = ['ta','ev', 'aj', 'jo', 'op'];
 */
export const SHOW_FOLLOW_BTN_CARD = [
  'EVENTS',
  'IDS_ACT_JOURNALS',
  'IDS_JOBS',
  'IDS_OPPS',
];

export const CARD_SETTINGS_OPTIONS = [
  // Commented for Time Being
  // {
  //   id: 1,
  //   name: 'Settings',
  //   icon: 'preferences',
  // },
  {
    id: 2,
    name: 'Remove',
    icon: 'trash',
    text: 'remove',
  },
];

export const medicalStatusArray = [
  { id: 0, text: 'Open' },
  { id: 1, text: 'Re-Scheduled' },
  { id: 2, text: 'Cancelled' },
  { id: 3, text: 'Closed' },
];

export const medicalStageArray = [
  { id: 0, text: 'Scheduled', message: 'Scheduled' },
  { id: 1, text: 'Gathering Inventory', message: 'Gathering inventory' },
  { id: 2, text: 'Building Sales Order', message: 'Building sales order' },
  { id: 3, text: 'Sending Sales Order', message: 'Quote creation' },
  { id: 4, text: 'Replenish Inventory', message: 'Replenish inventory' },
  { id: 5, text: 'Waiting For Inventory', message: 'Waiting for inventory' },
  { id: 6, text: 'PO Received', message: 'PO creation' },
  { id: 7, text: 'Commission Received', message: 'Commission received' },
];

export const subTagArray = [
  { name: '', tag: 'INDEX' },
  { name: 'expenses', tag: 'EXPENSES' },
  { name: 'design-registrations', tag: 'ACC_REG' },
  { name: 'division-report', tag: 'SALES_RPT' },
  { name: 'medical', tag: 'MEDICAL' },
  { name: 'x-reports', tag: 'Analytics' },
];

export const exportFormatList = [
  { id: 1, value: 'Unformatted' },
  { id: 0, value: 'Formatted' },
];
