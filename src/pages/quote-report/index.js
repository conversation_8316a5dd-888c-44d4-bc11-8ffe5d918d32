import { useAuth } from '@contexts/auth';
import HeaderFooterLayout from '@layout/page-header-footer';
import QuoteList from '@modules/quote-list';
import AccessDenied from '@components/access-denied';

const QuoteReportPage = () => {
  const { userMenus } = useAuth();
  // check whether userMenus has id called 89
  const hasAccess = userMenus.some((menu) => menu.id === 85);
  if (!hasAccess) return <AccessDenied />;
  return (
    <HeaderFooterLayout>
      <QuoteList />
    </HeaderFooterLayout>
  );
};
export default QuoteReportPage;
