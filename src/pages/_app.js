import { useEffect } from 'react';
import '@styles/globals.css';
import 'devextreme/dist/css/dx.softblue.css';
import 'primereact/resources/themes/fluent-light/theme.css'; // Fluent UI theme
import 'primereact/resources/primereact.min.css'; //core css
import 'primeicons/primeicons.css'; //icons
import '/node_modules/primeflex/primeflex.css';
import { AuthProvider, ProtectRoute } from '@contexts/auth';
import { MenuProvider } from '@contexts/menu';
import { useRouter } from 'next/router';
import { DefaultProvider } from '@contexts/defaults';

function App({ Component, pageProps }) {
  const { asPath, isReady, push } = useRouter();
  useEffect(() => {
    if (
      isReady &&
      asPath?.includes('quotes') &&
      asPath?.split('/').length > 1
    ) {
      push(asPath);
    }
  }, [asPath]);
  const Layout = Component.layout || (({ children }) => <>{children}</>);

  return (
    <AuthProvider>
      <ProtectRoute>
        <DefaultProvider>
          <MenuProvider>
            <Layout>
              <Component {...pageProps} />
            </Layout>
          </MenuProvider>
        </DefaultProvider>
      </ProtectRoute>
    </AuthProvider>
  );
}

export default App;
