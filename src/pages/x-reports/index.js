import HeaderFooterLayout from '@layout/page-header-footer';
import XReports from '@modules/x-reports';
import Head from 'next/head';
const XReportsPage = () => {
  return (
    <>
      <Head>
        {/* Google Tag Manager */}
        <script
          dangerouslySetInnerHTML={{
            __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-NKX6NB2');`,
          }}
        />
        {/* End Google Tag Manager */}
      </Head>

      <XReports />
    </>
  );
};
XReportsPage.layout = HeaderFooterLayout;
export default XReportsPage;
