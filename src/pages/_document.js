import Document, { Html, Head, Main, NextScript } from 'next/document';

import nextConfig from 'next.config';

class MyDocument extends Document {
  static async getInitialProps(ctx) {
    const initialProps = await Document.getInitialProps(ctx);
    const currentPath = ctx.pathname;
    return { ...initialProps, currentPath };
  }

  render() {
    const { basePath } = nextConfig;
    const currentPath = this.props?.currentPath;
    const isXReportsPage = currentPath === '/x-reports';

    return (
      <Html lang="en">
        <Head>
          <meta name="description" content="NextGen CRM Management" />
          <link rel="manifest" href={`${basePath}/manifest.json`} />
          <link
            rel="shortcut icon"
            type="image/x-icon"
            href={`${basePath}/favicon.ico.xhtml`}
          />
        </Head>
        <body className="dx-viewport">
          {isXReportsPage && (
            <noscript
              dangerouslySetInnerHTML={{
                __html: `
      <iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-NKX6NB2"
        height="0"
        width="0"
        style="display: none; visibility: hidden;"
      ></iframe>
    `,
              }}
            />
          )}

          <Main />
          <NextScript />
          <div id="modal" />
        </body>
      </Html>
    );
  }
}

export default MyDocument;
