import crypto from 'crypto';
import * as dayjs from 'dayjs';
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const isSameOrBefore = require('dayjs/plugin/isSameOrBefore');
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isSameOrBefore);

import { formatNumber } from 'devextreme/localization';
const customParseFormat = require('dayjs/plugin/customParseFormat');
dayjs.extend(customParseFormat);
/**
 * Generate a cryptographically well-built artificial 2 byte random data
 * @returns random generated value
 */
export const getRandomValue = () => crypto.randomBytes(2).toString('hex');

/**
 * Gets Todays date in MM-DD-YYYY format
 * @returns Todays Date in MM-DD-YYYY(eg: 07-28-2022)
 */
export const getTodaysDate = () => dayjs();

export const getTodaysDateTz = () => dayjs().utc().format();

export const getCurrentYear = () => dayjs().year();

export const getFirstDayOfCurrentMonth = () => dayjs().startOf('month').$d;

/**
 * Gets Today + N day in MM-DD-YYYY format
 * @returns Today + N day in MM-DD-YYYY(eg: 07-28-2022)
 */
export const getTodayDatePlusN = (n) => dayjs().utc().add(n, 'day');

/**
 * Gets Today - N day in MM-DD-YYYY format
 * @returns Today - N day in MM-DD-YYYY(eg: 07-28-2022)
 */
export const getTodayDateMinusN = (n) => dayjs().utc().subtract(n, 'day');

/**
 * Gets Todays date(UTC) in MM-DD-YYYY HH:mm:ss format(eg: 11-23-2022 13:22:22)
 * @returns Todays Date(UTC) in MM-DD-YYYY HH:mm:ss
 */
export const getTodaysDateWithTime = () =>
  dayjs().utc().format('MM-DD-YYYY HH:mm:ss');
/**
 * Gets Todays date-N(UTC) in MM-DD-YYYY HH:mm:ss format(eg: 11-23-2022 13:22:22)
 * @returns Todays Date-N(UTC) in MM-DD-YYYY HH:mm:ss
 */
export const getTodayDtmMinusN = (n) =>
  dayjs().utc().subtract(n, 'day').utc().format('MM-DD-YYYY HH:mm:ss');

/**
 * Gets Today + N day in MM-DD-YYYY HH:mm:ss format(eg: 11-23-2022 13:22:22)
 * @returns Today + N day in MM-DD-YYYY HH:mm:ss
 */
export const getTodaysDtmPlusN = (n) =>
  dayjs().add(n, 'day').utc().format('MM-DD-YYYY HH:mm:ss');

/**
 * Gets Given date in given format
 * @returns given Date in given format
 */
export const convertDateTimeFormat = (date, dateFormat, timeFormat = '') =>
  dayjs(date).utc().local().format(`${dateFormat} ${timeFormat}`);

/**
 * Converts Date according to format
 * @param {Date} date
 * @returns Date according to format
 */
export const convertDateFormat = (date, format) => dayjs(date).format(format);

/**
 * Convert First letter of each word capitalized
 * @param {string} str
 * @returns First letter cappitalized(eg: I'm A Little Tea Pot )
 */
export const capitalizeFirstLetter = (str) => {
  const splitStr = str?.toLowerCase().split(' ');
  for (let i = 0; i < splitStr?.length; i++) {
    splitStr[i] =
      splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
  }
  return splitStr?.join(' ');
};
/**
 * Convert Number/String to US Format currency
 * @param {string} num
 * @returns USD currency integer
 */
export const convertNumToCurrency = (num, minimumFractionDigits = 2) =>
  formatNumber(Number(num), {
    style: 'currency',
    currency: 'USD',
    useGrouping: true,
    minimumFractionDigits,
  });
/**
 * Return Custom label based on labelId
 * @param {array} labelArr
 * @param {array} labels
 */
export const getCustomValBasedOnLabel = (labelArr, labels) => {
  return labels.map((label) => {
    const labelObj = labelArr.find((l) => l.labelId == label);
    if (labelObj) {
      return labelObj.labelCustom;
    }
    return '';
  });
};

/**
 * Converts Local Date-Time to specific TimeZone
 * @param {tz} TimeZone("Asia/Tokyo")
 * @returns Date Time in specified timezone
 */
export const getDateTimeByTZ = (tz) =>
  dayjs().tz(tz).format('MM-DD-YYYY HH:mm:ss');

/**
 * Add Local Date-Time + N to get Date-Time in specific TimeZone
 * @param {tz} TimeZone("Asia/Tokyo")
 * @returns Date Time in specified timezone
 */
export const addDateTimeByTZ = (tz, N, format = 'MM-DD-YYYY HH:mm:ss') =>
  dayjs().tz(tz).add(N, 'day').format(format);

export const addMedicalDateTimeByTZ = (
  N,
  time,
  format = 'MM-DD-YYYY HH:mm:ss'
) => {
  let dateTime = dayjs().add(N, 'day');
  if (time) {
    const [hours, minutes, seconds] = time.split(':').map(Number);
    dateTime = dayjs()
      .add(N, 'day')
      .set('hour', hours)
      .set('minute', minutes)
      .set('second', seconds);
  }
  return dateTime.format(format);
};
/**
 * Add Local Date-Time - N to get Date-Time in specific TimeZone
 * @param {tz} TimeZone("Asia/Tokyo")
 * @returns Date Time in specified timezone
 */
export const subDateTimeByTZ = (tz, N) =>
  dayjs().tz(tz).subtract(N, 'day').format('MM-DD-YYYY HH:mm:ss');

/**
 * converts dayjs object to JS object
 * @param {obj} dayjsObj
 * @returns
 */
export const convertDayjsToJSObject = (dayjsObj, params) =>
  dayjs(dayjsObj, params).toDate();

export const isDay1SameOrBeforeDay2 = (day1, day2) =>
  dayjs(day1).isSameOrBefore(day2);

export const getDayTimeInLocal = (day, format = '') =>
  dayjs().add(1, 'day').format(format);

export const getDateFormatISOFormat = (date) => dayjs(date).utc().toISOString();

export const getDateTimeByTimeZone = (dateTime = '', tz = 'America/New_York') =>
  dayjs.utc(dateTime).tz(tz).format('YYYY-MM-DD HH:mm:ss');

export const getUTCDateTime = (dateTime = '') =>
  dayjs.utc(dateTime).format('YYYY-MM-DD HH:mm:ss');
