/**
 * NOTE: Add endpoints in below format
 * Suppose page is for quote and api
 * is used for this page, then its
 * endpoints **MUST** start with prefix
 * QUOTE_* and similarly for all Pages
 * For general, we can ignore
 */
export const endpoints = {
  QUOTE_LABELS: 'quote-labels',
  QUOTE_GENERATE_PDF: 'pdf',
  QUOTE_PDF_TEMPLATE: 'pdf-template',
  QUOTE_PDF_CONFIG: 'pdf-config',
  QUOTE_LOGO_UPLOAD: 'upload-logo',
  QUOTE_NAME_ADDR_PARAMS: 'params',
  QUOTE_DEFAULT_TEMPLATE: 'quote/princi-default-template',
  DASHBOARD_ALL_REPORTS: 'reports',
  DASHBOARD_RPT_PARAMS: 'params',
  DASHBOARD_TASK_CARD: 'dashboard/tasks',
  DASHBOARD_AJ_CARD: 'dashboard/activity-journal',
  DASHBOARD_EVENTS_CARD: 'dashboard/events',
  DASHBOARD_OPPR_CARD: 'dashboard/opportunities',
  DASHBOARD_QUOTES_CARD: 'dashboard/quotes',
  DASHBOARD_SAMPLE_CARD: 'dashboard/samples',
  DASHBOARD_MSG_CARD: 'dashboard/messages',
  DASHBOARD_PO_CARD: 'dashboard/purchase-order',
  DASHBOARD_JOBS_CARD: 'dashboard/jobs',
  DASHBOARD_TASK_DONE: 'dashboard/tasks',
  DASHBOARD_CUSTOM_USER_PARAMS: 'custom-user-params',
  DASHBOARD_RPT_SALES_BY_MTH: 'dashboard/sales-by-month',
  DASHBOARD_MSG_TODO: 'dashboard/todo-message',
  DASHBOARD_MSG_RLY: 'dashboard/reply-message',
  CUSTOM_LABLES: 'custom-label',
  ROLES: 'role',
  USERS: 'user-list',
  LOGOUT: 'logout',
  USER_MENUS: 'user-menu',
  USER_REPORTS: 'user-reports',
  USER_REPORT_PARAMS: 'user-report-params',
  DIVISION_REPORT: 'division/division-dashboard',
  DIVISION_PRINCI_CUST: 'principal-customer',
  DIVISION_REGION: 'region',
  DIVISION_USER: 'division/division-user',
  DIV_PRINCIPAL: 'principals',
  DIV_CUSTOMER: 'customers',
  DIV_DIVISION: 'divisions',
  DIVISION_REPROCESS: 'division/re-process-split',
  DASHBOARD_SALES_TEAM: 'dashboard/sales-team',
  MENU_EMAIL_ERROR: 'email-error',
  CHANGE_PASSWORD: 'change-password',
  UPDATE_USER_LAST_SID: 'user-last-sid',
  PARAM_CONFIG_RATE: 'param-config/DEF_MILEAGE_RATE',
  DEFAULT_USER_REPORTS: 'default-user-reports',
  DASHBOARD_USER_RPT_FLT: 'user-report-filter',
  DELIGATION_USERS: 'deligation-users',
  SUPER_USER_LOGIN_LIST: 'user-list-super',
  LOGIN_AS_SUPER_USER: 'super-login',
  LMSSSO: 'lms/sso',
  VERSION_NO: 'version',
  DASHBOARD_GOALS: 'goal',
  DASHBOARD_GOALS_REFRESH: 'goal/refresh',
  REGISTRATION: 'registration',
  REGISTRATION_UPDATE: 'registration/update',
  REGISTRATION_DELETE: 'registration/delete',
  USER_SUB_MENU: 'user-sub-menu',
  EXPENSE_VIEW_RECEIPTS: 'expense-receipt',
  EXPENSE_RECEIPT_DELETE: 'delete-expense-receipt',
  XREPORTS_USER_AUTH: 'xreports/user-auth',
  XREPORTS_URL: 'extended-report-url',
  QUOTE_LIST: 'quote/quote-list',
  PRINCIPAL_CUSTOMER: 'principal-customer/-1',
  COMPANY_TYPE: 'company-type',
  QUOTE_STATUS: 'status?source=QUOTE',
  FOOTER_LOGO_PATH: 'brand-type',
  MEDICAL_REPORT: 'medical-header',
  MEDICAL_DETAIL: 'medical-detail',
  MEDICAL_LINE_ITEM: 'medical-line-item',
  MED_STAGE: 'stage',
  MED_SALES_PERSONS: 'sales-persons',
  MED_PERSON_COMPANIES: 'person-companies',
  MED_FACILITIES: 'principal-customer/3',
  MED_LINE_PARTNO: 'product-mast',
  MED_LINE_CUSTOMER_PRICE: 'customer-price',
  MEDICAL_CONTACT: 'medical-contact',
  PRIMARY_CONTACT: 'primary-contact',
  COMPANY_CONTACT: 'company-contact',
  YOXEL_TOKEN: 'yoxel-token',
  NEW_QUOTE_NUMBER: 'quotes/new_quote_number',
  QUOTE: 'quotes',
  QUOTE_UOM: 'prod-uom',
  MED_SIGN_UPLOAD: 's3',
  MED_EMAIL: 'mail',
  MED_PO: 'purchase_orders',
  MED_PRINCI_COMM_RATE: 'princi-comm-rate',
  MED_MENU_ENABLED: 'medical-menu-enabled',
  MED_CALENDAR: 'calendar',
  EXP_FEED_TOTAL: 'tax-enabled',
  SYNC_PROCESS: 'sync/sync-tables',
  SYNC_ENABLE_TABLE: 'sync/enable-sync-table',
  SYNC_CRON_JOB_STATUS: 'sync/sync-status',
  SYNC_ENABLE_CRON_JOB: 'sync/enable-cronjob',
  SYNC_CLEAR_PROCESS: 'sync/clear-ng-sync',
  SYNC_ERRORS: 'sync/clear-error-record',
  SYNC_RETRY_UNPROCESS: 'sync/retry-unprocessed-sync',
};
