import axios from 'axios';
import { delete_cookie } from 'sfcookies';
const cookieKey = 'namedOFCookie';
import { endpoints } from './endpoints';

const createAxiosInstance = (baseURL) => {
  const instance = axios.create({
    baseURL,
    withCredentials: true,
  });
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response.status === 401) {
        delete_cookie(cookieKey);
        window.location.href = '/RepfabricCRM/Login.xhtml';
      }
    }
  );
  return instance;
};

const createYoxelAxiosInstance = (baseURL) => {
  const instance = axios.create({
    baseURL,
  });
  instance.interceptors.response.use(
    (response) => response,
    (e) => {
      if (e.response.status === 401) {
        delete_cookie(cookieKey);
        window.location.href = '/RepfabricCRM/Login.xhtml';
      }
    }
  );
  return instance;
};

const axiosDefaults = createAxiosInstance(process.env.NEXT_PUBLIC_BASE_URL);

const axiosMedicalDefaults = createAxiosInstance(
  process.env.NEXT_PUBLIC_MEDICAL_URL
);

const axiosYoxelDefaults = createYoxelAxiosInstance(
  process.env.NEXT_PUBLIC_YOXEL_URL
);

const request = (path, method, param, headers) => {
  return axiosDefaults[method](path, param, headers);
};

const medicalRequest = (path, method, param, headers) => {
  return axiosMedicalDefaults[method](path, param, headers);
};

const yoxelRequest = (path, method, param, headers, username) => {
  axiosYoxelDefaults.defaults.auth = { username };
  return axiosYoxelDefaults[method](path, param, headers);
};
const [GET_METHOD, POST_METHOD, PATCH_METHOD, DELETE_METHOD, PUT_METHOD] = [
  'get',
  'post',
  'patch',
  'delete',
  'put',
];

export const logoutUser = () => {
  return request(endpoints.LOGOUT, POST_METHOD, {
    'Content-Type': 'application/json',
  });
};

export const getQuoteLabels = (params) => {
  return request(
    `${endpoints.QUOTE_LABELS}?isSuperQuote=${params.isSuperQuote}&groupByManufactureNo=${params.groupByManufactureNo}`,
    GET_METHOD
  );
};

export const postQuotePDF = (params) => {
  return request(endpoints.QUOTE_GENERATE_PDF, POST_METHOD, params, {
    responseType: 'arraybuffer',
  });
};

export const getPdfTemplate = () => {
  return request(endpoints.QUOTE_PDF_TEMPLATE, GET_METHOD);
};

export const getPdfConfig = (id) => {
  return request(`${endpoints.QUOTE_PDF_CONFIG}/${id}`, GET_METHOD);
};

export const postPdfConfig = (params) => {
  return request(endpoints.QUOTE_PDF_CONFIG, POST_METHOD, params);
};

export const updatePdfConfig = (id, params) => {
  return request(`${endpoints.QUOTE_PDF_CONFIG}/${id}`, PATCH_METHOD, params);
};

export const deletePdfConfig = (id) => {
  return request(`${endpoints.QUOTE_PDF_CONFIG}/${id}`, DELETE_METHOD);
};

export const uploadQuoteLogo = (templateId, image) => {
  return request(
    `${endpoints.QUOTE_LOGO_UPLOAD}/${templateId}`,
    PUT_METHOD,
    image
  );
};

export const getNameAddrData = () => {
  return request(endpoints.QUOTE_NAME_ADDR_PARAMS, GET_METHOD);
};

export const getAllMenus = () => {
  return request(endpoints.USER_MENUS, GET_METHOD);
};

export const getAllDashboardReports = () => {
  return request(endpoints.DASHBOARD_ALL_REPORTS, GET_METHOD);
};

export const getDashboardReportParamById = (id) => {
  return request(`${endpoints.DASHBOARD_RPT_PARAMS}/${id}`, GET_METHOD);
};

export const getDashboardTaskCardDetails = (taskDate = '') => {
  return request(
    `${endpoints.DASHBOARD_TASK_CARD}?taskDate=${taskDate}`,
    GET_METHOD
  );
};

export const putDashboardTaskDone = (id) =>
  request(`${endpoints.DASHBOARD_TASK_DONE}/${id}`, PUT_METHOD);

export const getDashboardCustomUserParam = () =>
  request(endpoints.DASHBOARD_CUSTOM_USER_PARAMS, GET_METHOD);

export const getDashboardAJCardDetails = (
  fromDate,
  toDate,
  ajId = 0,
  order = 'ASC',
  showAll = 0
) => {
  // From Date: Client Todays Date
  // To Date: Client Today Date+2
  //ajId =0 -My activity Journal
  //ajId=1 sales Team and show all
  //order=ASC by default for show all
  //show all =1 for show all for rest its 0
  return request(
    `${endpoints.DASHBOARD_AJ_CARD}?fromDate=${fromDate}&toDate=${toDate}&myActivityJournal=${ajId}&order=${order}&showAll=${showAll}`,
    GET_METHOD
  );
};

export const delDashboardAJ = (id) =>
  request(`${endpoints.DASHBOARD_AJ_CARD}/${id}`, PUT_METHOD);

export const getDashboardEventCardDetails = (startDateTime, endDateTime) => {
  // Start Date: Client Todays Date Time
  // End Date: Client Today Date+2 Time
  return request(
    `${endpoints.DASHBOARD_EVENTS_CARD}?startDate=${startDateTime}&endDate=${endDateTime}`,
    GET_METHOD
  );
};

export const getDashboardOpprCardDetails = (fromDate, toDate, opType = 0) => {
  // From Date: Client Todays Date
  // To Date: Client Today Date+2
  // opType=1 for sales Team, 0 for my opp
  return request(
    `${endpoints.DASHBOARD_OPPR_CARD}?fromDate=${fromDate}&toDate=${toDate}&myOpportunities=${opType}`,
    GET_METHOD
  );
};

export const getDashboardQuotesCardDetails = (fromDate, toDate, myQt = 0) => {
  // From Date: Client Todays Date
  // To Date: Client Today Date+2
  //myQt =0 -My Quotes
  //myQt=1 sales Team and
  return request(
    `${endpoints.DASHBOARD_QUOTES_CARD}?fromDate=${fromDate}&toDate=${toDate}&myQuotes=${myQt}`,
    GET_METHOD
  );
};


export const getDashboardSamplesCardDetails = (fromDate, toDate, mySamples = 0) => {
 // From Date: Client Today's Date
  // To Date: Client Today's Date + 2
  // mySamples = 0 - My Samples
  // mySamples = 1 - Sales Team Samples
  return request(
    `${endpoints.DASHBOARD_SAMPLE_CARD}?fromDate=${fromDate}&toDate=${toDate}&mySamples=${mySamples}`,
    GET_METHOD
  );
};




export const getDashboardMsgCardDetails = () => {
  return request(endpoints.DASHBOARD_MSG_CARD, GET_METHOD);
};

/**
 * Dashboard:Message To-do
 * @param {obj} param
 */
export const postDashboardMsgTodo = (param) =>
  request(endpoints.DASHBOARD_MSG_TODO, POST_METHOD, param);

export const postDasboardMsgReply = (param) =>
  request(endpoints.DASHBOARD_MSG_RLY, POST_METHOD, param);

export const putDashboardMsgMarkAsRead = (id) =>
  request(`${endpoints.DASHBOARD_MSG_CARD}/${id}`, PUT_METHOD);

export const getDasboardPOCardDetails = (fromDate, toDate) => {
  // From Date: Client Todays Date
  // To Date: Client Today Date+2
  return request(
    `${endpoints.DASHBOARD_PO_CARD}?fromDate=${fromDate}&toDate=${toDate}`,
    GET_METHOD
  );
};

export const getDasboardJobsCardDetails = (fromDate, toDate) => {
  // From Date: Client Todays Date
  // To Date: Client Today Date+2
  return request(
    `${endpoints.DASHBOARD_JOBS_CARD}?fromDate=${fromDate}&toDate=${toDate}`,
    GET_METHOD
  );
};

export const defaultUserReports = () => {
  return request(endpoints.DEFAULT_USER_REPORTS, POST_METHOD);
};

export const getUserReports = () => {
  return request(endpoints.USER_REPORTS, GET_METHOD);
};

export const postUserReports = (param) => {
  return request(endpoints.USER_REPORTS, POST_METHOD, param);
};

export const deleteUserReports = (id) => {
  return request(`${endpoints.USER_REPORTS}/${id}`, DELETE_METHOD);
};

export const postUserReportParams = (param) => {
  return request(endpoints.USER_REPORT_PARAMS, POST_METHOD, param);
};

export const putUserReportParams = (id) => {
  return request(`${endpoints.USER_REPORT_PARAMS}/${id}`, PUT_METHOD);
};

export const getRoles = () => {
  return request(endpoints.ROLES, GET_METHOD);
};

export const getAllUsers = () => request(endpoints.USERS, GET_METHOD);

export const getAllCustomLabels = () =>
  request(endpoints.CUSTOM_LABLES, GET_METHOD);

export const divisionReportDashboard = (param) =>
  request(endpoints.DIVISION_REPORT, POST_METHOD, param);

export const divisionPrincipals = () =>
  request(`${endpoints.DIVISION_PRINCI_CUST}/1`, GET_METHOD);

export const divisionCustomers = () =>
  request(`${endpoints.DIVISION_PRINCI_CUST}/2`, GET_METHOD);

export const divisionRegions = () =>
  request(`${endpoints.DIVISION_REGION}`, GET_METHOD);

export const divisionUsers = () =>
  request(`${endpoints.DIVISION_USER}`, GET_METHOD);

export const divisionReProcessPrincipal = () =>
  request(`${endpoints.DIV_PRINCIPAL}`, GET_METHOD);

export const divisionReProcessCustomer = () =>
  request(`${endpoints.DIV_CUSTOMER}`, GET_METHOD);

export const divisionReProcessDivision = () =>
  request(`${endpoints.DIV_DIVISION}`, GET_METHOD);

export const divisionReprocess = (param) =>
  request(endpoints.DIVISION_REPROCESS, POST_METHOD, param);

export const getDashboardRptSalesByMth = (year, salesTeam) => {
  let saleTeam = salesTeam;
  if (!saleTeam) {
    saleTeam = 0;
  }
  return request(
    `${endpoints.DASHBOARD_RPT_SALES_BY_MTH}?year=${year}&saleTeam=${saleTeam}`,
    GET_METHOD
  );
};

export const getSalesTeam = () =>
  request(`${endpoints.DASHBOARD_SALES_TEAM}`, GET_METHOD);

export const getMenuEmailError = (date) =>
  request(`${endpoints.MENU_EMAIL_ERROR}/${date}`, GET_METHOD);

export const changeUserPassword = (param) =>
  request(endpoints.CHANGE_PASSWORD, POST_METHOD, param);

export const putUpdateUserLastSid = (userId, param) =>
  request(`${endpoints.UPDATE_USER_LAST_SID}/${userId}`, PUT_METHOD, param);

export const getParamConfigRate = () =>
  request(`${endpoints.PARAM_CONFIG_RATE}`, GET_METHOD);

export const getUserReportFilter = (userId, rptId) =>
  request(
    `${endpoints.DASHBOARD_USER_RPT_FLT}?rptUserId=${userId}&rptId=${rptId}`,
    GET_METHOD
  );

export const postUserReportFilter = (param) =>
  request(endpoints.DASHBOARD_USER_RPT_FLT, POST_METHOD, param);

export const getDeligationUsers = (date) =>
  request(`${endpoints.DELIGATION_USERS}/${date}`, GET_METHOD);

export const getSuperUserLoginList = () =>
  request(endpoints.SUPER_USER_LOGIN_LIST, GET_METHOD);

export const postLoginAsSuperUser = async (userId) => {
  axios.get(`/RepfabricCRM/SessionService.xhtml?extUserId=${userId}`);
};

export const lmsSSO = (subTag) =>
  request(`${endpoints.LMSSSO}?subTag=${subTag}`, GET_METHOD);

export const getVersion = () => request(endpoints.VERSION_NO, GET_METHOD);

export const getDashboardGoals = (goalPeriod, goalOwner) =>
  request(
    `${endpoints.DASHBOARD_GOALS}?goalPeriod=${goalPeriod}&goalOwner=${goalOwner}`,
    GET_METHOD
  );

export const dashboardGoalsRefresh = (goalPeriod, goalOwner, date) =>
  request(
    `${endpoints.DASHBOARD_GOALS_REFRESH}?goalPeriod=${goalPeriod}&goalOwner=${goalOwner}&currDate=${date}`,
    GET_METHOD
  );

export const getRegistrations = (param) =>
  request(`${endpoints.REGISTRATION}?${param}`, GET_METHOD);

export const updateRegitsrations = (param) =>
  request(endpoints.REGISTRATION_UPDATE, POST_METHOD, param);

export const deleteRegistrations = (param) =>
  request(endpoints.REGISTRATION_DELETE, POST_METHOD, param);

export const getUserSubMenu = () =>
  request(endpoints.USER_SUB_MENU, GET_METHOD);

export const getExpenseReceipt = (id) => {
  return request(`${endpoints.EXPENSE_VIEW_RECEIPTS}/${id}`, GET_METHOD);
};

export const delExpenseReceipt = (id) =>
  request(`${endpoints.EXPENSE_RECEIPT_DELETE}/${id}`, DELETE_METHOD);

export const getXreportsUserAuth = () => {
  return request(endpoints.XREPORTS_USER_AUTH, GET_METHOD);
};

export const getXreportsURL = () => request(endpoints.XREPORTS_URL, GET_METHOD);

export const getQuoteList = ({
  fromDate,
  toDate,
  principals,
  customers,
  parentCompany,
  region,
  custCompType,
  quoteStatus,
  groupByPrinci,
  showOnlyParentQuotes,
}) =>
  request(
    `${endpoints.QUOTE_LIST}?fromDate=${fromDate}&toDate=${toDate}&principals=${principals}&customers=${customers}&parentCompany=${parentCompany}&region=${region}&custCompType=${custCompType}&quoteStatus=${quoteStatus}&groupByPrinci=${groupByPrinci}&showOnlyParentQuotes=${showOnlyParentQuotes}`,
    GET_METHOD
  );

export const principalCustomer = () => {
  return request(endpoints.PRINCIPAL_CUSTOMER, GET_METHOD);
};

export const companyType = () => {
  return request(endpoints.COMPANY_TYPE, GET_METHOD);
};

export const quoteStatusList = () => {
  return request(endpoints.QUOTE_STATUS, GET_METHOD);
};

export const getFooterImagePath = () =>
  request(endpoints.FOOTER_LOGO_PATH, GET_METHOD);

/* Medical module API List */

export const getMedicalReport = () => {
  return medicalRequest(endpoints.MEDICAL_REPORT, GET_METHOD);
};

export const getMedicalDetail = (id) => {
  return medicalRequest(`${endpoints.MEDICAL_DETAIL}/${id}`, GET_METHOD);
};

export const getMedicalLineItem = (id) => {
  return medicalRequest(`${endpoints.MEDICAL_LINE_ITEM}/${id}`, GET_METHOD);
};

export const postMedicalReport = (param) => {
  return medicalRequest(endpoints.MEDICAL_REPORT, POST_METHOD, param);
};

export const postMedicalDetail = (param) => {
  return medicalRequest(endpoints.MEDICAL_DETAIL, POST_METHOD, param);
};

export const postMedicalLineItem = (param) => {
  return medicalRequest(endpoints.MEDICAL_LINE_ITEM, POST_METHOD, param);
};

export const updateMedicalReport = (param) => {
  return medicalRequest(
    `${endpoints.MEDICAL_REPORT}/${param.medicalId}`,
    PATCH_METHOD,
    param
  );
};

export const updateMedicalDetail = (param) => {
  return medicalRequest(
    `${endpoints.MEDICAL_DETAIL}/${param.recordId}`,
    PATCH_METHOD,
    param
  );
};

export const updateMedicalLineItem = (param) => {
  return medicalRequest(
    `${endpoints.MEDICAL_LINE_ITEM}/${param.recordId}`,
    PATCH_METHOD,
    param
  );
};

export const deleteMedicalReport = (id) => {
  return medicalRequest(`${endpoints.MEDICAL_REPORT}/${id}`, DELETE_METHOD);
};

export const deleteMedicalDetail = (id) => {
  return medicalRequest(`${endpoints.MEDICAL_DETAIL}/${id}`, DELETE_METHOD);
};

export const deleteMedicalLineItem = (id) => {
  return medicalRequest(`${endpoints.MEDICAL_LINE_ITEM}/${id}`, DELETE_METHOD);
};

export const getStage = () => {
  return request(endpoints.MED_STAGE, GET_METHOD);
};

export const getSalesPersons = () => {
  return request(endpoints.MED_SALES_PERSONS, GET_METHOD);
};

export const getPersonCompanies = () => {
  return request(endpoints.MED_PERSON_COMPANIES, GET_METHOD);
};

export const getFacilities = () => {
  return request(endpoints.MED_FACILITIES, GET_METHOD);
};

export const getMedLinePartNo = (princiId) =>
  request(`${endpoints.MED_LINE_PARTNO}?princiId=${princiId}`, GET_METHOD);

export const getMedCustomerPrice = ({
  princiId,
  partNumber,
  customerId,
  distributorId,
  qty,
}) =>
  request(
    `${endpoints.MED_LINE_CUSTOMER_PRICE}?princiId=${princiId}&partNumber=${partNumber}&customerId=${customerId}&distributorId=${distributorId}&qty=${qty}`,
    GET_METHOD
  );

export const getPrimaryContact = (personCompany, distributor) =>
  request(
    `${endpoints.PRIMARY_CONTACT}?personCompany=${personCompany}&distributor=${distributor}`,
    GET_METHOD
  );

export const postMedicalContact = (param) =>
  medicalRequest(endpoints.MEDICAL_CONTACT, POST_METHOD, param);

export const getMedicalContact = (medicalDetailId) =>
  medicalRequest(`${endpoints.MEDICAL_CONTACT}/${medicalDetailId}`, GET_METHOD);

export const getYoxelToken = () => request(endpoints.YOXEL_TOKEN, GET_METHOD);

export const getNewQuoteNumber = (
  customerId,
  principalId,
  distributorId,
  medOwner,
  username
) =>
  yoxelRequest(
    `${endpoints.NEW_QUOTE_NUMBER}?customer-id=${customerId}&principal-id=${principalId}&distributor-id=${distributorId}&medOwner=${medOwner}`,
    GET_METHOD,
    {},
    {},
    username
  );

export const postQuote = (param, username) =>
  yoxelRequest(endpoints.QUOTE, POST_METHOD, param, {}, username);

export const updateQuote = (param, username) =>
  yoxelRequest(
    `${endpoints.QUOTE}/${param['quote-number']}`,
    PUT_METHOD,
    param,
    {},
    username
  );

export const postQuoteLineItem = (quoteId, param, username) =>
  yoxelRequest(
    `${endpoints.QUOTE}/${quoteId}/line_items`,
    POST_METHOD,
    param,
    {},
    username
  );

export const updateQuoteLineItem = (quoteId, param, username) =>
  yoxelRequest(
    `${endpoints.QUOTE}/${quoteId}/line_items/${param['id']}`,
    PUT_METHOD,
    param,
    {},
    username
  );

export const deleteQuoteLineItem = (quoteId, lineItemId, medOwner, username) =>
  yoxelRequest(
    `${endpoints.QUOTE}/${quoteId}/line_items/${lineItemId}/medOwner=${medOwner}`,
    DELETE_METHOD,
    {},
    {},
    username
  );

export const getQuoteUOM = (type = 'QUOT_UOM') =>
  request(`${endpoints.QUOTE_UOM}/${type}`, GET_METHOD);

export const postMedSignFile = (param) =>
  medicalRequest(endpoints.MED_SIGN_UPLOAD, POST_METHOD, param);

export const getMedSignURL = (imagekey) =>
  medicalRequest(
    `${endpoints.MED_SIGN_UPLOAD}?imageKey=${imagekey}`,
    GET_METHOD
  );

export const getCompanyContact = (customerId, principalId) =>
  request(
    `${endpoints.COMPANY_CONTACT}?compId=${customerId},${principalId}`,
    GET_METHOD
  );

export const sendMail = (param) =>
  medicalRequest(endpoints.MED_EMAIL, POST_METHOD, param);

export const postMedPOSend = (param, username) =>
  yoxelRequest(endpoints.MED_PO, POST_METHOD, param, {}, username);

export const getMedPOSend = (poId, medOwner, username) =>
  yoxelRequest(
    `${endpoints.MED_PO}/${poId}/medOwner=${medOwner}`,
    GET_METHOD,
    {},
    {},
    username
  );

export const putMedPOSend = (id, param, username) =>
  yoxelRequest(`${endpoints.MED_PO}/${id}`, PUT_METHOD, param, {}, username);

export const postPOLineItem = (poID, param, username) =>
  yoxelRequest(
    `${endpoints.MED_PO}/${poID}/line_items`,
    POST_METHOD,
    param,
    {},
    username
  );

export const updatePOLineItem = (poID, param, username) =>
  yoxelRequest(
    `${endpoints.MED_PO}/${poID}/line_items/${param['id']}`,
    PUT_METHOD,
    param,
    {},
    username
  );

export const deletePOLineItem = (poID, lineItemId, medOwner, username) =>
  yoxelRequest(
    `${endpoints.MED_PO}/${poID}/line_items/${lineItemId}/medOwner=${medOwner}`,
    DELETE_METHOD,
    {},
    {},
    username
  );

export const linkPOQuote = (param, username) =>
  yoxelRequest(
    `${endpoints.MED_PO}/${param['po-id']}/quotes`,
    POST_METHOD,
    param,
    {},
    username
  );

export const postMedicalCalendar = (param) =>
  request(endpoints.MED_CALENDAR, POST_METHOD, param);

export const updateMedicalCalendar = (param) =>
  request(`${endpoints.MED_CALENDAR}/${param.id}`, PUT_METHOD, param);

export const deleteMedicalCalendar = (id) =>
  request(`${endpoints.MED_CALENDAR}/${id}`, DELETE_METHOD);

export const getValidateCalendar = (param) =>
  request(
    `${endpoints.MED_CALENDAR}?id=${param.medicalOwner}&fromDate=${param.medicalScheduled}&toDate=${param.medicalScheduledTo}`,
    GET_METHOD
  );

export const getMedicalPrinciCommRate = (princiId) =>
  request(`${endpoints.MED_PRINCI_COMM_RATE}/${princiId}`, GET_METHOD);

export const getMedMenuEnabled = () =>
  request(endpoints.MED_MENU_ENABLED, GET_METHOD);

export const getExpTaxVisibility = () =>
  request(endpoints.EXP_FEED_TOTAL, GET_METHOD);

export const getQuoteDefaultTemplate = () =>
  request(endpoints.QUOTE_DEFAULT_TEMPLATE, GET_METHOD);

export const postQuoteDefaultTemplate = (param) =>
  request(endpoints.QUOTE_DEFAULT_TEMPLATE, POST_METHOD, param);

export const updateQuoteDefaultTemplate = (param) =>
  request(`${endpoints.QUOTE_DEFAULT_TEMPLATE}/${param.id}`, PUT_METHOD, param);

export const deleteQuoteDefaultTemplate = (id) =>
  request(`${endpoints.QUOTE_DEFAULT_TEMPLATE}/${id}`, DELETE_METHOD);

export const getSyncProcess = () => request(endpoints.SYNC_PROCESS, GET_METHOD);

export const postEnableSyncTable = (param) =>
  request(endpoints.SYNC_ENABLE_TABLE, POST_METHOD, param);

export const getSyncCrornJobStatus = () =>
  request(endpoints.SYNC_CRON_JOB_STATUS, GET_METHOD);

export const postEnableSyncCronJob = (status) =>
  request(endpoints.SYNC_ENABLE_CRON_JOB, POST_METHOD, {
    status,
  });

export const clearProcessTable = () =>
  request(endpoints.SYNC_CLEAR_PROCESS, POST_METHOD);

export const clearSyncErrors = () =>
  request(endpoints.SYNC_ERRORS, POST_METHOD);
export const retryUnprocessed = () =>
  request(endpoints.SYNC_RETRY_UNPROCESS, POST_METHOD);
