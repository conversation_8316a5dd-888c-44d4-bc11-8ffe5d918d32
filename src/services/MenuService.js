import axiosDefaults from './axiosDefaults';

export class MenuService {
  /**
   * method to get all menu details
   */
  async getAllMenus() {
    return axiosDefaults.get(`user-menu`).then((res) => res);
  }

  /**
   * Function to update menu details
   * Output:
   * 1. Success message
   * 2. Error message
   * @param {JSON} menuDetails
   * @returns
   */
  updateMenuDetails(menuDetails) {
    return axiosDefaults
      .post(`user-flat-menu-update`, menuDetails)
      .then((res) => res.data)
      .catch((error) => error.message);
  }
}
