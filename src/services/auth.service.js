import axiosDefaults from './axiosDefaults';
export class AuthService {
  /**
   * function to login
   * On success, it will return the userNames
   * @param {string} loginId
   * @param {string} password
   * @returns
   */
  login(loginId, password) {
    return axiosDefaults
      .post('login', {
        loginId,
        password,
      })
      .then((result) => result.data.response);
  }
  /**
   * function to logout
   *
   */
  logout() {
    return axiosDefaults
      .post('logout')
      .then((result) => result.data)
      .catch((e) => e.message);
  }
  /**
   * function to register
   * On success, it will return the userNames
   * @param {string} loginId
   * @param {email} email
   * @param {string} password
   * @returns
   */
  register(loginId, email, password) {
    return axios.post(`${API_URL}signup`, {
      loginId,
      email,
      password,
    });
  }
  /**
   * function to check if user is authenticated
   * @returns {boolean}
   */
  checkAuth() {
    return axiosDefaults.get('checkAuth').then((result) => result.data);
  }
}
