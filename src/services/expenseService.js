import axiosDefaults from './axiosDefaults';

export class ExpenseService {
  async getExpenseList(createType, id, fromDate, toDate) {
    return axiosDefaults.get(
      `expense?createType=${createType}&expReportId=${id}&fromDate=${fromDate}&toDate=${toDate}`
    );
  }

  /**
   * method to get all User List
   */
  async getAllUserList() {
    return axiosDefaults.get(`user-list`);
  }
  /**
   * method to get Expense categories
   * @returns
   */
  async getExpenseCategory() {
    return axiosDefaults.get(`expense-category`);
  }

  async getMerchants() {
    return axiosDefaults.get(`merchants`);
  }

  async getDropdowns(type) {
    return axiosDefaults.get(`dropdowns?type=${type}`);
  }

  async getActivityJournal(date, createdForId, expId) {
    return axiosDefaults.get(
      `activity-journal?date=${date}&createdForId=${createdForId}&expId=${expId}`
    );
  }

  async getActivityJournalDetails(id) {
    return axiosDefaults.get(`activity-journal-details/${id}`);
  }

  async uploadImage(Id, image) {
    return axiosDefaults.put(`expense-receipt/${Id}`, image);
  }

  /**
   * method to create Expense
   * @param {object} expense
   * @returns
   */
  async createExpense(expense) {
    return axiosDefaults.post(`expense`, expense);
  }
  /**
   * method to create Expense Reports
   * @param {obj} expenseReport
   * @returns
   */
  async createExpenseReport(expenseReport) {
    return axiosDefaults.post(`expense-report`, expenseReport);
  }
  async getExpenseReport(type, status, fromDate, toDate) {
    return axiosDefaults.get(
      `expense-report?createType=${type}&status=${status}&fromDate=${fromDate}&toDate=${toDate}`
    );
  }

  async getExpReportById(Id) {
    return axiosDefaults.get(`expense-report/${Id}`);
  }

  async saveExpenseDetails(expenseDetails) {
    return axiosDefaults.post(`/expense`, expenseDetails);
  }
  async updateExpenseReport(expenseReport) {
    return axiosDefaults.post(`update-expense-report`, expenseReport);
  }
  async deleteExpenseReport(expenseReportId) {
    return axiosDefaults.delete(`expense-report/${expenseReportId}`);
  }
  async updateExpenseDetails(expenseDetails) {
    return axiosDefaults.post(`/update-expense`, expenseDetails);
  }

  async deleteExpenseDetails(expenseDetails) {
    return axiosDefaults.post(`/delete-expense`, expenseDetails);
  }

  async linkExpenseDetails(expenseDetails) {
    return axiosDefaults.post(`/link-expense`, expenseDetails);
  }

  async linkActivityJournal(params) {
    return axiosDefaults.post(`/link-activity-journal`, params);
  }

  async addApprover(params) {
    return axiosDefaults.post(`/add-approver`, params);
  }

  async createUpdateFilter(filter) {
    return axiosDefaults.post(`/general-filter`, filter);
  }
  async getFilter(name) {
    return axiosDefaults.get(`/general-filter?filterName=${name}`);
  }

  async getImage(id) {
    return axiosDefaults.get(`expense-receipt/${id}`, {
      responseType: 'arraybuffer',
    });
  }
  async updateImage(id) {
    return axiosDefaults.put(`update-expense-receipt/${id}`);
  }
  async getAttendees(expId) {
    return axiosDefaults.get(`attendees/${expId}`);
  }
  async submitApprove(expReportId) {
    return axiosDefaults.get(`submit-validation/${expReportId}`);
  }
  async approve(params) {
    return axiosDefaults.post(`approve`, params);
  }

  async expenseSummary(createType, createdFor) {
    return axiosDefaults.get(
      `summary?createType=${createType}&createdFor=${createdFor}`
    );
  }
  async expenseHistory(expReportId) {
    return axiosDefaults.get(`approval-history/${expReportId}`);
  }
  async postExpenseExport(params) {
    return axiosDefaults.post(`expense-report-pdf`, params, {
      responseType: 'arraybuffer',
    });
  }
  async attendeesCompany(attendees) {
    const params = attendees.length == 0 ? null : attendees;
    return axiosDefaults.get(`attendees-companies?attendees=${params}`);
  }
  async exportToExcel(params) {
    return axiosDefaults.get(
      `export-to-excel?fromDate=${params.fromDate}&toDate=${params.toDate}&category=${params.category}&userId=${params.user}&isUnFormatted=${params.isUnFormatted}`,
      {
        responseType: 'blob',
      }
    );
  }
  async linkExpenseReportValidation({ expReportId }) {
    return axiosDefaults.get(
      `link-expense-report-validation?expReportId=${expReportId}`
    );
  }
  async linkExpenseValidation({ expIds }) {
    return axiosDefaults.post(`link-expense-receipt-validation`, {
      expIds,
    });
  }
}
