import { useRouter } from 'next/router';
import NextNProgress from 'nextjs-progressbar';

import Head from 'next/head';
import PageHeader from '@components/page-header';
import Footer from '@components/footer';
import { Header } from '@components/header';
import { capitalizeFirstLetter } from 'src/utils';
import { useDefaults } from '@contexts/defaults';
import { PAGE_HEADER_TITLE } from 'src/constants';
const PageHeaderFooterLayout = ({ children }) => {
  const { pathname } = useRouter();
  const {
    defaults: { NAME, BRAND_TYPE },
  } = useDefaults();
  const { fabricClientName: TITLE_NAME } = BRAND_TYPE;
  const header =
    pathname === '/' ? NAME : PAGE_HEADER_TITLE[pathname.split('/')[1]];
  const pageTitle =
    pathname === '/'
      ? `${TITLE_NAME || ''} CRM`
      : capitalizeFirstLetter(header ?? '');
  const isBrandLogoReq = pathname === '/';
  return (
    <>
      <NextNProgress
        color="#F55353"
        startPosition={0.3}
        stopDelayMs={200}
        options={{ showSpinner: false }}
      />
      <Head>
        <title>{pageTitle}</title>
      </Head>
      <Header />

      <PageHeader
        header={pathname != '/x-reports' && header}
        isBrandLogoReq={isBrandLogoReq}
      />

      <div style={{ minHeight: '85vh' }}>{children}</div>
      <Footer />
    </>
  );
};

export default PageHeaderFooterLayout;
