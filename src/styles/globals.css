@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro&display=swap');
html,
body {
  font-family: 'Source Sans Pro', sans-serif !important;
  font-size: 14px;
  background-color: #edf0f4 !important;
  font-weight: normal;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
.p-menubar {
  padding: 0 0.5rem !important;
}
.card_box {
  background-color: var(--blue-100);
  padding: 1rem !important;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.05),
    0px 1px 4px rgba(0, 0, 0, 0.08) !important;
}
.card_box.card-w-title {
  padding-bottom: 2rem;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0.5rem 0 1rem;
  font-family: inherit;
  font-weight: 500;
  line-height: 1.2;
  color: inherit;
}

.card.header {
  position: fixed;
  width: 100%;
  z-index: 999;
  margin-bottom: 20px;
}
.grid {
  width: 100%;
}
.p-menu {
  border: none !important;
}
.p-component {
  font-size: 0.9rem !important;
}
.datatable-rowexpansion-demo .product-image {
  width: 100px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.datatable-rowexpansion-demo .orders-subtable {
  padding: 0.5rem;
  background: #9baebf;
}
.p-multiselect-label {
  padding: 0.25rem !important;
}
.p-sidebar-mask {
  top: 50px !important;
}
.p-sidebar .p-sidebar-header {
  display: none;
}
.p-sidebar .p-sidebar-content {
  padding: 1rem !important;
}
.p-button.p-button-lg {
  margin: 0 0.5rem;
}

.p-panel .p-panel-content {
  padding: 0.5rem !important;
}
.p-panel .p-panel-title {
  font-size: 1.3rem !important;
}
.p-card-title {
  font-size: 1.2rem !important;
}

.p-tabview-ink-bar {
  z-index: 1 !important;
  display: block !important;
  position: absolute !important;
  bottom: 0 !important;
  height: 2px !important;
}
.p-datatable-header {
  padding-top: 0 !important;
  border-bottom: 1px solid black !important;
}
.export-buttons .p-button.p-button-sm {
  font-size: 0.75rem !important;
  padding: 0.5rem 0.6rem !important;
}
.p-column-filter-element .p-inputtext {
  padding: 0.2rem !important;
}
.p-paginator .p-dropdown {
  height: 1.6rem !important;
}

.p-paginator .p-dropdown .p-dropdown-label {
  padding: 0.2rem;
  font-size: 0.9rem;
}
.menu_list .p-menuitem-link .p-menuitem-text {
  color: white !important;
}
.menu_list .p-menuitem-link:hover > .p-menuitem-text {
  color: black !important;
}

.p-datatable .p-sortable-column.p-highlight {
  background-color: var(--text-white) !important;
  border: none !important;
}
.p-multiselect-trigger-icon {
  color: var(--primary-color-text) !important;
}
.p-multiselect .p-multiselect-label.p-placeholder {
  color: var(--primary-color-text) !important;
}
.p-multiselect-panel .p-multiselect-items .p-multiselect-item {
  padding: 0.25rem !important;
}
/** Sample Demo CSS**/
.autocomplete_demo .p-dropdown-label {
  padding: 0.25rem !important;
}
.automplete_demo_panel input {
  padding: 0.25rem !important;
}
.automplete_demo_panel .p-dropdown-items .p-dropdown-item {
  padding: 0rem !important;
}
/** Helper Dropdown CSS**/
.helper_dropdown .p-dropdown-label {
  padding: 0.25rem !important;
}

.grid .card_box {
  background-color: var(--blue-100);
  padding: 0;
  margin-bottom: 1rem;
  border-radius: 0;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.05),
    0px 1px 4px rgba(0, 0, 0, 0.08) !important;
}

.grid .p-panel .p-panel-header {
  border: none !important;
  border-bottom: 1px solid #a19f9d !important;
  padding: 1rem;
  background: #fff !important;
  color: #000 !important;

  text-align: left !important;
  justify-content: space-between !important;
}

.grid .p-panel .p-panel-title {
  font-size: 1rem !important;
}

.grid .p-panel .p-panel-content {
  padding: 2rem;
  border: none !important;
}

.p-menubar .p-button.p-button-lg .p-button-icon {
  font-size: 1.1rem !important;
  font-weight: 900;
}

.p-button-icon.p-c.pi.pi-sign-out {
  font-weight: 900;
}

.menu_list .p-menubar-root-list > li > a[aria-haspopup='false'] span,
.menu_list .p-menubar-root-list > li > a[aria-haspopup='true'] span {
  color: #fff !important;
}
.menu_list .p-menubar-root-list > li > a:hover span,
.menu_list .p-menubar-root-list > li > a:active span,
.menu_list .p-menubar-root-list > li > a:focus span {
  color: black !important;
}
/* Tablet */
@media screen and (max-width: 960px) {
  .p-menubar .p-menubar-button {
    color: #fff !important;
  }
  .menu_list .p-menubar-button:hover {
    color: #0078d4 !important;
  }
  .menu_list
    .p-menubar-root-list
    > .p-menuitem
    > .p-menuitem-link
    .p-menuitem-text {
    color: black !important;
  }
}

.p-datatable-thead tr th {
  background: #d9e5ef !important;
}

.order-badge {
  padding: 0.5rem;
  font-weight: 900;
  border-radius: 5px;
}

.order-badge.order-pending {
  background: #feedaf;
  color: #8a5340;
}

.order-badge.order-approved {
  background: #c8e6c9;
  color: #256029;
}

.order-badge.order-rejected {
  background: #ffcdd2;
  color: #c63737;
}

.tabview-demo .p-tabview p {
  line-height: 1.5;
  margin: 0;
}

.p-dialog-header,
.p-dialog-footer {
  background: #e1e1e1 !important;
  padding: 0.7rem 2rem !important;
}

.p-dialog .p-dialog-content {
  padding: 0 1.5rem 0rem 1.5rem !important;
}

.tabview-demo .tabview-header-icon span {
  margin-left: 0;
  margin-right: 0.4rem;
}

.field {
  margin-bottom: 0.5rem !important;
}

.p-inputtext:disabled {
  background-color: #f3f2f1 !important;
  border-color: #605e5c !important;
  color: #605e5c !important;
}

.p-multiselect {
  min-width: 15rem;
}

.multiselect-custom
  .p-multiselect-label:not(.p-placeholder):not(.p-multiselect-items-label) {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.multiselect-custom .country-item-value {
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  display: inline-flex;
  float: left;
  margin: 0.2rem;
  background-color: var(--primary-color);
  color: var(--primary-color-text);
}

.p-tabview.p-component {
  border: 1px solid #a19f9d !important;
}

.p-tabview .p-tabview-panels {
  padding: 0.5rem !important;
}

.p-tabview .p-tabview-nav li .p-tabview-nav-link {
  color: #555 !important;
  background: #cad0d5 !important;
  border-right: 1px solid !important;
  border-bottom: 1px solid !important;
  border-color: #a19f9d !important;
  padding: 0.6rem 1.5rem !important;
  margin: 0 !important;
}

.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  background: #ffffff !important;
  color: #0078d4 !important;
  border-top: 3px solid #0078d4 !important;
  border-bottom: none !important;
  margin: 0 !important;
}

.p-inputtext,
.p-inputtext:disabled,
.p-dropdown {
  border: 1px solid #bebbb8 !important;
}

.p-dropdown-label {
  border: none !important;
}

.p-checkbox .p-checkbox-box {
  width: 15px !important;
  height: 15px !important;
}

.p-datatable.p-datatable-sm .p-datatable-thead > tr > th,
.p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
  padding: 0.2rem 0.375rem !important;
}

.dx-button-has-icon .dx-button-content {
  padding: 5px !important;
  font-size: 12px !important;
  font-weight: 600;
}

.dx-button-has-text .dx-icon {
  font-size: 16px !important;
}

.btn-rounded {
  border-radius: 50%;
  height: 2.5rem;
}

.dx-popup-wrapper > .dx-overlay-content {
  z-index: 9999;
}

.dx-datagrid-headers .dx-header-row {
  background: #b0c4de !important;
  color: #000 !important;
  font-weight: 600;
}
#expanded-row .dx-datagrid .dx-datagrid-headers .dx-header-row,
.expanded-table .dx-datagrid .dx-datagrid-headers .dx-header-row {
  background: #c6e1f7 !important;
}

.dx-datagrid-group-opened,
.dx-datagrid-group-closed {
  font-size: 30px !important;
}

.dx-datagrid-rowsview.dx-empty {
  height: 50px !important;
}

.dx-popup-title {
  background: #e1e1e1 !important;
}
.dx-overlay-shader .dx-popup-content {
  padding: 5px 10px;
  padding-right: 0 !important;
}

.dx-link.dx-link-icon {
  color: #fff !important;
  padding: 0.2rem !important;
  margin: 0.1rem !important;
  font-size: 1.2rem !important;
  height: 2rem !important;
  width: auto !important;
  border-radius: 0.2rem !important;
}

.dx-link.dx-icon-link {
  background: #498205;
  border: 1px solid #498205;
}

.dx-link.dx-icon-edit {
  background: #00b7c3;
  border: 1px solid #00b7c3;
}

.dx-link.dx-icon-newfolder,
.dx-link.dx-icon-view-expense,
.dx-link.dx-icon-upload,
.dx-link.dx-icon-doc,
.dx-link.dx-icon-add {
  background: #0078d4;
  border: 1px solid #0078d4;
}

.dx-link.dx-icon-newfolder,
.aj-link-btn {
  content: url(/rfnextgen/images/BookAddOutlined.svg);
  padding: 0.2rem !important;
  height: 2.2rem;
}
.dx-link.dx-icon-view-expense {
  content: url(/rfnextgen/images/ViewExpense.svg);
  padding: 0.2rem !important;
  height: 2.2rem;
}

.dx-icon-zoom-in {
  content: url(/rfnextgen/images/zoom-in.svg);
  padding: 0.2rem !important;
  height: 2.2rem;
}

.dx-icon-zoom-out {
  content: url(/rfnextgen/images/zoom-out.svg);
  padding: 0.2rem !important;
  height: 2.2rem;
}

.dx-link.dx-icon-activefolder {
  background: #e5921e;
  border: 1px solid #e5921e;
  content: url(/rfnextgen/images/BookOutlined.svg);
}

.dx-link.dx-icon-trash,
.dx-link.dx-icon-close {
  background: #d13438;
  border: 1px solid #d13438;
}

.dx-link.dx-icon-todo {
  background: #498205;
  border: 1px solid #498205;
}
.approveBtn {
  background: #498205 !important;
  border: 1px solid #498205 !important;
}
.rejectBtn {
  background: #d13438 !important;
  border: 1px solid #d13438 !important;
}
.dx-link.dx-icon-pdffile {
  background: #d13438;
  border: 1px solid #d13438;
}

.p-dialog-footer.popup {
  bottom: 0;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 9;
}

.dx-overlay-wrapper {
  font-size: 12px !important;
}

.dx-popup-wrapper.dx-overlay-shader {
  background-color: rgba(0, 0, 0, 0.6) !important;
}

.p-autocomplete,
.p-multiselect,
.p-inputnumber,
.p-inputtext {
  width: 100%;
}

.p-datepicker,
.p-dropdown-panel,
.p-autocomplete-panel,
.p-multiselect-panel {
  z-index: 2222 !important;
}

.react-grid-layout {
  position: relative;
  transition: height 200ms ease;
}
.react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top;
  background: white;
  padding: 10px;
}
.react-grid-item.cssTransforms {
  transition-property: transform;
}
.react-grid-item.resizing {
  z-index: 1;
  will-change: width, height;
}

.react-grid-item.react-draggable-dragging {
  transition: none;
  z-index: 3;
  will-change: transform;
}

.react-grid-item.react-grid-placeholder {
  background: rgb(104, 160, 235);
  opacity: 0.2;
  transition-duration: 100ms;
  z-index: 2;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  cursor: se-resize;
}

.react-grid-item > .react-resizable-handle::after {
  content: '';
  position: absolute;
  right: 3px;
  bottom: 3px;
  width: 5px;
  height: 5px;
  border-right: 2px solid rgba(0, 0, 0, 0.4);
  border-bottom: 2px solid rgba(0, 0, 0, 0.4);
}
.react-grid-item > .react-resizable-handle.react-resizable-handle-s {
  bottom: 0;
  transform: rotate(45deg);
}
.react-grid-item > .react-resizable-handle.react-resizable-handle-n,
.react-grid-item > .react-resizable-handle.react-resizable-handle-s {
  left: 50%;
  margin-left: -10px;
  cursor: ns-resize;
}
.dx-scrollbar-horizontal {
  top: -2px !important;
}
.dx-scrollbar-horizontal.dx-scrollbar-hoverable.dx-scrollable-scrollbar-active,
.dx-scrollbar-horizontal.dx-scrollbar-hoverable.dx-scrollable-scrollbar-active
  .dx-scrollable-scroll,
.dx-scrollbar-horizontal.dx-scrollbar-hoverable.dx-state-hover,
.dx-scrollbar-horizontal.dx-scrollbar-hoverable.dx-state-hover
  .dx-scrollable-scroll {
  height: 10px !important;
}

#overlay_tmenu {
  top: 46px !important;
  padding: 1px;
  line-height: 16px;
}

#overlay_tmenu .p-menuitem-link .p-menuitem-text {
  color: black !important;
}
#overlay_upanel {
  top: 36.5521px !important;
}

.p-component.p-calendar {
  display: flex;
}
.dataGrid-cell:hover {
  text-overflow: clip;
  white-space: normal;
  word-break: break-all;
}

/**These CSS are for Datepicker navigation consistancy*/
.p-datepicker .p-datepicker-prev {
  order: 0 !important;
}

.p-datepicker .p-datepicker-prev .p-datepicker-prev-icon:before {
  content: '\e900' !important;
}
.p-datepicker .p-datepicker-next .p-datepicker-next-icon:before {
  content: '\e901' !important;
}

.dx-datagrid-content
  .dx-datagrid-table
  .dx-row
  .dx-command-edit.dx-command-edit-with-icons {
  width: 100% !important;
  display: flex;
  align-items: center;
  justify-content: center;
}