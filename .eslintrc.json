{
  "extends": "next/core-web-vitals",

  "rules": {
    "no-unused-vars": "error",
    "no-console": "error",
    "no-duplicate-imports": "error",
    "camelcase": "error",
    "prefer-const": "error",
    "prefer-destructuring": [
      "error",
      {
        "VariableDeclarator": {
          "array": false,
          "object": true
        },
        "AssignmentExpression": {
          "array": true,
          "object": true
        }
      },
      {
        "enforceForRenamedProperties": false
      }
    ],
    "prefer-rest-params": "error",
    "prefer-spread": "error",
    "prefer-template": "error",
    "no-param-reassign": "error",
    "no-unused-expressions": "error",
    "no-use-before-define": "error",
    "no-useless-call": "error",
    "no-useless-concat": "error",
    "no-useless-escape": "error",
    "no-useless-return": "error",
    "no-var": "error",
    "prefer-arrow-callback": "error",
    "prefer-numeric-literals": "error",
    "eqeqeq": "error",
    // Other rules
    "@next/next/no-img-element": "off",
    "react-hooks/exhaustive-deps": "off"
  }
}
