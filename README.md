# rf-nextgen-frontend

## 🛠️ Steps to Run the rf-nextgen-frontend Project

### 1. Clone the repository

```bash
git clone <repo-url>
cd rf-nextgen-frontend
```

### 2. Install dependencies

Run the following command to install all required packages:

```bash
npm install
```

### 3. Set up environment variables

Create the following environment files in the root of the project (you can copy from .env.example):

- `.env`
- `.env.local`

### 4. Start the development server

Run the project using:

```bash
npm run dev
```

### 5. Set Required Cookie for Access

Once the application loads in the browser (<http://localhost:3000> or similar), manually add the `JSESSIONID` cookie in your browser's developer tools. This cookie is required for authentication and UI access.

- Open DevTools → Application → Cookies → localhost
- Click "Add", and set:
  - Name: JSESSIONID
  - Value: (your valid session token)
- Refresh the page after setting the cookie.

## ✅ Environment Configuration Details for rf-nextgen-frontend

The project uses the following environment files for different setups:

### 📄 .env.example

This is a template for environment variables. It contains the variable names without values:

```env
NODE_ENV= 
NEXT_PUBLIC_BASE_URL= 
NEXT_PUBLIC_MEDICAL_URL=
NEXT_PUBLIC_YOXEL_URL=
NEXT_PUBLIC_BASE_PATH= 
```

Use this as a reference to create your own .env, .env.local, or other environment-specific files.
