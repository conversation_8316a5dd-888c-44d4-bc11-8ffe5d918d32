{"name": "nextgen2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build && next export -o build", "start": "next start", "lint": "next lint", "prettier": "prettier --write .", "cypress": "cypress open", "prepare": "husky install", "pre-commit": "npm run build"}, "dependencies": {"axios": "^0.27.2", "chart.js": "^3.8.0", "classnames": "^2.3.2", "compressorjs": "^1.1.1", "dayjs": "^1.11.4", "devextreme": "22.1.5", "devextreme-react": "22.1.5", "html-react-parser": "^3.0.4", "jquery": "^3.6.3", "next": "12.1.5", "nextjs-progressbar": "^0.0.14", "nprogress": "^0.2.0", "primeflex": "^3.1.3", "primeicons": "^5.0.0", "primereact": "^8.7.3", "prop-types": "^15.8.1", "react": "18.0.0", "react-dom": "18.0.0", "react-grid-layout": "^1.3.4", "react-signature-canvas": "^1.0.6", "sfcookies": "^1.0.2"}, "devDependencies": {"@commitlint/cli": "^17.3.0", "@commitlint/config-conventional": "^17.3.0", "cypress": "^10.0.2", "eslint": "8.13.0", "husky": "^8.0.1", "prettier": "^2.6.2"}, "overrides": {"semver": "7.5.2"}}